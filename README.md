# 무안 공간정보플랫폼 구축 사업 Frontend(터보레포)

Turborepo 를 사용한 NextJS Monorepo 프로젝트

## 🧩 magp-turbo Subtree 구성 가이드

이 프로젝트는 **Turborepo 기반 모노레포**입니다.  
`packages/` 디렉토리는 별도의 Git 저장소로 관리되며, **Git Subtree 방식**으로 연동되어 있습니다.

---

## 📁 디렉토리 구조

```text
magp-turbo/ # 메인 저장소 (https://gitlab.geon.kr/geon-biz/magp/magp-turbo.git)
├── apps/ # apps 저장소 subtree
├── packages/ # packages 저장소 subtree (https://gitlab.geon.kr/geon-biz/magp/pakages.git)
├── turbo.json
├── pnpm-workspace.yaml
└── ...
```

---

## ✅ Git

### 서비스팀 메인 프로젝트: magp-turbo

```bash
# 메인 프로젝트 클론
git clone https://gitlab.geon.kr/geon-biz/magp/magp-turbo.git
cd magp-turbo
```

### 🚀 변경사항 반영 (Push)

**NOTE**: 개발 내용 반영은 `dev` 브랜치로 통일합니다.

``` bash
# apps 서비스 변경사항 push:
git push origin dev

# 공통 packages 변경사항 push:
git subtree push --prefix=packages https://gitlab.geon.kr/geon-biz/magp/pakages.git dev
```

### 🔄 공통 packages 변경사항 가져오기 (Pull)

```bash
# packages 최신 내용 가져오기 (dev)
git subtree pull --prefix=packages https://gitlab.geon.kr/geon-biz/magp/pakages.git dev
```

## 프로젝트 구성

### Apps and Packages

- `web`: 무안군 서비스 프론트 [Next.js](https://nextjs.org/) app
- `@geon/map`: 지도 관련 컴포넌트. 사용할 프레임워크 및 버전에 따라 import
- `@geon-ui/react`: UI 라이브러리. 사용할 프레임워크 및 버전에 따라 import
- `@config/eslint`: `eslint` 설정
- `@config/typescript`: 타입스크립트 설정 json 모음
- `@config/tailwind`: tailwindcss 설정
- 그 외 추가 예정

### Developer Experience

- [PNPM](https://pnpm.io)
- [Turborepo](https://turborepo.com/)
- [TypeScript](https://www.typescriptlang.org/)
- Code Formatting
  - [ESLint](https://eslint.org/)
  - [Prettier](https://prettier.io)
- Git Hooks
  - [Husky](https://typicode.github.io/husky/get-started.html)
  - [Lint Staged](https://github.com/lint-staged/lint-staged#readme)

### 프로젝트 빌드

아래 커맨드를 통해 패키지 매니저를 `pnpm` 으로 설정합니다:

```bash
# pnpm 이 없다면 설치 먼저
npm install -g pnpm
corepack use pnpm@latest
```

### Develop

To develop all apps and packages, run the following command:

```bash
pnpm dev
```

Bundle Analyzer (번들 사이즈 분석) 실행:

```bash
pnpm web analyze
```

## 프로젝트 Workspace 패키지

각 패키지 설명 문서 및 [패키지 추가 방법](./packages/README.md)

- [`@config/*`](./packages/config/README.md): 프로젝트 기본 환경 설정 파일 모음
- [`@geon/map/core`](./packages/geon-map/core/README.md): odf 기능을 공통 활용하기 위한 core pakage(미작성)
- [`@geon/map/react`](./packages/geon-map/react/README.md): react 컴포넌트 패키지
- [`@geon-ui/react`](./packages/geon-ui/react/README.md): react 컴포넌트 패키지

## 외부 npm 패키지 설치

모노레포에서의 npm 패키지 설치는 `pnpm --filter` 옵션이 필요합니다.  
모노레포 루트 패키지 설치는 `-w` 옵션으로 진행합니다.

```bash
# web 패키지에 devDependencies 로 typescript 설치:
pnpm add -D --filter "web" typescript
```

패키지 설치/제거가 자주 일어나는 workspace는 루트 `package.json`의 `scripts`에 등록하는 것을 추천합니다.

```json
{
  ...
  "scripts": {
    "web": "pnpm --filter web",
    ...
  },
  ...
}
```

## `turbo.json`

모든 workspace 에 대해서 `tasks` 에 명시된 scripts 를 병렬 실행합니다.

```json
{
  ...
  "tasks": {
    "check-types": {
      "dependsOn": ["^check-types"]
    },
  },
  ...
}
```
