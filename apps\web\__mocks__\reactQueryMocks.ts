import {
  useAppMutation as originalUseAppMutation,
  useAppQuery as originalUseAppQuery,
} from "@geon-query/react-query";

/**
 * ### [ useAppMutation이 반환하는 속성과 동일한 구조를 가진 Mock 객체 ]
 *
 * @description
 * **useAppMutation 내부에서 생성되는 실제 useMutation의 반환 속성을 Jest가 추적하고 테스트할 수 있도록,**<br>
 * **useAppMutation의 Mock이 이 객체를 반환하도록 구성**
 *
 * ---
 * @property {`unknown`} `data`  : 뮤테이션으로 데이터 요청 성공 시, 반환되는 데이터
 * @property {`null`}`error` : 뮤테이션으로 데이터 요청 실패 시, 반환되는 에러 객체
 * @property {`boolean`} `isPending` : 뮤테이션으로 데이터 요청 중인지 여부
 * @property {`boolean`} `isSuccess` :"  뮤테이션으로 데이터 요청 시, 성공했는지 여부
 * @property {`boolean`} `isError` : 뮤테이션으로 데이터 요청 시, 에러가 발생했는지 여부
 * @property {`boolean`} `isIdle` : 뮤테이션이으로 데이터 요청이 한번 도 수행되지 않았는지 여부
 * @property { `"idle" | "loading" | "error" | "success"` } `status` : loading, error, success, idle 중 하나의 상태를 나타내는 문자열
 * @property {`UseMutateFunction<unknown, unknown, unknown, unknown>`} `mutate` : 동기 방식으로 뮤테이션으로 데이터를 요청하는 방식
 * @property {`(variables: unknown, options?: MutateOptions<unknown, unknown, unknown, unknown> | undefined) => Promise<unknown>`} `mutateAsync` : 비동기 방식으로 뮤테이션으로 데이터를 요청하는 방식
 * @property {`() => void`} `reset` : 뮤테이션의 상태 초기화
 * @property {`number`} `failureCount` : 뮤테이션 요청이 실패한 횟수 (재시도 포함)
 * @property {`unknown`} `failureReason` : 마지막 뮤테이션 실패 시 반환된 에러 객체
 * @property {`boolean`} `isPaused` : 네트워크 상태나 설정에 의해 뮤테이션이 일시 정지되었는지 여부
 * @property {`number`} `submittedAt` : 마지막으로 뮤테이션이 호출된 시점의 타임스탬프 (Unix time)
 * @property {`any`} `variables` : 마지막으로 뮤테이션을 호출 호출 할때, 전달된 변수
 * @property {`unknown`} `context` :  실행 중 전달된 context ( => 낙관적 업데이트(결과를 화면에 먼저 반영하고 아니면 없앰.) 등
 *
 */
export const mockedUseAppMutation: ReturnType<typeof originalUseAppMutation> = {
  data: null,
  error: null,
  isPending: false,
  isSuccess: true,
  isError: false,
  isIdle: false,
  status: "success",
  mutate: jest.fn(),
  mutateAsync: jest.fn(),
  reset: jest.fn(),
  failureCount: 0,
  failureReason: null,
  isPaused: false,
  submittedAt: Date.now(),
  variables: undefined,
  context: undefined,
};

/**
 * ### [ useQuery 가 반환하는 속성과 동일한 구조를 가진 Mock 객체 ]
 *
 * @description
 * **useAppQuery 내부에서 생성되는 실제 useQuery 반환 속성을 Jest가 추적하고 테스트할 수 있도록,**<br>
 * **useAppQuery의 Mock이 이 객체를 반환하도록 구성**
 *
 * ---
 * @property {`unknown`} `data` :  쿼리로 데이터 요청(조회) 성공 시, 반환되는 데이터
 * @property {`null`} `error` : 쿼리로 데이터 요청(조회) 실패 시, 반환되는 에러 객체
 * @property {`boolean`} `isLoading` : 쿼리로 데이터 요청(조회) 중인지 여부
 * @property {`boolean`} `isInitialLoading` : 마운트 이후, 최초 로딩의 여부
 * @property {`boolean`} `isFetching` : 쿼리로 데이터를 다시 요청(조회) 중인지 여부
 * @property {`(options?: RefetchOptions | undefined) => Promise<QueryObserverResult<unknown, unknown>>`} `refetch` : 수동으로 데이터를 요청(조회)하는 메서드
 * @property {`boolean`} `isError` : 쿼리로 데이터 요청(조회) 시, 에러가 발생했는지 여부
 * @property {`boolean`} `isSuccess` : 쿼리로 데이터 요청(조회) 시, 성공했는지 여부
 * @property { `"idle" | "loading" | "error" | "success"` } `status` : loading, error, success, idle 중 하나의 상태를 나타내는 문자열
 * @property {`number`} `dataUpdateAt` : 마지막으로 data 가 업데이트 된 시간(Timestamp)
 * @property {`number`} `errorUpdateAt` : 마지막으로 error 가 업데이트 된 시간(Timestamp)
 * @property {`number`} `failureCount` : 쿼리로 데이터 요청(조회)을 실패한 횟수
 * @property {`boolean`} `isFetched` : 쿼리로 데이터 요청(조회)가 최소한 한 번은 실행됬는지 여부
 * @property {`boolean`} `isFetchedAfterMount` : 마운트 이후 쿼리로 데이터 요청이 실행됬는지 여부
 * @property {`boolean`} `isRefetching` : 수동 또는 자동으로 재요청 중인지애 대한 여부
 * @property {`boolean`} `isStale` : 데이터가 오래되어 다시 요청해야 하는지 여부
 * @property {`boolean`} `isPlaceholderData` : 현재 data 가 placeholderData 옵션에 의한 값인지에 대한 여부
 * @property {` "fetching" | "paused" | "idle" `} `fetchStatus` : "fetching" | "paused" | "idle" 중 하나의 상태를 나타내는 문자열
 * @property {`boolean`} `isPaused` : 쿼리의 데이터 요청이 일시 중지 상태인지에 대한 여부
 * @property {`boolean`} `isPending` : pending 상태인지 여부 (`status === "loading"` + `data === undefined`)
 * @property {`boolean`} `isLoadingError` : 로딩 중 에러가 발생했는지 여부
 * @property {`boolean`} `isRefetchError` : refetch 중 에러가 발생했는지 여부
 * @property {`unknown`} `failureReason` : 에러 정보
 * @property {`number`} `errorUpdateCount` : error 가 업데이트된 횟수
 * @property {`Promise<...>`} `promise` : 실행 중인 쿼리의 결과를 기다릴 수 있는 Promise 객체
 */

export const mockedUseAppQuery: ReturnType<typeof originalUseAppQuery> = {
  data: null,
  error: null,
  isEnabled: true,
  isLoading: false,
  isInitialLoading: false,
  isFetching: false,
  refetch: jest.fn(),
  isError: false,
  isSuccess: true,
  status: "success",
  dataUpdatedAt: 0,
  errorUpdatedAt: 0,
  failureCount: 0,
  isFetched: false,
  isFetchedAfterMount: false,
  isRefetching: false,
  isStale: true,
  isPlaceholderData: false,
  fetchStatus: "idle",
  isPaused: false,
  isPending: false,
  isLoadingError: false,
  isRefetchError: false,
  failureReason: null,
  errorUpdateCount: 0,
  promise: Promise.resolve(null),
};

/** ### [ useAppMutation Mock ]
 * @description
 * **내부 속성들을 Jest가 추적할 수 있도록 mockedUseAppMutation 을 반환**
 */
export const useAppMutation = jest.fn(() => mockedUseAppMutation);

/** ### [ useAppQueryMock ]
 * @description
 * **내부 속성들을 Jest가 추적할 수 있도록 mockedUseAppQuery 을 반환**
 */
export const useAppQuery = jest.fn(() => mockedUseAppQuery);
