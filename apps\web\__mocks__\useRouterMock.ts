import { useRouter as originalUseRouter } from "next/router";
/**
 * ### [ useRouter 가 반환하는 속성과 동일한 구조를 가진 Mock 객체 ]
 *
 * @description
 * **useRouter 가 반환하는 속성을 Jest 가 추적 및 테스트 할 수 있도록,**
 * **useRouter 의 Mock 이 아래의 객체를 반환하도록 구성.**
 *
 * ---
 * @property {`Promise<boolean>`} `push` : 페이지 이동 메서드 (브라우저 히스토리에 새 항목으로 기록 추가)
 * @property {`() => void`} `back`: 페이지 뒤로 이동 (브라우저 히스토리 기록 상 이전 페이지)
 * @property {`Promise<boolean>`} `replace` : 페이지 이동 메서드 (브라우저 히스토리에 현재 경로 기록을 대체)
 * @property {`() => void`} `reload` : 브라우저 전체 리로드하는 메서드 (완전한 새로고침)
 * @property {`Promise<void>`} `prefetch` : 이동할 가능성이 있는 페이지 코드와 데이터를 미리 로드하는 메서드
 * @property {`() => void`} `forward` : 페이지 앞으로 이동
 * @property {`string`} `route` : 동적 라우트 기준 기본 경로
 * @property {`string`} `pathname` : 현재 경로의 경로명
 * @property {`ParsedUrlQuery`} `query` : URL 쿼리 파라미터 값
 * @property {`string`} `asPath` : 실제 브라우저에 표기되는 전체 경로
 * @property {`string`} `basePath` : next.config.js 에서 설정한 Base URL
 * @property {`boolean`} `isFallback` : getStaticPaths 에서 fallback 이 true 일 때, 로딩 중인지 여부
 * @property {`boolean`} `isReady` : 쿼리파라미터가 모두 준비되었는지 여부
 * @property {`boolean`} `isLocaleDomain` : locale 도메인의 사용여부
 * @property {`boolean`} `isPreview` : 프리뷰 모드 여부
 * @property {`(event: string, handler: () => void) => void`} `on` : 라우팅 이벤트 구독
 * @property {`(event: string, handler: () => void) => void`} `off` : 라우팅 이벤트 구독 해제
 * @property {`(event: string, ...args: any[]) => void`} `emit` : 라우팅 이벤트 발생
 * @property {`(cb: BeforePopStateCallback) => void`} `beforePopState` : 뒤로가기 등의 브라우저 히스토리 조작 시 실행되는 동작을 커스텀
 * @property {`string | undefined`} `locale` : 현재 활성화 된 locale
 * @property {`readonly DomainLocale[] | undefined`} `locales` : 지원하는 locale
 * @property {`string | undefined`} `defaultLocale` : locale 의 기본값
 * @property {`readonly DomainLocale[] | undefined`} `domainLocales` : 어떤 도메인이 어떤 locale에 매핑되는지에 대한 목록
 *
 */
export const mockedUseRouter: ReturnType<typeof originalUseRouter> = {
  push: jest.fn(),
  back: jest.fn(),
  replace: jest.fn(),
  reload: jest.fn(),
  prefetch: jest.fn(),
  forward: jest.fn(),
  route: "/mocked_current_route",
  pathname: "/mocked_current_route",
  query: { mock: "router" },
  asPath: "/mocked_current_route?mock=useRouter",
  basePath: "",
  isFallback: false,
  isReady: true,
  isLocaleDomain: false,
  isPreview: false,
  events: {
    on: jest.fn(),
    off: jest.fn(),
    emit: jest.fn(),
  },
  beforePopState: jest.fn(),
  locale: "en",
  locales: ["en", "ko"],
  defaultLocale: "en",
  domainLocales: undefined,
};

/** ### [ useRouterMock ]
 * @description
 * **내부 속성들을 Jest가 추적할 수 있도록 mockedUseRouter 을 반환**
 */
export const useRouter = jest.fn(() => mockedUseRouter);
