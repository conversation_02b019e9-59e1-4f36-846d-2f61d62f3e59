
# [ Jest 설치 및 설정 ]

- 참고
  - <https://nextjs.org/docs/app/guides/testing/jest>
  - <https://nextjs-ko.org/docs/pages/building-your-application/testing/jest>

## 1. Jest 설치

- Next.js + turborepo 이므로 테스트를 수행할 위치인 apps/web/ 위치에 Jest 설치
- `pnpm install -D jest jest-environment-jsdom @testing-library/react @testing-library/dom @testing-library/jest-dom ts-node @types/jest`
  
## 2. JestConfig 파일 생성 및 설정

- `pnpm create jest@latest` => `jest.config.ts` 생성
- 마법사 설정은 Next.js 공식문서 한글 번역 참조  
  <https://nextjs-ko.org/docs/pages/building-your-application/testing/jest>

    ```text
    √ Would you like to use Jest when running "test" script in "package.json"?
        ... yes
        { => package.json 에 "scripts" 에 "test": "jest 추가하는 설정. }

    √ Would you like to use Typescript for the configuration file
        ... yes
        { => jest.config.js 파일을 ts로 생성하는 설정. }

    √ Choose the test environment that will be used for testing
        » jsdom (browser-like)
        { => Jest 가 테스트를 실행할 때, 기본적으로 Node.js 환경에서 실행하는데 브라우저에서  
             실행되는 RA, DOM 조작 등은 Node 환경에서 테스트할 수 없어 jsdom 을 사용해 가상의 
             브라우저 환경에서 이를 테스트 할 수 있도록 하는 설정. }

    √ Do you want Jest to add coverage reports?
        ... yes
        { => Jest 가 테스트 실행 중 어떤 파일의 어떤 코드가 실행되었는지 기록(커버리지 기록)하는 설정.}

    √ Which provider should be used to instrument code for coverage?
        » v8
        { => 커버리지 측정 시, 진행하는 코드 계측(커버리지 측정을 위한 측정 코드 삽입)을   
             바벨이 아닌 Node.js, Chrome 에 탑재된 v8엔진의 빌트인 커버리지 기능을  
             활용해서 처리.(ts-jest 나 별도의 babel 설정이 필요 없음) }

    √ Automatically clear mock calls, instances, contexts and results before every test?
        ... yes
        { => Jest 에서 jest.fn() 또는 jest.mock() 을 통한 mock 함수의 호출 기록,  
             인스턴스, 컨텍스트 정보 등을 초기화해 테스트간 간섭을 방지하는 설정. }
    ```

## 3. `jest-dom` 설정

- `jest-dom` 을 통해 직관적인 custom matcher 를 사용
- 매번 테스트 코드마다 이를 import 하기 위한 것을 피하기 위해 아래의 절차를 수행
- `apps/web` 에 `jest.setup.ts` 파일 생성 후, 아래와 같이 작성
  
  ```ts
  import '@testing-library/jest-dom'
  ```
  
- `jest.config.ts` 파일에서 아래와 같이 추가

  ```ts
  ....
    // 테스트 실행 이전 미리 실행할 스크립트 파일 목록을 지정하는 필드.
    // 글로벌로 import '@testing-library/jest-dom'
    setupFilesAfterEnv: ['<rootDir>/jest.setup.ts'],
  ....
  ```

- 참고
  - `jest-dom` 없는 테스트 코드  
    - `expect(button.testContent).toBe('로그인')`
    - `textContent`, `value`, `checked` 등으로 직접 속성에 접근해야 함  

  - `jest-dom` 있는 테스트 코드
    - `expect(screen.getByText('로그인')).toBeInTheDocument();`
    - 눈으로 직접 보는 UI 단위로 테스트 가능
    - `toBeInTheDocument()`, `toBeVisible()` 와 같이 명확한 의도 명시

## Next.js 설정 Jest에 반영

- Jest 는 Next.js 의 Webpack, Ts, CSS 모듈, 이미지, import 등의 내부 설정을 기본적으로 지원하지 않기 때문에 Next.js 에서 제공하는 공식 유틸인 `next/jest`를 통해 `next.config.js` 를 포함한 기본적인 Next.js 설정이 Jest 환경에 반영되도록 한다.

   ```tsx
     import nextJest from 'next/jest';
     
     //추가 
     //Jest 설정 객체의 명확한 타입을 가져오고, 
     //TS 타입만 가져와 실행코드에는 포함 X -> import type 
     import type { Config } from 'jest';
 
     //추가
     //nextJest() 는 Next.js 에서 Jest 설정 생성자로,
     //Next.js 에 최적화된 Jest 설정을 루트 디렉토리를 기준으로 자동생성
     //Webpack, CSS 모듈, 이미지, 환경변수(.env, next.config.json) 적용 등을 
     //Jest에 맞게 설정.
     const createJestConfig = nextJest({ dir: './' })
 
     // 수정
     // Jest 의 config 객체의 타입을 명확하게 import 한 Config 로 지정하여,
     // 잘못된 설정을 오류로 알리도록 설정
     const config: Config = {
     ..........
     ......
     ....
     // export default config 면 아래로 변경
     export default createJestConfig(config);
   ```

- 테스트 코드 변경 시, 테스트 재실행을 위한 옵션 설정 `apps/web/package.json` 에 아래와 같이 추가

   ```json
     .....
       "test": "jest",
       "test:watch": "jest --watch"
     },
     .....
   ```
