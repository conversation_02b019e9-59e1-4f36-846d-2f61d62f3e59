/**
 * ### [테스트 참고]
 * 1. [ sample.tsx - sample.test.tsx ] 와 같이 실제 컴포넌트, 테스트 코드를 쌍으로 하여, 테스트 코드 ~.test.tsx 는 apps/web/__test__ 에 작성한다.
 * 2.
 * 2. 외부 유틸 함수, Props 등의 외부에서 주입받아 사용하는 메서드는 Mocking 하여 `toHaveBeenCalled`, `toHaveBeenCalledWith` 등의 호출 여부만 검증.<br/>
 *   (외부에서 주입받은 메서드의 내부 로직이 이 테스트의 관심사가 아니다.)
 * 2. 컴포넌트 내부
 */

import { fireEvent, render, screen, waitFor } from "@testing-library/react";

import {
  mockedUseAppMutation,
  mockedUseAppQuery,
} from "@/__mocks__/reactQueryMocks";
import { mockedUseRouter } from "@/__mocks__/useRouterMock";
import { jestSampleUtilMethod1 } from "@/app/sample/jest/jestSampleUtils";

import JestSampleComponent from "../app/sample/jest/page";

// 외부 유틸 객체나 Props 등으로 받는 외부 메서드는 Mocking 하여 toHaveBeen~() 으로 검증(그 메서드의 로직을 검사하는게 아닌 현재 컴포넌트에서 정상 호출인지)
// 테스트하는 컴포넌트의 내부 메서드는 toBeInTheDocument 와 같이 화면상의 결과로 간접 검증

// mocking 을 아래와 같이 "~".test.tsx 에서 직접 jest.mock(..) 으로 만들어도 되고
// __mocks__ 하위에 자주 사용될 것 같은 mock 을 미리 만들어두고 import 하여 사용해도 된다.

/** 외부 유틸 메서드 Mock */
jest.mock("../app/sample/jest/jestSampleUtils", () => ({
  jestSampleUtilMethod1: jest.fn(),
}));

/** Props 로 전달 받은 메서드 Mock */
const mockPropsMethod = jest.fn();

describe("Jest Sample Component Test", () => {
  beforeEach(() => {
    mockedUseAppQuery.data = "2025-06-17T12:00:00.000Z";
    jest.clearAllMocks();
  });

  /** 1. 화면 렌더링 테스트 : 화면의 모든 요소가 렌더링 되었는지 테스트
   *
   *  -  HTML 태그와 getByRole 맵핑 중 <div>, <p> 는 자동 매핑이 없어 강제로 role 을 부여하거나 getByText 사용
   *   -------------------------------------------------------------------------------
   *   | HTML 태그                           | getByRole                              |
   *   | -----------------------------------| ---------------------------------------|
   *   | <button>                           | getByRole("button", { name: "등록" })   |
   *   | <a href="...">                     | getByRole("link", { name: "홈" })       |
   *   | <img alt="..." />                  | getByRole("img", { name: "프로필" })    |
   *   | <h1>\<h6>                          | getByRole("heading", { level: 1 })     |
   *   | <input type="checkbox">            | getByRole("checkbox", { name: "동의" }) |
   *   | <input type="radio">               | getByRole("radio", { name: "남자" })    |
   *   | <input type="text">, <textarea>    | getByRole("textbox", { name: "내용" })  |
   *   | <input type="search">              | getByRole("searchbox")                 |
   *   | <form>                             | getByRole("form")                      |
   *   | <select>                           | getByRole("combobox", { name: "옵션" })  |
   *   | <table>                            | getByRole("table")                       |
   *   | <th> (scope="col/row")             | getByRole("columnheader")                |
   *   | <progress>                         | getByRole("progressbar")                 |
   *   | <dialog>                           | getByRole("dialog")                      |
   *   | <nav>                              | getByRole("navigation")                  |
   *   | <aside>                            | getByRole("complementary")               |
   *   | <main>                             | getByRole("main")                        |
   *   | <header>                           | getByRole("banner")                      |
   *   | <footer>                           | getByRole("contentinfo")`                |
   *   | ----------------------------------------------------------------------------- |
   */
  it("화면상에 렌더링을 검증하고자 하는 요소가 정상적으로 렌더링 되는지 확인", () => {
    render(<JestSampleComponent />);

    const heading = screen.getByTestId("Header-1");
    expect(heading).toBeInTheDocument();

    const clock = screen.getByText(/currentTime/);
    expect(clock).toBeInTheDocument();

    const getCurrentTimeButton = screen.getByRole("button", {
      name: "시간 조회",
    });
    expect(getCurrentTimeButton).toBeInTheDocument();

    const plusOneHourButton = screen.getByRole("button", {
      name: "1 시간 증가",
    });
    expect(plusOneHourButton).toBeInTheDocument();
  });
  /** 2. useAppQuery refetch 호출하는 버튼 테스트  */
  it("시간 조회 버튼 클릭 시, 화면에 출력(Refetch)하는지 확인", async () => {
    render(<JestSampleComponent />);

    // refetch 시, 시간 데이터를 Promise 형태로 반환하도록 Mocking
    (mockedUseAppQuery.refetch as jest.Mock).mockResolvedValue({
      data: mockedUseAppQuery.data,
    });

    fireEvent.click(screen.getByText("시간 조회"));

    await waitFor(() => {
      expect(mockedUseAppQuery.refetch).toHaveBeenCalled();
      expect(
        screen.getByText(`currentTime : ${mockedUseAppQuery.data}`),
      ).toBeInTheDocument();
    });
  });

  /** 3. useAppQuery refetch 실패 시, 호출하는 버튼 테스트 */
  it("시간 조회 버튼 클릭 후, 요청이 실패했을 때 메시지 또는 fallback 하는지 확인", async () => {
    render(<JestSampleComponent />);

    (mockedUseAppQuery.refetch as jest.Mock).mockRejectedValue(
      new Error("API 호출 실패"),
    );

    fireEvent.click(screen.getByText("시간 조회"));

    await waitFor(() => {
      expect(screen.getByText(/에러가 발생했습니다./)).toBeInTheDocument();
    });
  });

  /** 4. [시간 조회 버튼 클릭] -> [input 에 증가 시킬 시간 입력] -> [X 시간 증가 버튼 클릭] 의 순서 정상 검증 테스트*/
  it("ipnut값 N에 의한 N 시간 증가 버튼 클릭 시, 화면에 N시간 증가시켜서 출력하는지 확인", async () => {
    render(<JestSampleComponent />);
    (mockedUseAppQuery.refetch as jest.Mock).mockResolvedValue({
      data: mockedUseAppQuery.data,
    });
    fireEvent.click(screen.getByText("시간 조회"));

    await waitFor(() => {
      // 증가시킬 시간 input 에 입력(3)
      const inputIncrementTime = 3;
      // getByLabelText 사용시, 컴포넌트에서 label 의 htmlFor, 대상 ele 의 id 매핑 되어 있어야함.
      fireEvent.change(screen.getByLabelText("증가시킬 시간 :"), {
        target: { value: inputIncrementTime },
      });

      // 3시간 증가한 기대값
      const currentTime = new Date(mockedUseAppQuery.data as string);
      currentTime.setHours(currentTime.getHours() + inputIncrementTime);

      // 버튼이 3 시간 증가 로 변경되었는지
      expect(
        screen.getByRole("button", { name: `${inputIncrementTime} 시간 증가` }),
      ).toBeInTheDocument();

      // 버튼 클릭
      fireEvent.click(screen.getByText(`${inputIncrementTime} 시간 증가`));

      // 3시간 증가한 시간이 화면에 출력되는지
      expect(screen.getByTestId("currentTime-p")).toHaveTextContent(
        `currentTime : ${currentTime.toISOString()}`,
      );
    });
  });

  /** 5. 컴포넌트가 받은 테스트용 Props 의 메서드를 제대로 호출하는지 검증 */
  it("컴포넌트가 Props로 받는 함수를 호출할 때, 제대로 호출하고 있는지 확인", () => {
    render(<JestSampleComponent testMethod={mockPropsMethod} />);

    fireEvent.click(screen.getByText("테스트용 콘솔출력1"));

    expect(mockPropsMethod).toHaveBeenCalled();
  });

  /** 6. 컴포넌트에서 사용하는 외부 유틸의 호출 여부와 특정 파라미터로 호출되는지 검증 */
  it("테스트용 외부 유틸 호출 버튼 클릭 시, 특정 파라미터로 호출되는지 확인", () => {
    render(<JestSampleComponent />);

    fireEvent.click(screen.getByText("테스트용 콘솔출력2"));

    expect(jestSampleUtilMethod1).toHaveBeenCalledWith("테스트 파라미터");
  });

  /** 7. 컴포넌트가 받은 테스트용 Props 의 메서드가 호출되어야 하는 만큼 호출되는지 검증 */
  it("컴포넌트가 Props로 받는 함수를 호출할 때, 원하는 횟수만큼 호출하고 있는지 확인", () => {
    render(<JestSampleComponent testMethod={mockPropsMethod} />);

    fireEvent.click(screen.getByTestId("testConsoleLog3"));

    expect(mockPropsMethod).toHaveBeenCalledTimes(6);
  });

  /** 8. 라우팅 검증 */
  it("라우팅 이벤트 호출 시, 정상적으로 라우팅 되는지 확인", () => {
    render(<JestSampleComponent />);

    fireEvent.click(screen.getByRole("button", { name: "Zustand 샘플" }));

    // expect(useRouter().push).toHaveBeenCalledWith("/sample/stores");
    expect(mockedUseRouter.push).toHaveBeenCalledWith("/sample/stores");
  });

  /** 9. useMutation 작동 검증 */
  it("useMutation 작동 여부에 대한 확인", async () => {
    render(<JestSampleComponent />);

    // 버튼 클릭 (정확한 버튼 텍스트 기준으로 name 설정)
    fireEvent.click(screen.getByRole("button", { name: /뮤테이션 테스트 용/ }));

    await waitFor(() => {
      expect(mockedUseAppMutation.mutate).toHaveBeenCalled();
    });
  });
});
