/* eslint-disable @typescript-eslint/no-explicit-any */

import { NextRequest, NextResponse } from "next/server";

import boardDataList from "./tempData/board.json";
import commentData from "./tempData/comment.json";

/** 샘플 게시판 API */
export const GET = async (req: NextRequest) => {
  const { searchParams } = new URL(req.url);
  const pageParam = Number(searchParams.get("page")) || 1;
  const sizeParam = Number(searchParams.get("size")) || 10;
  const searchKeywordType = searchParams.get("searchKeywordType");
  const searchKeyword = searchParams.get("searchKeyword")?.toLowerCase() || "";
  const searchDateType = searchParams.get("searchDateType");
  const searchFromDate = searchParams.get("searchFromDate");
  const searchToDate = searchParams.get("searchToDate");

  // 게시글 목록에서 각 게시글에 댓글 개수를 표기하기 때문에 아래와 같이 댓글 개수 매핑
  // (실제로 하게 된다면 쿼리에서 해결)
  const commentCountMap: Record<number, number> = {};
  for (const comment of commentData as any[]) {
    const boardId = comment.boardId;
    commentCountMap[boardId] = (commentCountMap[boardId] || 0) + 1;
  }

  /** 검색 어 필터링 */
  const filteredData = (boardDataList as any).filter((item: any) => {
    // 날짜 필터링
    if (searchFromDate && searchToDate) {
      const dateType = searchDateType?.trim() || "regDate";

      const targetDateStr = item[dateType];

      if (!targetDateStr) return false;

      const itemDate = new Date(item.regDate);
      const fromDate = new Date(searchFromDate);
      const toDate = new Date(searchToDate);
      toDate.setHours(23, 59, 59, 999);

      if (itemDate < fromDate || itemDate > toDate) {
        return false;
      }
    }

    // 검색어 없으면 item 전체 반환.
    if (!searchKeyword) return true;

    // 검색어 필터링
    if (
      searchKeywordType &&
      ["title", "content", "writer", "all"].includes(searchKeywordType)
    ) {
      // searchKeyword 가 all 이면 title, content, writer 중 하나라도 포함되면 반환
      if (searchKeywordType === "all") {
        return (
          item.title.toLowerCase().includes(searchKeyword) ||
          item.content.toLowerCase().includes(searchKeyword) ||
          item.writer.toLowerCase().includes(searchKeyword)
        );
      } else {
        // searchKeyword 가 all 이 아닌 것이 존재하면 그 필드에서만 검색
        return item[searchKeywordType].toLowerCase().includes(searchKeyword);
      }
    } else {
      // searchKeywordType이 없거나 잘못된 경우에도 title, content, writer 조건으로 통합 검색
      return (
        item.title.toLowerCase().includes(searchKeyword) ||
        item.content.toLowerCase().includes(searchKeyword) ||
        item.writer.toLowerCase().includes(searchKeyword)
      );
    }
  });

  const totalRecordCount = filteredData.length;
  const totalPageCount = Math.ceil(totalRecordCount / sizeParam);
  const startIdx = (pageParam - 1) * sizeParam;

  const boardList = filteredData
    .sort(
      (a: any, b: any) =>
        new Date(b.regDate).getTime() - new Date(a.regDate).getTime(),
    )
    .slice(startIdx, startIdx + sizeParam)
    .map((item: any) => ({
      ...item,
      commentCount: commentCountMap[item.boardId] ?? 0,
    }));

  const pageBlockSize = 5;
  const currentBlock = Math.floor((pageParam - 1) / pageBlockSize);
  const startPage = currentBlock * pageBlockSize + 1;
  const endPage = Math.min(startPage + pageBlockSize - 1, totalPageCount);

  const hasPreviousBlock = startPage > 1;
  const hasNextBlock = endPage < totalPageCount;

  const responseData = {
    success: true,
    message: "요청 성공.",
    content: boardList,
    pageNumber: pageParam,
    recordCountPerPage: sizeParam,
    totalRecordCount,
    totalPageCount,
    startPage: startPage,
    endPage: endPage,
    hasPreviousBlock,
    hasNextBlock,
    errorReasonPhrase: null,
  };

  return NextResponse.json(responseData);
};
