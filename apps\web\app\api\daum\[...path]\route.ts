// apps/web/app/api/daum/[...path]/route.ts

import { NextRequest, NextResponse } from "next/server";

export async function GET(req: NextRequest) {
  const { pathname } = new URL(req.url);
  const path = pathname.split("/api/daum/")[1];

  if (!path) {
    return new NextResponse("Missing path", { status: 400 });
  }

  const proxyUrl = `https://map.daumcdn.net/${path}`;
  const response = await fetch(proxyUrl);

  if (!response.ok) {
    return new NextResponse("Upstream error", { status: response.status });
  }

  const buffer = await response.arrayBuffer();
  const contentType =
    response.headers.get("content-type") ?? "application/octet-stream";

  return new NextResponse(Buffer.from(buffer), {
    status: 200,
    headers: {
      "Content-Type": contentType,
      "Cache-Control": "public, max-age=86400",
    },
  });
}
