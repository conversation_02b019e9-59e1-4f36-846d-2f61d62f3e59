:root {
  --background: #ffffff;
  --foreground: #171717;
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

.odf-map {
  width: 100%;
  height: 100%;
}

.odf-control {
  display: none;
}
html,
body {
  max-width: 100vw;
  overflow-x: hidden;
}

body {
  color: var(--foreground);
  background: var(--background);
}

* {
  box-sizing: border-box;
  padding: 0;
  margin: 0;
}

a {
  color: inherit;
  text-decoration: none;
}

.imgDark {
  display: none;
}

@media (prefers-color-scheme: dark) {
  html {
    color-scheme: dark;
  }

  .imgLight {
    display: none;
  }
  .imgDark {
    display: unset;
  }
}
