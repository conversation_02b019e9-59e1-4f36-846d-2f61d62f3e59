import "@config/tailwind/globals.css";

import { QueryProvider } from "@geon-query/react-query";
import type { Metada<PERSON> } from "next";
import Script from "next/script";

import { geistMono, geistSans } from "@/assets/fonts";

export const metadata: Metadata = {
  title: "Create Next App",
  description: "Generated by create next app",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en">
      <body className={`${geistSans.variable} ${geistMono.variable}`}>
        {/* TODO provider는 하나로 합치기 */}
        <main className="size-full">
          <QueryProvider>{children}</QueryProvider>
        </main>
        <Script src="/js/odf.min.js" />
      </body>
    </html>
  );
}
