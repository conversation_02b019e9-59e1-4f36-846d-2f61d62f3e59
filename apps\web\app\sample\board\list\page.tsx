import React from "react";

import BoardList from "../../../../components/board/BoardList";

const page = () => {
  return (
    <div className="h-screen">
      {/* 임시 layout */}
      <div className="flex h-[5%] items-center justify-center bg-gray-300">
        <p>임시 Header 영역</p>
      </div>
      <div className="flex h-[95%] justify-between">
        <div className="flex h-full w-[5%] items-center justify-center bg-green-200">
          <p>임시 대 매뉴</p>
        </div>
        <div className="flex h-full w-[15%] items-center justify-center bg-yellow-200 ">
          <p>임시 중 매뉴</p>
        </div>
        <div className="flex h-full w-[85%] flex-col">
          <BoardList />
        </div>
      </div>
    </div>
  );
};

export default page;
