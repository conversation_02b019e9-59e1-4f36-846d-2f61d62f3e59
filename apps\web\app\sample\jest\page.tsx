"use client";

import { fetcher } from "@geon-query/model/utils/fetcher";
import { useAppMutation, useAppQuery } from "@geon-query/react-query";
import { useRouter } from "next/navigation";
import React, { useState } from "react";

import { jestSampleUtilMethod1 } from "./jestSampleUtils";

type JestSampleComponentProps = {
  testMethod?: () => void;
};

const JestSampleComponent = ({
  testMethod = () => console.log("테스트 Props 메서드"),
}: JestSampleComponentProps) => {
  const router = useRouter();
  const [currentTime, setCurrentTime] = useState<string | null>(null);
  const [incrementTime, setIncrementTime] = useState<number>(1);

  const {
    data,
    isLoading,
    refetch: refetchCurrentTime,
  } = useAppQuery<string>({
    queryKey: ["currentTime"],
    queryFn: () => fetcher.get("/api/currentTime"),
  });

  const { mutate: postTime } = useAppMutation<string, void>({
    mutationFn: () => fetcher.post("/api/currentTime", {}),
    onSuccess: (data) => setCurrentTime(data),
  });

  const handleClickGetCurrentTime = async () => {
    try {
      await refetchCurrentTime();
      if (data && !isLoading) {
        setCurrentTime(data);
      }
    } catch {
      setCurrentTime("에러가 발생했습니다.");
    }
  };

  const handleClickPlusOneHour = (incrementTime: number) => {
    if (currentTime) {
      const date = new Date(currentTime);
      date.setHours(date.getHours() + incrementTime);
      setCurrentTime(date.toISOString());
    }
  };

  const handleClickTestConsoleLog1 = () => {
    testMethod();
  };

  const handleClickTestConsoleLog2 = (testParam: string) => {
    jestSampleUtilMethod1(testParam);
  };

  const handleClickTestConsoleLog3 = (numberOfCalled: number) => {
    for (let i = 0; i <= numberOfCalled; i++) {
      testMethod();
    }
  };

  const handleClickTestRouter = () => {
    router.push("/sample/stores");
  };

  return (
    <div className="w-5/12">
      <h1 data-testid="Header-1" className="mb-4 text-center">
        Jest Sample Component
      </h1>
      <p data-testid="currentTime-p" className="mb-2 text-center">
        currentTime : {currentTime}
      </p>
      <div className="mb-2 flex justify-between">
        <button
          className="w-6/12 rounded border px-4"
          onClick={() => handleClickGetCurrentTime()}
        >
          시간 조회
        </button>
        <button
          className="w-6/12 rounded border px-4"
          onClick={() => handleClickPlusOneHour(incrementTime)}
        >
          {incrementTime} 시간 증가
        </button>
      </div>

      <div className="mb-4 flex justify-between">
        <label htmlFor="increment-input">증가시킬 시간 :</label>
        <input
          id="increment-input"
          className="w-8/12 text-center"
          type="number"
          onChange={(e) => setIncrementTime(Number(e.target.value))}
        />
      </div>

      <div className="mb-2 flex justify-between">
        <button
          className="w-6/12 rounded border px-4"
          onClick={() => handleClickTestConsoleLog1()}
        >
          테스트용 콘솔출력1
        </button>
        <button
          className="w-6/12 rounded border px-4"
          onClick={() => handleClickTestConsoleLog2("테스트 파라미터")}
        >
          테스트용 콘솔출력2
        </button>
      </div>

      <div className="mb-2 flex justify-between">
        <button
          data-testid="testConsoleLog3"
          className="w-6/12 rounded border px-4"
          onClick={() => handleClickTestConsoleLog3(5)}
        >
          테스트용 콘솔출력3
        </button>
        <button
          className="w-6/12 rounded border px-4"
          onClick={() => handleClickTestRouter()}
        >
          Zustand 샘플
        </button>
      </div>
      <div className="flex justify-between">
        <button
          className="w-6/12 rounded border px-4"
          onClick={() => postTime()}
        >
          뮤테이션 테스트 용
        </button>
      </div>
    </div>
  );
};

export default JestSampleComponent;
