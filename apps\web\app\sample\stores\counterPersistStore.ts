import { createZustandStore } from "../../../zustand/zustand";

type CounterState = {
  pCount: number;
  pInc: () => void;
  pClear: () => void;
};

export const useCounterPersistStore = createZustandStore<CounterState>({
  persist: true,
  state: (set) => ({
    pCount: 0,
    pInc: () =>
      set((prev) => ({
        ...prev,
        pCount: prev.pCount + 1,
      })),
    pClear: () => {
      set((prev) => ({
        ...prev,
        pCount: 0,
      }));
      if (typeof localStorage !== "undefined") {
        localStorage.removeItem("counter-persist");
      }
    },
  }),
  name: "counter-persist",
  // 기본값이 false 로 생략 가능, => 지정한 storage 에서 복원
  skipHydration: false,
});
