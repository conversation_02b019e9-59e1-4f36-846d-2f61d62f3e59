import { createZustandStore } from "../../../zustand/zustand";

type CounterState = {
  count: number;
  inc: () => void;
  clear: () => void;
};

export const useCounterStore = createZustandStore<CounterState>({
  persist: false,
  state: (set) => ({
    count: 0,
    inc: () =>
      set((prev) => ({
        ...prev,
        count: prev.count + 1,
      })),
    clear: () => {
      set((prev) => ({
        ...prev,
        count: 0,
      }));
    },
  }),
});
