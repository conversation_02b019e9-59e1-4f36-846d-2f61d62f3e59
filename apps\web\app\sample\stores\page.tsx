//apps/web/apps/sample/stores/page.ts
"use client";

import React, { useEffect, useState } from "react";

import { useCounterPersistStore } from "./counterPersistStore";
import { useCounterStore } from "./counterStore";

const CounterStoreSample = () => {
  const { count, inc, clear } = useCounterStore();
  const { pCount, pInc, pClear } = useCounterPersistStore();

  /* persist : true, skipHydration : false => 비동기로 지정된 Storage 에서 상태값을 복원 */
  /* useEffect + Hydrated 처리 없을 시, store 의 초기값이 보일 수 있음.(선택사항) */
  const [hydrated, setHydrated] = useState(false);
  useEffect(() => {
    setHydrated(true);
  }, []);

  if (!hydrated) return <div>Loading...</div>;
  return (
    <div>
      <h1>새로고침 후 확인</h1>
      <div className="p-4">
        <h1 className="mb-2 text-2xl">CounterStore: {count}</h1>
        <button className="rounded border px-4 py-2" onClick={inc}>
          Increment
        </button>
        <button className="ml-2 rounded border px-4 py-2" onClick={clear}>
          clear
        </button>
      </div>

      <div className="p-4">
        <h1 className="mb-2 text-2xl">CounterPersistStore: {pCount}</h1>
        <button className="rounded border px-4 py-2" onClick={pInc}>
          Increment
        </button>
        <button className="ml-2 rounded border px-4 py-2" onClick={pClear}>
          clear
        </button>
      </div>
    </div>
  );
};

export default CounterStoreSample;
