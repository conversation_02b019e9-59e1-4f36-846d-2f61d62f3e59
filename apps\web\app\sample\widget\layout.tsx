import {
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  ScaleProvider,
} from "@geon-map/react-odf";
import React from "react";

export default function WidgetLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <MapProvider defaultOptions={{ projection: "EPSG:5186" }}>
      <ScaleProvider scaleOptions={{ size: 100, scaleInput: false }} />
      <BasemapProvider />
      <ClearProvider />
      <MeasureProvider
        measureOptions={{
          tools: ["distance", "area", "round", "spot"],
          continuity: false,
          rightClickDelete: false,
        }}
      />
      <DrawProvider
        drawOptions={{
          continuity: false,
          tools: [
            "text",
            "polygon",
            "lineString",
            "box",
            "point",
            "circle",
            "curve",
          ],
          style: {
            fill: { color: [255, 255, 0, 1] },
            stroke: { color: [0, 255, 0, 0.8], width: 5 },
          },
        }}
      />
      {children}
    </MapProvider>
  );
}
