"use client";
import { Map, useMap, useMapActions } from "@geon-map/react-odf";
import {
  DownloadWidget,
  MouseCoordWidget,
  OverviewWidget,
  ScaleWidget,
  SpatailSearchWidget,
} from "@geon-map/react-ui/components";
import { RoadviewWidget } from "@geon-map/react-ui/components";
import { useEffect, useState } from "react";

import AddressSearchWidgetPackage from "@/components/widget/address-search-widget-pakage";
import BasemapWidgetPakage from "@/components/widget/basemap-widget-pakage";
import BasemapWidgetUse from "@/components/widget/basemap-widget-use";
import DrawWidgetPakage from "@/components/widget/draw-widget-pakage";
import LayerFileDownloadPackage from "@/components/widget/layer-file-download-widget-pakage";
import { MouseCoordDisplay } from "@/components/widget/mouse-coord-display";
import RegionSelectorWidgetPackage from "@/components/widget/region-selector-widget-pakage";

// 🎯 지도 상태 동기화 테스트 컴포넌트
function MapStateMonitor() {
  const { center, zoom, scale, isLoading, isReady } = useMap();
  const { setCenter, setZoom } = useMapActions();
  const [updateCount, setUpdateCount] = useState(0);

  // TODO: 베이스맵 상태 추후 구현
  // const currentBasemap = useMapStore((state) => state.currentBasemap);

  // 상태 변경 감지
  useEffect(() => {
    setUpdateCount((prev) => prev + 1);
  }, [center, zoom, scale]);

  return (
    <div className="absolute left-4 top-4 z-50 max-w-[280px] rounded-lg bg-white/90 p-3 text-xs shadow-lg backdrop-blur">
      <h4 className="mb-2 text-sm font-bold text-blue-600">
        🔄 상태 동기화 테스트
      </h4>

      <div className="space-y-1">
        <div className="flex justify-between">
          <span>상태:</span>
          <span className={isReady ? "text-green-600" : "text-orange-600"}>
            {isLoading ? "로딩중" : isReady ? "준비됨" : "초기화중"}
          </span>
        </div>

        <div className="flex justify-between">
          <span>중심점:</span>
          <span className="font-mono">
            [{center[0].toFixed(1)}, {center[1].toFixed(1)}]
          </span>
        </div>

        <div className="flex justify-between">
          <span>줌:</span>
          <span>{zoom}</span>
        </div>

        <div className="flex justify-between">
          <span>축척:</span>
          <span>{scale || "계산중"}</span>
        </div>

        <div className="flex justify-between border-t pt-1">
          <span>업데이트:</span>
          <span className="font-bold text-blue-600">{updateCount}회</span>
        </div>
      </div>

      {/* 빠른 테스트 버튼들 */}
      <div className="mt-2 space-y-1 border-t pt-2">
        <div className="flex gap-1">
          <button
            onClick={() => setCenter([126.978, 37.5665], "EPSG:4326")}
            className="rounded bg-blue-500 px-2 py-1 text-xs text-white hover:bg-blue-600"
          >
            서울
          </button>
          <button
            onClick={() => setCenter([129.0756, 35.1796], "EPSG:4326")}
            className="rounded bg-blue-500 px-2 py-1 text-xs text-white hover:bg-blue-600"
          >
            부산
          </button>
        </div>
        <div className="flex gap-1">
          <button
            onClick={() => setZoom(8)}
            className="rounded bg-green-500 px-2 py-1 text-xs text-white hover:bg-green-600"
          >
            줌8
          </button>
          <button
            onClick={() => setZoom(15)}
            className="rounded bg-green-500 px-2 py-1 text-xs text-white hover:bg-green-600"
          >
            줌15
          </button>
        </div>
      </div>
    </div>
  );
}

export default function Page() {
  return (
    <Map className="h-[800px] w-full">
      {/* 🎯 상태 동기화 테스트 모니터 */}
      <MapStateMonitor />

      <BasemapWidgetPakage className="top-20" />
      <BasemapWidgetUse />
      <AddressSearchWidgetPackage />
      <DrawWidgetPakage />
      <MouseCoordDisplay />
      <MouseCoordWidget />
      <RoadviewWidget />
      <OverviewWidget />
      <RegionSelectorWidgetPackage />
      <ScaleWidget />
      <DownloadWidget />
      <SpatailSearchWidget />
      {/* LayerFileDownloadPackage 의 layerFileDownloadInfo 는 임시데이터, buttonName 은 버튼 이름 지정 */}
      <LayerFileDownloadPackage
        layerFileDownloadInfo={{
          // trgetTypeName: "Wgeonapi:L100003391",
          trgetTypeName: "muan_gis:2017_2018_n1a_a0010000",
          outputFormat: "",
        }}
        buttonName="레이어 다운로드"
      />
      {/* 테스트 컴포넌트 */}
      {/* <MapTest /> */}
    </Map>
  );
}
