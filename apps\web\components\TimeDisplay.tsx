"use client";

import { fetcher } from "@geon-query/model/utils/fetcher";
import { useAppMutation, useAppQuery } from "@geon-query/react-query";
import { useEffect, useState } from "react";

export function TimeDisplay() {
  const [time, setTime] = useState<string | null>(null);

  // ✅ GET: 최초 렌더링 시 호출
  const { data, isLoading, refetch, isFetching } = useAppQuery<string>({
    queryKey: ["currentTime"],
    queryFn: () => fetcher.get("/api/currentTime"),
  });

  // ✅ 최초 응답 오면 상태에 저장
  useEffect(() => {
    if (data && !isLoading) {
      setTime(data);
    }
  }, [data, isLoading]);

  // ✅ POST: 수동 실행
  const {
    mutate: postTime,
    isPending: isPosting,
    error,
  } = useAppMutation<string, void>({
    mutationFn: () => fetcher.post("/api/currentTime", {}),
    onSuccess: (data) => setTime(data),
  });

  return (
    <div style={{ padding: 16 }}>
      <h2>현재 시간</h2>

      <button onClick={() => refetch()} disabled={isFetching}>
        🔄 GET 요청 (최초 조회 또는 새로고침)
      </button>

      <button
        onClick={() => postTime()}
        disabled={isPosting}
        style={{ marginLeft: 8 }}
      >
        📤 POST 요청 (시간 새로 받기)
      </button>

      {(isFetching || isPosting) && <p>요청 중...</p>}

      {time && <p>🕒 {time}</p>}

      {error instanceof Error && (
        <p style={{ color: "red" }}>에러: {error.message}</p>
      )}
    </div>
  );
}
