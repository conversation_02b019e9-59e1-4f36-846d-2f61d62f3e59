"use client";

import { defaultGeonSmtClient } from "@geon-query/model/restapi/smt";
import {
  BasemapInsertRequest,
  BasemapListRequest,
  BasemapUpdateRequest,
} from "@geon-query/model/restapi/type/smt-type";
import { useAppQuery } from "@geon-query/react-query";
import { useState } from "react";

export default function BasemapCrud() {
  const [params, setParams] = useState<BasemapListRequest>({
    pageIndex: 1,
    pageSize: 100,
  });

  const [form, setForm] = useState<
    Partial<BasemapInsertRequest & { bcrnMapId?: string }>
  >({
    bcrnMapNm: "",
    bcrnMapClCode: "",
    bcrnMapUseAt: "Y",
    lyrStleCode: "M",
    mapUrl: "",
    registerId: "admin",
  });

  const { data, isFetching, refetch } = useAppQuery({
    queryKey: ["basemapList", params],
    queryFn: () => defaultGeonSmtClient.basemap.list(params),
  });

  const handleAPIResponse = async (
    fn: () => Promise<any>,
    actionName: string,
  ) => {
    if (!confirm(`${actionName} 하시겠습니까?`)) return;
    try {
      const res = await fn();
      if (res?.code === 200) {
        alert(`${actionName} 완료`);
        refetch();
        setForm({
          bcrnMapNm: "",
          bcrnMapClCode: "",
          bcrnMapUseAt: "Y",
          lyrStleCode: "M",
          mapUrl: "",
          registerId: "admin",
        });
      } else {
        alert(`오류: ${res?.message || "실패하였습니다."}`);
      }
    } catch (e: any) {
      alert(`예외 발생: ${e?.message}`);
    }
  };

  return (
    <div className="mx-auto max-w-2xl space-y-6 p-6">
      <h2 className="text-xl font-semibold">🗺️ 베이스맵 CRUD</h2>

      <div className="space-y-3 rounded bg-white p-4 shadow">
        <input
          className="w-full rounded border px-3 py-2"
          placeholder="지도 이름 (bcrnMapNm)"
          value={form.bcrnMapNm || ""}
          onChange={(e) =>
            setForm((f) => ({ ...f, bcrnMapNm: e.target.value }))
          }
        />
        <input
          className="w-full rounded border px-3 py-2"
          placeholder="지도 분류코드 (bcrnMapClCode)"
          value={form.bcrnMapClCode || ""}
          onChange={(e) =>
            setForm((f) => ({ ...f, bcrnMapClCode: e.target.value }))
          }
        />
        <input
          className="w-full rounded border px-3 py-2"
          placeholder="지도 URL (mapUrl)"
          value={form.mapUrl || ""}
          onChange={(e) => setForm((f) => ({ ...f, mapUrl: e.target.value }))}
        />
        <select
          className="w-full rounded border px-3 py-2"
          value={form.lyrStleCode || "M"}
          onChange={(e) =>
            setForm((f) => ({
              ...f,
              lyrStleCode: e.target.value as "M" | "F" | "T",
            }))
          }
        >
          <option value="M">WMS</option>
          <option value="F">WFS</option>
          <option value="T">WMTS</option>
        </select>
        <select
          className="w-full rounded border px-3 py-2"
          value={form.bcrnMapUseAt}
          onChange={(e) =>
            setForm((f) => ({
              ...f,
              bcrnMapUseAt: e.target.value as "Y" | "N",
            }))
          }
        >
          <option value="Y">사용</option>
          <option value="N">미사용</option>
        </select>

        <button
          className="w-full rounded bg-blue-600 py-2 text-white hover:bg-blue-700"
          onClick={() => {
            if (form.bcrnMapId) {
              handleAPIResponse(
                () =>
                  defaultGeonSmtClient.basemap.update(
                    form as BasemapUpdateRequest,
                  ),
                "수정",
              );
            } else {
              handleAPIResponse(
                () =>
                  defaultGeonSmtClient.basemap.insert(
                    form as BasemapInsertRequest,
                  ),
                "등록",
              );
            }
          }}
        >
          {form.bcrnMapId ? "✏️ 수정하기" : "➕ 등록하기"}
        </button>
      </div>

      <div>
        <h3 className="mb-2 text-lg font-medium">📄 목록</h3>
        {isFetching && <p>불러오는 중...</p>}

        <ul className="space-y-3">
          {data?.result?.list?.length ? (
            data.result.list.map((item: any) => (
              <li
                key={item.bcrnMapId}
                className="rounded border bg-gray-50 px-4 py-2"
              >
                <div className="flex items-center justify-between">
                  <div>
                    <p className="font-semibold">{item.bcrnMapNm}</p>
                    <p className="text-xs text-gray-500">{item.bcrnMapId}</p>
                  </div>
                  <div className="flex gap-2">
                    <button
                      className="rounded bg-yellow-500 px-2 py-1 text-sm text-white"
                      onClick={() => setForm({ ...item, registerId: "admin" })}
                    >
                      ✏️ 수정
                    </button>
                    <button
                      className="rounded bg-red-600 px-2 py-1 text-sm text-white"
                      onClick={() =>
                        handleAPIResponse(
                          () =>
                            defaultGeonSmtClient.basemap.delete({
                              bcrnMapId: item.bcrnMapId,
                              crtfckey: undefined,
                            }),
                          "삭제",
                        )
                      }
                    >
                      🗑 삭제
                    </button>
                  </div>
                </div>
              </li>
            ))
          ) : (
            <p className="text-sm text-gray-500">목록이 없습니다.</p>
          )}
        </ul>
      </div>
    </div>
  );
}
