"use client";

type BoardDetailProps = { boardId: string; closeDetailModal: () => void };

const BoardDetailModal = ({ boardId, closeDetailModal }: BoardDetailProps) => {
  return (
    <div className="fixed inset-0 flex items-center justify-center bg-black/50">
      <div className="flex h-[90%] w-[1200px] flex-col  rounded bg-white">
        <div className="flex items-center justify-between ">
          <p className="px-4 py-2 text-2xl font-bold">게시판 종류</p>
          <button
            onClick={() => closeDetailModal()}
            className="right-4 top-4 h-full rounded-r border-l border-black px-3 text-2xl font-bold transition hover:bg-gray-200"
          >
            ⨉
          </button>
        </div>
        <hr className="border-black" />
        <div className=" overflow-y-auto p-4">
          <div className="flex items-center justify-between">
            <p className="text-lg font-bold">게시글 제목</p>
            <button className=" ml-2 rounded border border-black px-[3px] text-xs hover:border-white hover:bg-red-600 hover:text-white">
              삭제
            </button>
          </div>

          <div className="mt-4 border bg-gray-100 p-2 ">
            <div className="flex items-center">
              <p className="text-xs">▸ 작성자 : </p>
              <p className="ml-1 text-xs font-bold">작성자 이름 </p>
            </div>

            <div className="flex items-center">
              <p className="text-xs">▸ 게시글 URL : {window.location.href}</p>
              <button className=" ml-2 rounded border border-black px-[3px] text-xs hover:bg-black hover:text-white">
                복사
              </button>
            </div>
          </div>
          {/** 텍스트 에디터(Tiptap, quill 등)로 textarea 대체 */}
          <div className="mt-3 border ">
            <textarea
              className="min-h-[450px] w-full resize-none px-4 pb-3 pt-4 text-xs focus:outline-none"
              placeholder="텍스트를 입력하세요(Tiptap, Quill 등의 에디터로 대체)."
            />
            <hr className=" border" />
            <ul className="mt-2 space-y-2">
              {/* map으로 반복 */}
              <li className="flex flex-col px-3 pb-0">
                <div className=" flex items-center justify-center ">
                  <div className="flex items-center justify-end">
                    <p className="text-xl">👤</p>
                  </div>
                  <div className="flex w-[95%] items-center gap-4 p-2 text-xs">
                    <div className="flex flex-col ">
                      <p>작성자</p>
                      <p>해당 게시글의 댓글</p>
                    </div>
                    <button className=" ml-2 rounded border border-black px-[3px] text-xs hover:bg-black hover:text-white">
                      댓글
                    </button>
                  </div>
                </div>

                <ul className="pl-10">
                  {/* 대댓글 등록 -> 댓글 버튼 누를 시, 댓글 버튼 취소로 변경하고 해당 li setter 로 노출 */}
                  <li className="flex items-start">
                    <div className="flex items-center justify-end">
                      <p className="text-xl">👤</p>
                    </div>
                    <div className="ml-1 flex w-[95%] flex-col items-end border">
                      <textarea className="w-full resize-none p-4 text-xs focus:outline-none"></textarea>
                      <div className="flex w-full items-center justify-between">
                        <button className="mt-2 px-2 py-1 ">🖇️</button>
                        <button className="mt-2 px-2 py-1 text-xs text-gray-400">
                          등록
                        </button>
                      </div>
                    </div>
                  </li>

                  {/* 대댓글 map으로 반복 */}
                  <li className="flex items-center">
                    <div className="flex items-center justify-end">
                      <p className="text-xl">👤</p>
                    </div>
                    <div className="w-[95%] items-center p-2 text-xs">
                      <p>작성자</p>
                      해당 게시글의 대댓글
                    </div>
                  </li>

                  <li className="flex items-center">
                    <div className="flex items-center justify-end">
                      <p className="text-xl">👤</p>
                    </div>
                    <div className="w-[95%] items-center p-2 text-xs">
                      <p>작성자</p>
                      해당 게시글의 대댓글
                    </div>
                  </li>
                </ul>
              </li>

              <li className="flex flex-col px-3 pb-0">
                <div className="flex items-center justify-center">
                  <div className="flex items-center justify-end">
                    <p className="text-xl">👤</p>
                  </div>
                  <div className="flex w-[95%] items-center gap-4 p-2 text-xs">
                    <div className="flex flex-col ">
                      <p>작성자</p>
                      <p>해당 게시글의 댓글</p>
                    </div>
                    <button className=" ml-2 rounded border border-black px-[3px] text-xs hover:bg-black hover:text-white">
                      댓글
                    </button>
                  </div>
                </div>
              </li>

              <li className="flex items-start justify-center px-3 pb-3">
                <div className="flex items-center justify-end">
                  <p className="text-xl">👤</p>
                </div>
                <div className="ml-1 flex w-[95%] flex-col items-end border">
                  <textarea className="w-full resize-none p-4 text-xs focus:outline-none"></textarea>
                  <div className="flex w-full items-center justify-between">
                    <button className="mt-2 px-2 py-1 ">🖇️</button>
                    <button className="mt-2 px-2 py-1 text-xs text-gray-400">
                      등록
                    </button>
                  </div>
                </div>
              </li>
            </ul>
          </div>
          <div className="mt-3 min-h-[100px] border-x border-t">
            <table className="min-w-full table-fixed text-xs">
              <colgroup>
                <col className="w-[55%]" />
                <col className="w-[15%]" />
                <col className="w-[15%]" />
                <col className="w-[15%]" />
              </colgroup>

              <thead className="border-b bg-gray-100">
                <tr>
                  <th className="px-4 py-2 text-left font-semibold">제목</th>
                  <th className="px-4 py-2 text-left font-semibold">작성자</th>
                  <th className="px-4 py-2 text-left font-semibold">작성일</th>
                  <th className="px-4 py-2 text-left font-semibold">
                    댓글개수
                  </th>
                </tr>
              </thead>

              <tbody>
                {/* tr 은 map 으로 반복, 현재 글은 따로 표시 */}
                <tr className="border-b hover:bg-gray-100">
                  <td className="truncate px-4 py-1">
                    게시글 제목 1 게시글 제목 1 게시글 제목 1 게시글 제목 1
                  </td>
                  <td className="px-4 py-1">작성자1</td>
                  <td className="px-4 py-1">2025-07-03</td>
                  <td className="px-4 py-1">12</td>
                </tr>
                <tr className="border-b hover:bg-gray-100">
                  <td className="truncate px-4 py-1">게시글 제목 2</td>
                  <td className="px-4 py-1">작성자2</td>
                  <td className="px-4 py-1">2025-07-02</td>
                  <td className="px-4 py-1">5</td>
                </tr>
                <tr className="border-b bg-gray-100 hover:bg-gray-100">
                  <td className="truncate px-4 py-1">게시글 제목 3</td>
                  <td className="px-4 py-1">작성자2</td>
                  <td className="px-4 py-1">2025-07-02</td>
                  <td className="px-4 py-1">5</td>
                </tr>
                <tr className="border-b hover:bg-gray-100">
                  <td className="truncate px-4 py-1">게시글 제목 4</td>
                  <td className="px-4 py-1">작성자2</td>
                  <td className="px-4 py-1">2025-07-02</td>
                  <td className="px-4 py-1">5</td>
                </tr>
                <tr className="border-b hover:bg-gray-100">
                  <td className="truncate px-4 py-1">게시글 제목 5</td>
                  <td className="px-4 py-1">작성자2</td>
                  <td className="px-4 py-1">2025-07-02</td>
                  <td className="px-4 py-1">5</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <hr className="border-black" />
        <div className="flex justify-center">
          <button className="w-2/4 border-black p-2 font-bold hover:bg-gray-200">
            수정
          </button>
          <button className="w-2/4 border-l border-black p-2 font-bold hover:bg-gray-200">
            닫기
          </button>
        </div>
      </div>
    </div>
  );
};
export default BoardDetailModal;
