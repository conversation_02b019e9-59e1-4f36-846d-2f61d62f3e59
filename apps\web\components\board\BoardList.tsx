"use client";

import { fetcher } from "@geon-query/model/utils/fetcher";
import { useAppQuery } from "@geon-query/react-query";
import { useRouter, useSearchParams } from "next/navigation";
import React, { useEffect, useState } from "react";

import { ApiResponse, Paginated } from "@/types/response";

import BoardDetailModal from "./BoardDetailModal";
import BoardSearchFilter from "./BoardSearchFilter";
import Pagination from "./Pagination";
import { useSearchBoardStore } from "./stores/searchParamStore";
import { BoardItem } from "./types";

/** API 응답 결과 type */
type BoardItemList = ApiResponse<BoardItem[]> & Paginated;

const BoardList = () => {
  // 모달 핸들링
  const router = useRouter();
  const searchParams = useSearchParams();
  const selectedBoardId = searchParams.get("boardId");

  /** zustand */
  // 페이징, 검색 저장소
  const { page, appliedSearchParams } = useSearchBoardStore();

  /** useState */
  // 응답 결과 데이터
  const [boardList, setBoardList] = useState<BoardItem[] | undefined>();
  // 응답 결과 페이지네이션 정보
  const [paginationInfo, setPaginationInfo] = useState<Paginated>();

  /** useQuery */
  const { data, isLoading } = useAppQuery<BoardItemList>({
    /** zustand 로 Pagination, SearchFilter 컴포넌트로부터 발생하는 파라미터 page, appliedSearchParams 구독
     * -> 갱신 시, 자동으로 refetch 수행.
     */
    queryKey: ["boardList", page, appliedSearchParams],

    queryFn: () =>
      fetcher.get("/api/board", {
        page,
        ...appliedSearchParams,
      }),
  });

  /** 모달 Handler Area - 사용하면 공통으로*/
  const openDetailModal = (boardId: string) => {
    router.push(`/sample/board/list?boardId=${boardId}`);
  };

  const closeDetailModal = () => {
    router.push(`/sample/board/list`);
  };

  /** useEffect */
  // 데이터 세팅
  useEffect(() => {
    if (data && !isLoading) {
      setBoardList(data.content);
      setPaginationInfo({
        pageNumber: data.pageNumber,
        recordCountPerPage: data.recordCountPerPage,
        totalRecordCount: data.totalRecordCount,
        totalPageCount: data.totalPageCount,
        startPage: data.startPage,
        endPage: data.endPage,
      });
    }
  }, [data, isLoading]);

  return (
    <div className="flex h-screen flex-col">
      <div>
        <BoardSearchFilter useSearchFilterStore={useSearchBoardStore} />
        <hr className="border-t border-black" />
      </div>
      <div className="h-0 grow overflow-auto bg-red-300">
        {isLoading ? (
          <p className="p-4 text-center text-gray-700">검색 중입니다...</p>
        ) : boardList && boardList.length === 0 ? (
          <p className="p-4 text-center text-gray-700">검색 결과가 없습니다.</p>
        ) : (
          <>
            <ul>
              {boardList?.map((item: BoardItem) => (
                <li
                  key={item.boardId}
                  className="max-h-[155px] min-h-[110px] border border-black p-2"
                  onClick={() => {
                    openDetailModal(item.boardId);
                  }}
                >
                  <p className="text-xs text-gray-500">{item.boardType}</p>
                  <div className="mt-1 flex w-full items-center gap-4">
                    <p className="font-bold">{item.title} </p>
                    <p>🗨️{item.commentCount}</p>
                  </div>
                  <p className="mt-1 max-h-[60px] overflow-hidden text-ellipsis break-words text-sm">
                    {item.content}
                  </p>
                  <div className="mt-1 flex w-full gap-4 text-sm">
                    <p className="text-gray-500">[{item.writer}]</p>
                    <p className="text-gray-500">
                      [{item.cngDate?.replace("T", " ").slice(0, 16)}]
                    </p>
                  </div>
                </li>
              ))}
            </ul>
            {paginationInfo && paginationInfo?.totalPageCount !== 0 && (
              <Pagination
                paginationInfo={paginationInfo}
                usePaginationStore={useSearchBoardStore}
              />
            )}
            {selectedBoardId && (
              <BoardDetailModal
                boardId={selectedBoardId as string}
                closeDetailModal={closeDetailModal}
              />
            )}
          </>
        )}
      </div>
    </div>
  );
};

export default BoardList;
