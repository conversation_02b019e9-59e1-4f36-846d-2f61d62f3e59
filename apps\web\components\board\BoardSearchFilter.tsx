"use client";

import React, { useState } from "react";

import { SearchBoardParams } from "./stores/searchParamStore";

/** 임시 날짜 선택을 위한 메서드 */
const getDateValue = (label: string): string => {
  const today = new Date();
  const target = new Date(today);

  switch (label) {
    case "오늘":
      break;
    case "어제":
      target.setDate(today.getDate() - 1);
      break;
    case "일주일":
      target.setDate(today.getDate() - 7);
      break;
    case "한달":
      target.setMonth(today.getMonth() - 1);
      break;
    case "일년":
      target.setFullYear(today.getFullYear() - 1);
      break;
    case "전체":
      break;
    default:
      break;
  }
  return target.toISOString().slice(0, 10);
};

type BoardSearchFilterProps = {
  useSearchFilterStore: () => {
    setSearchParams: (params: Partial<SearchBoardParams>) => void;
    applySearchParams: () => void;
  };
};

const BoardSearchFilter = ({
  useSearchFilterStore,
}: BoardSearchFilterProps) => {
  const { setSearchParams, applySearchParams } = useSearchFilterStore();
  /** ### 검색어 종류 */
  const [keywordType, setKeywordType] = useState<string>("");

  /** ### 검색어 */
  const [keyword, setKeyword] = useState<string>("");

  /** ### 기간선택 필터 더 보기  */
  const [showMoreFilter, setShowMoreFilter] = useState<boolean>(false);

  /** ### 기간선택 라디오버튼 값 */
  const [selectedDateRange, setSelectedDataRange] = useState<string>("");

  /** ### 기간선택 중 직접선택 , DatePicker의 disable 여부  */
  const [datePickerDisable, setDatePickerDisable] = useState<boolean>(true);

  /** ### 기간선택 중 직접선택 시, from Date */
  const [fromDate, setFromDate] = useState<string>("");

  /** ### 기간선택 중 직접선택 시, .to Date */
  const [toDate, setToDate] = useState<string>("");

  /** ### 기간선택 onChangeHandler */
  const onChangeDataRangeRadio = (
    e: React.ChangeEvent<HTMLInputElement>,
    label: string,
  ) => {
    const value = e.target.value; // YYYY-MM-DD
    setSelectedDataRange(label); // UI 표시용

    if (label === "직접선택") {
      setDatePickerDisable(false);
    } else if (label === "전체") {
      setDatePickerDisable(true);
      setSearchParams({
        searchDateType: "regDate",
        searchFromDate: "",
        searchToDate: "",
      });
    } else {
      setDatePickerDisable(true);
      setSearchParams({
        searchDateType: "regDate",
        searchFromDate: value,
        searchToDate: new Date().toISOString().slice(0, 10), // 오늘까지로 설정
      });
    }
  };

  const onChangeFromDate = (e: React.ChangeEvent<HTMLInputElement>) => {
    setFromDate(e.target.value);
    setSearchParams({
      searchFromDate: e.target.value,
    });
  };

  const onChangeToDate = (e: React.ChangeEvent<HTMLInputElement>) => {
    setToDate(e.target.value);
    setSearchParams({
      searchToDate: e.target.value,
    });
  };

  /** ### 검색 버튼 onClickHandler */
  const onClickSearchButton = () => {
    if (keywordType === "title") {
      // zustand 저장소의 검색어 타입 업데이트
      // zustand 저장소의 임시 검색어 업데이트
      setSearchParams({ searchKeywordType: "title" });
      setSearchParams({ searchKeyword: keyword });
    } else if (keywordType === "content") {
      setSearchParams({ searchKeywordType: "content" });
      setSearchParams({ searchKeyword: keyword });
    } else if (keywordType === "writer") {
      setSearchParams({ searchKeywordType: "writer" });
      setSearchParams({ searchKeyword: keyword });
    } else {
      setSearchParams({ searchKeywordType: "all" });
      setSearchParams({ searchKeyword: keyword });
    }

    // 버튼을 누르므로써 실제 useQuery 에서 감지하고 있는 zustand 저장소의 검색어를 업데이트.
    applySearchParams();
  };

  return (
    <div className="relative w-full bg-blue-300 p-2">
      <div className="flex h-2/3 w-full items-center gap-4">
        <select
          className="h-10 w-[14%] text-center"
          onChange={(e) => setKeywordType(e.target.value)}
        >
          <option value="all">통합검색</option>
          <option value="title">제목</option>
          <option value="content">내용</option>
          <option value="writer">작성자</option>
        </select>
        <input
          className="h-10 w-[76%]"
          value={keyword}
          onChange={(e) => setKeyword(e.target.value)}
          onKeyDown={(e) => {
            if (e.key === "Enter") {
              e.preventDefault();
              onClickSearchButton();
            }
          }}
        ></input>
        <button
          className="h-10 w-[10%] bg-white"
          onClick={() => onClickSearchButton()}
        >
          검색
        </button>
      </div>
      <div className="flex h-1/3 items-center justify-center">
        <div className="mt-2 border-b border-black">
          <button
            className="cursor-pointer text-xs font-bold"
            onClick={() => setShowMoreFilter((prev) => !prev)}
          >
            기간 선택 {showMoreFilter ? "▲" : "▼"}
          </button>
        </div>
        {showMoreFilter && (
          <div className="absolute top-full z-50 flex w-full flex-col items-start justify-start gap-4 bg-blue-300 px-2 py-4 md:flex-row md:items-center md:justify-center">
            <div className="flex w-full items-center justify-start border-r md:w-[14%] md:justify-center">
              <p className="text-center font-bold ">🕒 기간</p>
            </div>

            <div className="flex w-full flex-col gap-3 md:w-[86%] md:flex-row md:flex-wrap md:justify-between">
              {["전체", "오늘", "어제", "일주일", "한달", "일년"].map(
                (label) => {
                  const value = getDateValue(label); // YYYY-MM-DD로 변환
                  return (
                    <label
                      key={label}
                      className="flex w-full items-center gap-2 md:w-auto"
                    >
                      <input
                        type="radio"
                        name="dateRange"
                        value={value}
                        checked={selectedDateRange === label}
                        onChange={(e) => onChangeDataRangeRadio(e, label)}
                        className="size-4 text-blue-600"
                      />
                      <span className="text-sm">{label}</span>
                    </label>
                  );
                },
              )}

              <div className="flex w-full flex-col items-start gap-2 md:w-auto md:flex-row md:items-center">
                <label className="flex items-center gap-2">
                  <input
                    type="radio"
                    name="dateRange"
                    value={"직접선택"}
                    checked={selectedDateRange === "직접선택"}
                    onChange={(e) => onChangeDataRangeRadio(e, "직접선택")}
                    className="size-4 text-blue-600"
                  />
                  <span className="text-sm">직접선택</span>
                </label>
                <div className="flex items-center">
                  <input
                    type="date"
                    className="mr-2 border p-1 text-center text-sm"
                    disabled={datePickerDisable}
                    value={fromDate}
                    onChange={(e) => onChangeFromDate(e)}
                  />
                  ~
                  <input
                    type="date"
                    className="ml-2 border p-1 text-center text-sm"
                    disabled={datePickerDisable}
                    value={toDate}
                    onChange={(e) => onChangeToDate(e)}
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default BoardSearchFilter;
