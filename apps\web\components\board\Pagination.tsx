import React from "react";

import { Paginated } from "@/types/response";

/** 페이지 네이션을 공통으로 사용하기 위해 페이지네이션 정보와 저장소에 저장할 실제 페이지를 Props 로 받는다. */
type PaginationProps = {
  paginationInfo: Paginated;
  usePaginationStore: () => { page: number; setPage: (page: number) => void };
};

const Pagination = ({
  paginationInfo,
  usePaginationStore,
}: PaginationProps) => {
  const { pageNumber, startPage, endPage, totalPageCount } = paginationInfo;

  const { page, setPage } = usePaginationStore();

  const pageNumbers = [];
  for (let i = startPage; i <= endPage; i++) {
    pageNumbers.push(i);
  }

  return (
    <div>
      <div className="flex h-[50px] items-center justify-center border border-blue-700">
        <ul className="flex items-center justify-center gap-2 text-sm">
          {page > 1 ? (
            <li>
              <button
                onClick={() => setPage(Math.max(1, startPage - 1))}
                className={
                  "flex size-8 items-center justify-center rounded-md border border-gray-300 transition hover:bg-gray-100"
                }
              >
                &lt;
              </button>
            </li>
          ) : (
            ""
          )}

          {pageNumbers.map((num) => (
            <li key={num}>
              <button
                onClick={() => setPage(num)}
                className={`flex size-8 items-center justify-center rounded-md border transition ${
                  num === pageNumber
                    ? "border-blue-500 bg-blue-500 font-semibold text-white shadow"
                    : "border-gray-300 hover:bg-gray-100"
                }`}
              >
                {num}
              </button>
            </li>
          ))}
          {page < totalPageCount ? (
            <li>
              <button
                onClick={() => setPage(Math.min(totalPageCount, endPage + 1))}
                className={
                  "flex size-8 items-center justify-center rounded-md border border-gray-300 transition hover:bg-gray-100"
                }
              >
                &gt;
              </button>
            </li>
          ) : (
            ""
          )}
        </ul>
      </div>
    </div>
  );
};

export default Pagination;
