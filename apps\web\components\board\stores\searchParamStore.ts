import { create } from "zustand";

/** SearchParam 공통으로 뺄 건지 */
export interface SearchBoardParams {
  searchKeywordType?: string;
  searchKeyword?: string;
  searchDateType?: string;
  searchFromDate?: string;
  searchToDate?: string;
}

export interface SearchParamState<T> {
  // 임시 검색어 저장
  searchParams: T;
  setSearchParams: (params: Partial<T>) => void;
  resetSearchParams: () => void;

  // 실제 useQuery 에서 감지할 검색어
  appliedSearchParams: T;
  applySearchParams: () => void;
  page: number;
  setPage: (page: number) => void;
  resetPage: () => void;
}

export const useSearchBoardStore = create<SearchParamState<SearchBoardParams>>(
  (set) => ({
    // 사용자 입력을 임시로 저장하는 searchParam Area
    searchParams: {},
    setSearchParams: (params) =>
      set((state) => ({
        searchParams: { ...state.searchParams, ...params },
      })),
    resetSearchParams: () => set({ searchParams: {}, page: 1 }),

    // 실제 useAppQuery 에서 감지하는 searchParamArea
    appliedSearchParams: {},
    applySearchParams: () =>
      set((state) => ({
        appliedSearchParams: { ...state.searchParams },
        page: 1,
      })),

    // useAppQuery 에서 감지하는 page Area
    page: 1,
    setPage: (page) => set({ page }),
    resetPage: () => set({ page: 1 }),
  }),
);
