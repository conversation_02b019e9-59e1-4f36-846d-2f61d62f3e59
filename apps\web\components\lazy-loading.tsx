/* eslint-disable @typescript-eslint/no-explicit-any */
"use client";

import React, { lazy, Suspense, useEffect, useState } from "react";

// =============================================================================
// SKELETON COMPONENTS
// =============================================================================

const CardSkeleton = () => (
  <div className="animate-pulse space-y-4 rounded-lg border border-gray-200 p-6">
    <div className="flex items-center justify-between">
      <div className="h-4 w-3/4 rounded bg-gray-200"></div>
      <div className="flex items-center text-xs text-purple-600">
        <div className="mr-1 size-3 animate-spin rounded-full border border-purple-600 border-t-transparent"></div>
        Loading profile... (~3s)
      </div>
    </div>
    <div className="h-3 w-1/2 rounded bg-gray-200"></div>
    <div className="space-y-2">
      <div className="h-3 rounded bg-gray-200"></div>
      <div className="h-3 w-5/6 rounded bg-gray-200"></div>
    </div>
    <div className="h-20 rounded bg-gray-200"></div>
  </div>
);

const ChartSkeleton = () => (
  <div className="animate-pulse space-y-4 rounded-lg border border-gray-200 p-6">
    <div className="flex items-center justify-between">
      <div className="h-4 w-1/3 rounded bg-gray-200"></div>
      <div className="flex items-center text-xs text-blue-600">
        <div className="mr-1 size-3 animate-spin rounded-full border border-blue-600 border-t-transparent"></div>
        Loading chart data... (~2s)
      </div>
    </div>
    <div className="h-64 rounded bg-gray-200"></div>
    <div className="flex space-x-2">
      <div className="h-3 w-16 rounded bg-gray-200"></div>
      <div className="h-3 w-16 rounded bg-gray-200"></div>
      <div className="h-3 w-16 rounded bg-gray-200"></div>
    </div>
  </div>
);

const NotificationSkeleton = () => (
  <div className="animate-pulse space-y-4 rounded-lg border border-gray-200 p-6">
    <div className="flex items-center justify-between">
      <div className="h-4 w-1/4 rounded bg-gray-200"></div>
      <div className="flex items-center text-xs text-green-600">
        <div className="mr-1 size-3 animate-spin rounded-full border border-green-600 border-t-transparent"></div>
        Loading notifications... (~1s)
      </div>
    </div>
    <div className="space-y-3">
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="flex items-center space-x-3">
          <div className="size-2 rounded-full bg-gray-200"></div>
          <div className="flex-1 space-y-1">
            <div className="h-3 w-3/4 rounded bg-gray-200"></div>
            <div className="h-2 w-1/4 rounded bg-gray-200"></div>
          </div>
        </div>
      ))}
    </div>
  </div>
);

// =============================================================================
// MOCK DATA AND UTILITIES
// =============================================================================

const mockUserData = {
  name: "John Doe",
  email: "<EMAIL>",
  avatar:
    "https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=100&h=100&fit=crop&crop=face",
  bio: "Software developer passionate about React and Next.js",
  stats: {
    posts: 42,
    followers: 1337,
    following: 289,
  },
};

const mockChartData = [
  { month: "Jan", value: 400 },
  { month: "Feb", value: 300 },
  { month: "Mar", value: 600 },
  { month: "Apr", value: 800 },
  { month: "May", value: 500 },
  { month: "Jun", value: 700 },
];

const mockNotifications = [
  { id: 1, message: "New user registered", type: "success", time: "2 min ago" },
  {
    id: 2,
    message: "Server maintenance scheduled",
    type: "warning",
    time: "1 hour ago",
  },
  { id: 3, message: "Payment received", type: "success", time: "3 hours ago" },
  {
    id: 4,
    message: "Error in payment gateway",
    type: "error",
    time: "1 day ago",
  },
];

// Different loading times for different components to demonstrate async loading
const fetchUserData = (): Promise<typeof mockUserData> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(mockUserData), 3000); // 3 seconds - heaviest component
  });
};

const fetchChartData = (): Promise<typeof mockChartData> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(mockChartData), 2000); // 2 seconds - medium load
  });
};

const fetchNotifications = (): Promise<typeof mockNotifications> => {
  return new Promise((resolve) => {
    setTimeout(() => resolve(mockNotifications), 1000); // 1 second - lightest component
  });
};

// =============================================================================
// HEAVY COMPONENTS (These will be lazy loaded)
// =============================================================================

// UserProfile component - Simulates a heavy component with data fetching
const UserProfile = () => {
  const [userData, setUserData] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [loadStartTime] = useState(Date.now());

  useEffect(() => {
    fetchUserData().then((data) => {
      setUserData(data);
      setLoading(false);
    });
  }, []);

  if (loading) {
    return <CardSkeleton />;
  }

  const loadTime = ((Date.now() - loadStartTime) / 1000).toFixed(1);

  return (
    <div className="space-y-4 rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
      <div className="flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">User Profile</h3>
        <span className="rounded bg-green-50 px-2 py-1 text-xs text-green-600">
          ✓ Loaded in {loadTime}s
        </span>
      </div>

      <div className="flex items-center space-x-4">
        {/* eslint-disable-next-line @next/next/no-img-element */}
        <img
          src={userData.avatar}
          alt={userData.name}
          className="size-16 rounded-full object-cover ring-2 ring-blue-100"
        />
        <div>
          <h3 className="text-lg font-semibold text-gray-900">
            {userData.name}
          </h3>
          <p className="text-gray-600">{userData.email}</p>
        </div>
      </div>

      <p className="text-gray-700">{userData.bio}</p>

      <div className="flex space-x-6 border-t border-gray-100 pt-4">
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {userData.stats.posts}
          </div>
          <div className="text-sm text-gray-500">Posts</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {userData.stats.followers}
          </div>
          <div className="text-sm text-gray-500">Followers</div>
        </div>
        <div className="text-center">
          <div className="text-2xl font-bold text-blue-600">
            {userData.stats.following}
          </div>
          <div className="text-sm text-gray-500">Following</div>
        </div>
      </div>
    </div>
  );
};

// Analytics component - Another heavy component
const Analytics = () => {
  const [chartData, setChartData] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadStartTime] = useState(Date.now());

  useEffect(() => {
    fetchChartData().then((data) => {
      setChartData(data);
      setLoading(false);
    });
  }, []);

  if (loading) {
    return <ChartSkeleton />;
  }

  const loadTime = ((Date.now() - loadStartTime) / 1000).toFixed(1);
  const maxValue = Math.max(...chartData.map((d: any) => d.value));

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Analytics Dashboard
        </h3>
        <span className="rounded bg-green-50 px-2 py-1 text-xs text-green-600">
          ✓ Loaded in {loadTime}s
        </span>
      </div>

      <div className="mb-4 flex h-64 items-end space-x-2">
        {chartData.map((item: any, index) => (
          <div key={index} className="flex flex-1 flex-col items-center">
            <div
              className="w-full rounded-t bg-blue-500 transition-all duration-500 ease-out"
              style={{
                height: `${(item.value / maxValue) * 200}px`,
                animationDelay: `${index * 100}ms`,
              }}
            ></div>
            <div className="mt-2 text-xs text-gray-600">{item.month}</div>
          </div>
        ))}
      </div>

      <div className="flex space-x-4 text-sm text-gray-600">
        <span className="flex items-center">
          <div className="mr-2 size-3 rounded bg-blue-500"></div>
          Monthly Views
        </span>
      </div>
    </div>
  );
};

// Notifications component - Fastest loading component
const Notifications = () => {
  const [notifications, setNotifications] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [loadStartTime] = useState(Date.now());

  useEffect(() => {
    fetchNotifications().then((data) => {
      setNotifications(data);
      setLoading(false);
    });
  }, []);

  if (loading) {
    return <NotificationSkeleton />;
  }

  const loadTime = ((Date.now() - loadStartTime) / 1000).toFixed(1);

  const getNotificationColor = (type: any) => {
    switch (type) {
      case "success":
        return "bg-green-500";
      case "warning":
        return "bg-yellow-500";
      case "error":
        return "bg-red-500";
      default:
        return "bg-blue-500";
    }
  };

  return (
    <div className="rounded-lg border border-gray-200 bg-white p-6 shadow-sm">
      <div className="mb-4 flex items-center justify-between">
        <h3 className="text-lg font-semibold text-gray-900">
          Recent Notifications
        </h3>
        <span className="rounded bg-green-50 px-2 py-1 text-xs text-green-600">
          ✓ Loaded in {loadTime}s
        </span>
      </div>

      <div className="space-y-3">
        {notifications.map((notification, index) => (
          <div
            key={notification.id}
            className="flex items-center space-x-3 rounded-lg bg-gray-50 p-3 transition-all duration-300 ease-out"
            style={{ animationDelay: `${index * 150}ms` }}
          >
            <div
              className={`size-3 rounded-full ${getNotificationColor(notification.type)}`}
            ></div>
            <div className="flex-1">
              <p className="text-sm font-medium text-gray-900">
                {notification.message}
              </p>
              <p className="text-xs text-gray-500">{notification.time}</p>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// =============================================================================
// LAZY LOADING SETUP
// =============================================================================

// Create lazy components with different module loading delays
const LazyUserProfile = lazy(
  () =>
    new Promise((resolve) => {
      setTimeout(() => {
        resolve({ default: UserProfile as unknown as never });
      }, 800); // Simulate heavy module loading
    }),
);

const LazyAnalytics = lazy(
  () =>
    new Promise((resolve) => {
      setTimeout(() => {
        resolve({ default: Analytics as unknown as never });
      }, 400); // Medium module loading
    }),
);

const LazyNotifications = lazy(
  () =>
    new Promise((resolve) => {
      setTimeout(() => {
        resolve({ default: Notifications as unknown as never });
      }, 200); // Fast module loading
    }),
);

// =============================================================================
// TIMING TRACKER COMPONENT
// =============================================================================

const LoadingTracker = ({ components }: any) => {
  const activeLoads = components.filter(
    (c: any) => c.isVisible && c.status === "loading",
  );

  if (activeLoads.length === 0) return null;

  return (
    <div className="mb-6 rounded-lg border border-blue-200 bg-blue-50 p-4">
      <h4 className="mb-2 text-sm font-semibold text-blue-900">
        Loading in Progress:
      </h4>
      <div className="space-y-2">
        {activeLoads.map((component: any) => (
          <div
            key={component.name}
            className="flex items-center justify-between text-sm"
          >
            <span className="text-blue-800">{component.name}</span>
            <div className="flex items-center space-x-2">
              <div className="size-3 animate-spin rounded-full border border-blue-600 border-t-transparent"></div>
              <span className="text-blue-600">~{component.expectedTime}s</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
};

// =============================================================================
// MAIN DEMO COMPONENT
// =============================================================================

export default function LazyLoadingDemo() {
  const [showProfile, setShowProfile] = useState(false);
  const [showAnalytics, setShowAnalytics] = useState(false);
  const [showNotifications, setShowNotifications] = useState(false);
  const [loadAllTriggered, setLoadAllTriggered] = useState(false);

  // Component status tracking
  const components = [
    {
      name: "User Profile",
      isVisible: showProfile,
      status: showProfile ? "loading" : "idle",
      expectedTime: 3.8, // Module load (0.8s) + Data fetch (3s)
      color: "purple",
    },
    {
      name: "Analytics",
      isVisible: showAnalytics,
      status: showAnalytics ? "loading" : "idle",
      expectedTime: 2.4, // Module load (0.4s) + Data fetch (2s)
      color: "blue",
    },
    {
      name: "Notifications",
      isVisible: showNotifications,
      status: showNotifications ? "loading" : "idle",
      expectedTime: 1.2, // Module load (0.2s) + Data fetch (1s)
      color: "green",
    },
  ];

  const loadAllComponents = () => {
    setLoadAllTriggered(true);
    // Staggered loading to show the effect
    setTimeout(() => setShowNotifications(true), 0);
    setTimeout(() => setShowAnalytics(true), 500);
    setTimeout(() => setShowProfile(true), 1000);
  };

  const resetAll = () => {
    setShowProfile(false);
    setShowAnalytics(false);
    setShowNotifications(false);
    setLoadAllTriggered(false);
  };

  return (
    <div className="mx-auto max-w-6xl space-y-8 p-6">
      {/* Header */}
      <div className="space-y-4 text-center">
        <h1 className="text-3xl font-bold text-gray-900">
          Lazy Loading with Different Load Times
        </h1>
        <p className="mx-auto max-w-3xl text-gray-600">
          Watch how components load independently at different speeds. Each
          component has different module loading times (bundle splitting) and
          data fetching delays to simulate real-world scenarios.
        </p>
      </div>

      {/* Loading Tracker */}
      <LoadingTracker components={components} />

      {/* Controls */}
      <div className="flex flex-wrap justify-center gap-4">
        <button
          onClick={() => setShowNotifications(!showNotifications)}
          className={`rounded-lg px-4 py-2 font-medium transition-colors ${
            showNotifications
              ? "bg-green-600 text-white hover:bg-green-700"
              : "bg-gray-200 text-gray-700 hover:bg-gray-300"
          }`}
        >
          {showNotifications ? "Hide" : "Load"} Notifications (Fast - 1.2s)
        </button>

        <button
          onClick={() => setShowAnalytics(!showAnalytics)}
          className={`rounded-lg px-4 py-2 font-medium transition-colors ${
            showAnalytics
              ? "bg-blue-600 text-white hover:bg-blue-700"
              : "bg-gray-200 text-gray-700 hover:bg-gray-300"
          }`}
        >
          {showAnalytics ? "Hide" : "Load"} Analytics (Medium - 2.4s)
        </button>

        <button
          onClick={() => setShowProfile(!showProfile)}
          className={`rounded-lg px-4 py-2 font-medium transition-colors ${
            showProfile
              ? "bg-purple-600 text-white hover:bg-purple-700"
              : "bg-gray-200 text-gray-700 hover:bg-gray-300"
          }`}
        >
          {showProfile ? "Hide" : "Load"} User Profile (Slow - 3.8s)
        </button>

        <div className="mx-2 border-l border-gray-300"></div>

        <button
          onClick={loadAllComponents}
          disabled={loadAllTriggered}
          className={`rounded-lg px-6 py-2 font-medium transition-colors ${
            loadAllTriggered
              ? "cursor-not-allowed bg-gray-300 text-gray-500"
              : "bg-orange-600 text-white hover:bg-orange-700"
          }`}
        >
          🚀 Load All (Staggered)
        </button>

        <button
          onClick={resetAll}
          className="rounded-lg bg-red-600 px-4 py-2 font-medium text-white hover:bg-red-700"
        >
          🔄 Reset All
        </button>
      </div>

      {/* Component Grid */}
      <div className="grid gap-6 lg:grid-cols-3">
        {/* Notifications - Fastest */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <div className="size-3 rounded-full bg-green-500"></div>
            <h2 className="text-lg font-semibold text-gray-800">
              Notifications (Fastest)
            </h2>
          </div>

          {showNotifications ? (
            <Suspense fallback={<NotificationSkeleton />}>
              <LazyNotifications />
            </Suspense>
          ) : (
            <div className="rounded-lg border border-gray-200 bg-gray-50 p-6">
              <p className="text-center text-sm text-gray-500">
                📱 Light component with minimal data
                <br />
                <span className="text-xs">
                  Module: 0.2s | Data: 1s | Total: ~1.2s
                </span>
              </p>
            </div>
          )}
        </div>

        {/* Analytics - Medium */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <div className="size-3 rounded-full bg-blue-500"></div>
            <h2 className="text-lg font-semibold text-gray-800">
              Analytics (Medium)
            </h2>
          </div>

          {showAnalytics ? (
            <Suspense fallback={<ChartSkeleton />}>
              <LazyAnalytics />
            </Suspense>
          ) : (
            <div className="rounded-lg border border-gray-200 bg-gray-50 p-6">
              <p className="text-center text-sm text-gray-500">
                📊 Chart component with API data
                <br />
                <span className="text-xs">
                  Module: 0.4s | Data: 2s | Total: ~2.4s
                </span>
              </p>
            </div>
          )}
        </div>

        {/* User Profile - Slowest */}
        <div className="space-y-4">
          <div className="flex items-center space-x-2">
            <div className="size-3 rounded-full bg-purple-500"></div>
            <h2 className="text-lg font-semibold text-gray-800">
              User Profile (Slowest)
            </h2>
          </div>

          {showProfile ? (
            <Suspense fallback={<CardSkeleton />}>
              <LazyUserProfile />
            </Suspense>
          ) : (
            <div className="rounded-lg border border-gray-200 bg-gray-50 p-6">
              <p className="text-center text-sm text-gray-500">
                👤 Heavy component with user data
                <br />
                <span className="text-xs">
                  Module: 0.8s | Data: 3s | Total: ~3.8s
                </span>
              </p>
            </div>
          )}
        </div>
      </div>

      {/* Timing Explanation */}
      <div className="space-y-4 rounded-lg border border-yellow-200 bg-yellow-50 p-6">
        <h3 className="text-lg font-semibold text-yellow-900">
          ⏱️ Understanding Load Times:
        </h3>
        <div className="grid gap-4 text-sm text-yellow-800 md:grid-cols-2">
          <div>
            <h4 className="mb-2 font-semibold">
              Module Loading (Bundle Splitting):
            </h4>
            <ul className="space-y-1">
              <li>
                • <span className="text-green-600">Fast (0.2s)</span> - Small
                utility components
              </li>
              <li>
                • <span className="text-blue-600">Medium (0.4s)</span> - Chart
                libraries
              </li>
              <li>
                • <span className="text-purple-600">Slow (0.8s)</span> - Heavy
                UI components
              </li>
            </ul>
          </div>
          <div>
            <h4 className="mb-2 font-semibold">Data Fetching:</h4>
            <ul className="space-y-1">
              <li>
                • <span className="text-green-600">Fast (1s)</span> -
                Cached/simple queries
              </li>
              <li>
                • <span className="text-blue-600">Medium (2s)</span> - Analytics
                processing
              </li>
              <li>
                • <span className="text-purple-600">Slow (3s)</span> - Complex
                user data
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Best Practices */}
      <div className="space-y-4 rounded-lg border border-blue-200 bg-blue-50 p-6">
        <h3 className="text-lg font-semibold text-blue-900">
          🚀 Performance Tips:
        </h3>
        <ul className="space-y-2 text-blue-800">
          <li className="flex items-start">
            <span className="mr-2 text-blue-600">1.</span>
            <span>
              <strong>Load fast components first</strong> - Show content
              immediately while heavy components load
            </span>
          </li>
          <li className="flex items-start">
            <span className="mr-2 text-blue-600">2.</span>
            <span>
              <strong>Stagger heavy loads</strong> - Don&apos;t load all
              expensive components simultaneously
            </span>
          </li>
          <li className="flex items-start">
            <span className="mr-2 text-blue-600">3.</span>
            <span>
              <strong>Show realistic skeletons</strong> - Match the actual
              component layout and indicate timing
            </span>
          </li>
          <li className="flex items-start">
            <span className="mr-2 text-blue-600">4.</span>
            <span>
              <strong>Bundle splitting</strong> - Use React.lazy() and dynamic
              imports to split code by features
            </span>
          </li>
          <li className="flex items-start">
            <span className="mr-2 text-blue-600">5.</span>
            <span>
              <strong>Preload strategically</strong> - Load components when user
              interaction is likely
            </span>
          </li>
        </ul>
      </div>
    </div>
  );
}
