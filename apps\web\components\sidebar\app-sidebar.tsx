import {
  Sidebar,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
  SidebarHeader,
} from "@geon-ui/react/primitives/sidebar";
import * as React from "react";

import NavMain from "@/components/sidebar/nav-main";

import { NavUser } from "./nav-user";
import { VersionSwitcher } from "./version-switcher";

// This is sample data.
const data = {
  versions: ["Home", "Service 1", "Service 2", "Service 3"],
  user: {
    name: "user1",
    role: "Administrator",
    avatar: "/avatars/shadcn.jpg",
  },
  items: [
    {
      id: "menu-1",
      title: "Menu 1",
      icon: "SquareTerminal",
      items: [
        { id: "menu-1-1", title: "History" },
        { id: "menu-1-2", title: "Starred" },
        { id: "menu-1-3", title: "Settings" },
      ],
    },
    {
      id: "menu-2",
      title: "Menu 2",
      icon: "Bot",
      items: [
        { id: "menu-2-1", title: "Genesis" },
        { id: "menu-2-2", title: "Explorer" },
        { id: "menu-2-3", title: "Quantum" },
      ],
    },
    {
      id: "menu-3",
      title: "Menu 3",
      icon: "BookOpen",
      items: [
        { id: "menu-3-1", title: "Introduction" },
        { id: "menu-3-2", title: "Get Started" },
        { id: "menu-3-3", title: "Tutorials" },
        { id: "menu-3-4", title: "Changelog" },
      ],
    },
  ],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
  return (
    <Sidebar {...props} collapsible="icon" className="z-50">
      <SidebarHeader>
        <VersionSwitcher
          versions={data.versions}
          defaultVersion={data.versions[0]!}
        />
      </SidebarHeader>
      <SidebarContent>
        <NavMain items={data.items} />
      </SidebarContent>
      <SidebarFooter>
        <NavUser user={data.user} />
      </SidebarFooter>
    </Sidebar>
  );
}
