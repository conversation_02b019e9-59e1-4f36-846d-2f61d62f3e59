"use client";

import {
  BasemapInfo,
  BasemapWidget,
  DEFAULT_BASE_MAPS,
} from "@geon-map/react-ui/components";
import { useState } from "react";

// ✅ baseMaps를 변수로 분리
// 3. DEFAULT_BASE_MAPS 선언

interface BasemapWidgetPakageProps {
  className?: string;
}

export default function BasemapWidgetPakage({
  className = "absolute right-4 top-4 flex flex-col gap-2",
}: BasemapWidgetPakageProps) {
  const [selected, setSelected] = useState<BasemapInfo | undefined>(
    DEFAULT_BASE_MAPS[1],
  );

  // singletonLogger.log("싱글톤 로그");
  // anotherLogger.log("인스턴스 로그");
  // singletonTestLogger.log("싱글톤 로그");
  // console.log(singletonLogger === singletonTestLogger); // false
  // console.log(singletonLogger === anotherLogger); // false

  return (
    <>
      {DEFAULT_BASE_MAPS.length > 0 && (
        <BasemapWidget
          className={className}
          onValueChange={(v: BasemapInfo) => {
            setSelected(v);
            console.log("선택:", v);
          }}
          selectBasemap={selected}
          baseMaps={DEFAULT_BASE_MAPS}
        />
      )}
    </>
  );
}
