"use client";

import { BasemapInfo } from "@geon-map/core";
import {
  Basemap,
  BasemapContent,
  BasemapItem,
  BasemapMapType,
  BasemapTrigger,
  DEFAULT_BASE_MAPS,
} from "@geon-map/react-ui/components";
import { basemapWidget as basemapConverter } from "@geon-map/react-ui/utils";
import { BasemapListRequest, defaultGeonSmtClient } from "@geon-query/model";
import { useAppQuery } from "@geon-query/react-query";
import { cn } from "@geon-ui/react/lib/utils";
import { MapIcon } from "lucide-react";
import { useEffect, useState } from "react";

interface BasemapWidgetUseProps {
  className?: string;
}
export default function BasemapWidgetUse({
  className = "absolute right-4 top-4 flex flex-col gap-2",
}: BasemapWidgetUseProps) {
  const [value, onValueChange] = useState<BasemapInfo | undefined>(
    DEFAULT_BASE_MAPS[1],
  );
  const [basemapWidgetList, setBasemapWidgetList] = useState<
    BasemapMapType | []
  >([]);
  const [params, setParams] = useState<BasemapListRequest>({
    pageIndex: 1,
    pageSize: 100,
    bcrnMapClCode: "01",
    // 필요시 추가: bcrnMapNm: "기본지도"
  });
  const { data, isLoading } = useAppQuery({
    queryKey: ["basemapList", params],
    queryFn: () => defaultGeonSmtClient.basemap.list(params),
  });
  useEffect(() => {
    if (!isLoading && data?.result) {
      const converter = basemapConverter(data);
      console.log("converter", converter);
      setBasemapWidgetList(converter);
    }
  }, [isLoading, data]);

  return (
    <Basemap
      basemapInfo={value}
      onValueChange={onValueChange}
      className={className}
    >
      <BasemapTrigger className="bg-white/70 hover:bg-white dark:bg-zinc-800/90 dark:hover:bg-zinc-800">
        <MapIcon />
      </BasemapTrigger>
      <BasemapContent>
        {basemapWidgetList.map((map, index) => (
          <BasemapItem key={index} basemapInfo={map}>
            <div
              className={cn(
                "flex items-center gap-3 transition-colors duration-200",
                // value === key
                //   ? "bg-white/80 hover:bg-white"
                //   : "bg-white/60 hover:bg-white",
              )}
            >
              <div className="rounded-lg p-1.5">
                <img
                  src={map["base64"]}
                  alt={map.bcrnMapNm}
                  className="h-[50px] w-[50px] object-contain"
                />
              </div>
              <div className="flex flex-col items-start gap-0.5">
                <span className="font-medium">{map.bcrnMapNm}</span>
                <span className="text-[11px] leading-tight">
                  {map.bcrnMapClCodeNm}
                </span>
              </div>
            </div>
          </BasemapItem>
        ))}
      </BasemapContent>
    </Basemap>
  );
}
