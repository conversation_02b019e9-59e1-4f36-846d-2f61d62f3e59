import {
  type LayerDetectionResult,
  type LayerFeatureInfo,
  Popup,
  STYLE_PRESETS,
  useEvent,
  useFeatureActions,
  useLayer,
} from "@geon-map/react-odf";
import React, { useCallback, useEffect, useRef, useState } from "react";

/**
 * 컨텍스트 메뉴 정보 (앱 레이어에서 관리)
 */
interface ContextMenuInfo {
  pixel: [number, number];
  position: [number, number];
  feature: LayerFeatureInfo | null;
  visible: boolean;
  menuType: "feature" | "map" | "draw" | "measure";
}

export const DrawContextMenu: React.FC = () => {
  // 새로운 아키텍처: 앱 레이어에서 상태 관리
  const [selectedFeature, setSelectedFeature] =
    useState<LayerFeatureInfo | null>(null);
  const [contextMenuInfo, setContextMenuInfo] = useState<ContextMenuInfo>({
    pixel: [0, 0],
    position: [0, 0],
    feature: null,
    visible: false,
    menuType: "map",
  });

  // ✅ 훅들은 컴포넌트 최상위에서 호출
  const { registerLayerContextMenu } = useEvent();
  const { drawLayer, measureLayer } = useLayer();
  const {
    deleteFeature,
    changeFeatureStyle,
    isReady: featureActionsReady,
  } = useFeatureActions();

  const menuRef = useRef<HTMLDivElement>(null);

  /**
   * 컨텍스트 메뉴 숨기기
   */
  const hideContextMenu = useCallback(() => {
    setContextMenuInfo((prev) => ({ ...prev, visible: false }));
    setSelectedFeature(null);
  }, []);

  /**
   * ✅ 새로운 아키텍처: 간단한 피처 삭제 (Core + Store 동시 처리)
   */
  const deleteSelectedFeature = useCallback(() => {
    if (!selectedFeature || !featureActionsReady) return;

    try {
      const success = deleteFeature(selectedFeature.feature);

      if (success) {
        hideContextMenu();
      } else {
        console.error("❌ Feature 삭제 실패");
      }
    } catch (error) {
      console.error("Error deleting feature:", error);
    }
  }, [selectedFeature, featureActionsReady, deleteFeature, hideContextMenu]);

  // ✅ 한 번만 실행되는 초기화 로직
  useEffect(() => {
    // 레이어가 준비되지 않았으면 건너뛰기
    if (!drawLayer?.odfLayer || !measureLayer?.odfLayer) {
      return;
    }

    console.log("drawLayer, measureLayer:", drawLayer, measureLayer);

    registerLayerContextMenu(
      [
        { layer: drawLayer.odfLayer, layerType: "draw", priority: 1 },
        { layer: measureLayer.odfLayer, layerType: "measure", priority: 0 },
      ],
      (result: LayerDetectionResult) => {
        if (result.layerFeature) {
          console.log("✅ Feature 감지됨:", result.layerFeature);

          // 메뉴 타입 결정
          let menuType: "feature" | "map" | "draw" | "measure" = "feature";
          if (result.layerFeature.layerType === "draw") menuType = "draw";
          else if (result.layerFeature.layerType === "measure")
            menuType = "measure";

          // 선택된 feature와 컨텍스트 메뉴 정보 설정
          setSelectedFeature(result.layerFeature);
          setContextMenuInfo({
            pixel: result.pixel,
            position: result.position,
            feature: result.layerFeature,
            visible: true,
            menuType,
          });
        } else {
          // 빈 공간 우클릭 시 컨텍스트 메뉴 숨기기
          console.log("빈 공간 우클릭:", result.pixel, result.position);
          setContextMenuInfo((prev) => ({ ...prev, visible: false }));
          setSelectedFeature(null);
        }
      },
    );
  }, []); // ✅ 의존성 배열 완전 제거 - 매번 실행되지만 cleanupRef.current로 중복 방지

  // 메뉴 외부 클릭 시 닫기
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        hideContextMenu();
      }
    };

    if (contextMenuInfo?.visible) {
      document.addEventListener("mousedown", handleClickOutside);
      return () => {
        document.removeEventListener("mousedown", handleClickOutside);
      };
    }
  }, [contextMenuInfo?.visible, hideContextMenu]);

  // ESC 키로 메뉴 닫기
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        hideContextMenu();
      }
    };

    if (contextMenuInfo?.visible) {
      document.addEventListener("keydown", handleKeyDown);
      return () => {
        document.removeEventListener("keydown", handleKeyDown);
      };
    }
  }, [contextMenuInfo?.visible, hideContextMenu]);

  const handleStyleChange = useCallback(
    (color: string) => {
      if (!selectedFeature || !featureActionsReady) return;

      try {
        // ✅ STYLE_PRESETS 사용하거나 직접 스타일 정의
        const styleOptions = STYLE_PRESETS[
          color as keyof typeof STYLE_PRESETS
        ] || {
          fill: { color: [255, 0, 0, 1] },
          stroke: { color: [255, 0, 0, 0.8], width: 2 },
        };

        // ✅ useFeatureActions 훅이 Core 메서드를 사용해 스타일 변경
        const success = changeFeatureStyle(
          selectedFeature.feature,
          styleOptions,
        );

        if (success) {
          console.log(
            `✅ 스타일 변경 완료: ${color} for feature:`,
            selectedFeature.featureId,
          );
          hideContextMenu();
        } else {
          console.error("❌ 스타일 변경 실패");
        }
      } catch (error) {
        console.error("Error changing feature style:", error);
      }
    },
    [selectedFeature, featureActionsReady, changeFeatureStyle, hideContextMenu],
  );

  const handleRedStyle = useCallback(
    () => handleStyleChange("red"),
    [handleStyleChange],
  );
  const handleBlueStyle = useCallback(
    () => handleStyleChange("blue"),
    [handleStyleChange],
  );
  const handleGreenStyle = useCallback(
    () => handleStyleChange("green"),
    [handleStyleChange],
  );

  return (
    <Popup
      visible={contextMenuInfo?.visible || false}
      position={contextMenuInfo?.position || [0, 0]}
      positioning="top-left"
      offset={[10, -10]}
      popupKey="draw-context-menu"
    >
      <div
        ref={menuRef}
        className="min-w-40 overflow-hidden rounded-lg border border-gray-300 bg-white shadow-lg"
      >
        <div className="border-b border-gray-200 px-3 py-2 text-xs font-medium text-gray-600">
          {selectedFeature?.featureType} 편집
        </div>

        {/* 스타일 섹션 */}
        <div className="py-1">
          <div className="px-3 py-1 text-xs font-bold uppercase text-gray-500">
            스타일
          </div>
          <button
            onClick={handleRedStyle}
            className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-100"
          >
            🔴 빨간색
          </button>
          <button
            onClick={handleBlueStyle}
            className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-100"
          >
            🔵 파란색
          </button>
          <button
            onClick={handleGreenStyle}
            className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm hover:bg-gray-100"
          >
            🟢 초록색
          </button>
        </div>

        {/* 구분선 */}
        <div className="mx-2 h-px bg-gray-200" />

        {/* 액션 섹션 */}
        <div className="py-1">
          <button
            onClick={deleteSelectedFeature}
            className="flex w-full items-center gap-2 px-3 py-2 text-left text-sm text-red-600 hover:bg-red-50"
          >
            🗑️ 삭제
          </button>
        </div>
      </div>
    </Popup>
  );
};
