"use client";

import { useDraw } from "@geon-map/react-odf";
import React from "react";

import { DrawContextMenu } from "./draw-context-menu";
import { HeadlessDrawingWidget } from "./headless-drawing-widget";

export default function DrawWidgetPakage() {
  // 새로운 Provider 기반 Hook 사용
  const { isReady, error } = useDraw({
    enableSelection: true,
    enableContextMenu: true,
    onFeatureDelete: (feature) => {
      console.log("Feature deleted:", feature);
    },
  });

  if (error) {
    return <div>Error: {error.message}</div>;
  }

  if (!isReady) {
    return <div>Loading draw tools...</div>;
  }

  return (
    <>
      {/* 그리기 위젯 + 우클릭 컨텍스트 메뉴 */}
      <HeadlessDrawingWidget />
      <DrawContextMenu />
    </>
  );
}
