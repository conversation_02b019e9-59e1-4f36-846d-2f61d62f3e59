"use client";

import { DrawingMode } from "@geon-map/core";
import { useDraw } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@geon-ui/react/primitives/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@geon-ui/react/primitives/tooltip";
import {
  AreaChart,
  ChevronDown,
  Circle,
  Edit3,
  MapPin,
  Minus,
  Ruler,
  Square,
  Target,
  Triangle,
  X,
} from "lucide-react";
import React from "react";

// 도구 설정
const DRAWING_TOOLS = [
  {
    id: "point",
    mode: "point" as DrawingMode,
    label: "포인트",
    icon: MapPin,
    description: "지점을 표시합니다",
    color: "text-blue-500",
  },
  {
    id: "lineString",
    mode: "lineString" as DrawingMode,
    label: "선",
    icon: Minus,
    description: "선을 그립니다",
    color: "text-green-500",
  },
  {
    id: "polygon",
    mode: "polygon" as DrawingMode,
    label: "다각형",
    icon: Triangle,
    description: "다각형을 그립니다",
    color: "text-purple-500",
  },
  {
    id: "box",
    mode: "box" as DrawingMode,
    label: "사각형",
    icon: Square,
    description: "사각형을 그립니다",
    color: "text-orange-500",
  },
  {
    id: "circle",
    mode: "circle" as DrawingMode,
    label: "원",
    icon: Circle,
    description: "원을 그립니다",
    color: "text-pink-500",
  },
];

const MEASUREMENT_TOOLS = [
  {
    id: "measure-distance",
    mode: "measure-distance" as DrawingMode,
    label: "거리 측정",
    icon: Ruler,
    description: "두 지점 간의 거리를 측정합니다",
    color: "text-cyan-500",
  },
  {
    id: "measure-area",
    mode: "measure-area" as DrawingMode,
    label: "면적 측정",
    icon: AreaChart,
    description: "영역의 면적을 측정합니다",
    color: "text-emerald-500",
  },
  {
    id: "measure-round",
    mode: "measure-round" as DrawingMode,
    label: "둘레 측정",
    icon: Circle,
    description: "둘레를 측정합니다",
    color: "text-yellow-500",
  },
  {
    id: "measure-spot",
    mode: "measure-spot" as DrawingMode,
    label: "지점 측정",
    icon: Target,
    description: "특정 지점을 측정합니다",
    color: "text-red-500",
  },
];

interface HeadlessDrawingWidgetProps {
  className?: string;
}

export function HeadlessDrawingWidget({
  className,
}: HeadlessDrawingWidgetProps) {
  const { startDrawing, stopDrawing, drawMode, isDrawing } = useDraw();

  const drawingMode = drawMode;

  // 도구 클릭 핸들러
  const handleToolClick = (mode: DrawingMode) => {
    startDrawing(mode);
  };

  // 활성 도구 찾기
  const activeDrawingTool = DRAWING_TOOLS.find(
    (tool) => tool.mode === drawingMode,
  );
  const activeMeasurementTool = MEASUREMENT_TOOLS.find(
    (tool) => tool.mode === drawingMode,
  );

  // 드롭다운 메뉴 아이템 렌더링
  const renderMenuItem = (tool: (typeof DRAWING_TOOLS)[0]) => {
    const Icon = tool.icon;
    const isActive = drawingMode === tool.mode;

    return (
      <DropdownMenuItem
        key={tool.id}
        onClick={() => handleToolClick(tool.mode)}
        className={cn(
          "hover:bg-accent/50 cursor-pointer gap-3 p-2.5 transition-all duration-200",
          isActive && "bg-accent text-accent-foreground",
        )}
      >
        <Icon className={cn("h-4 w-4 flex-shrink-0", tool.color)} />
        <div className="flex min-w-0 flex-1 flex-col gap-0.5">
          <span className="text-sm font-medium">{tool.label}</span>
          <span className="text-muted-foreground text-xs leading-tight">
            {tool.description}
          </span>
        </div>
        {isActive && (
          <div className="ml-2 h-2 w-2 flex-shrink-0 animate-pulse rounded-full bg-green-500" />
        )}
      </DropdownMenuItem>
    );
  };

  return (
    <div
      className={cn(
        "fixed left-1/2 top-6 z-50 -translate-x-1/2 transform",
        className,
      )}
    >
      <div className="bg-background/95 supports-[backdrop-filter]:bg-background/60 flex items-center gap-1.5 rounded-lg border p-1.5 shadow-lg backdrop-blur">
        {/* 그리기 도구 드롭다운 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className={cn(
                "h-9 gap-2 px-2.5 transition-all duration-200",
                (activeDrawingTool || isDrawing) &&
                  "bg-primary text-primary-foreground",
              )}
            >
              {activeDrawingTool ? (
                <>
                  <activeDrawingTool.icon className="h-4 w-4" />
                  <span className="hidden sm:inline">
                    {activeDrawingTool.label}
                  </span>
                </>
              ) : (
                <>
                  <Edit3 className="h-4 w-4" />
                  <span className="hidden sm:inline">그리기</span>
                </>
              )}
              <ChevronDown className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuLabel className="flex items-center gap-2">
              <Edit3 className="h-4 w-4" />
              그리기 도구
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {DRAWING_TOOLS.map(renderMenuItem)}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* 측정 도구 드롭다운 */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              size="sm"
              className={cn(
                "h-9 gap-2 px-2.5 transition-all duration-200",
                (activeMeasurementTool || isDrawing) &&
                  "bg-primary text-primary-foreground",
              )}
            >
              {activeMeasurementTool ? (
                <>
                  <activeMeasurementTool.icon className="h-4 w-4" />
                  <span className="hidden sm:inline">
                    {activeMeasurementTool.label}
                  </span>
                </>
              ) : (
                <>
                  <Ruler className="h-4 w-4" />
                  <span className="hidden sm:inline">측정</span>
                </>
              )}
              <ChevronDown className="h-3 w-3" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-56">
            <DropdownMenuLabel className="flex items-center gap-2">
              <Ruler className="h-4 w-4" />
              측정 도구
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            {MEASUREMENT_TOOLS.map(renderMenuItem)}
          </DropdownMenuContent>
        </DropdownMenu>

        {/* 액션 버튼들 */}
        <div className="flex items-center gap-1">
          {isDrawing && (
            <Tooltip>
              <TooltipTrigger asChild>
                <Button
                  variant="outline"
                  size="sm"
                  className="h-7 w-7 p-0"
                  onClick={stopDrawing}
                >
                  <X className="h-4 w-4" />
                </Button>
              </TooltipTrigger>
              <TooltipContent>그리기 중단</TooltipContent>
            </Tooltip>
          )}
        </div>
      </div>
    </div>
  );
}
