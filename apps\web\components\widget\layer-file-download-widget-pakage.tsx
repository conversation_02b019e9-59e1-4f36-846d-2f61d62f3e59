"use-client";
import { LayerFileDownloadWidget } from "@geon-map/react-ui/components";
import { useLayerFileDownload } from "@geon-map/react-ui/hooks";
import { LayerFileDownloadParams } from "@geon-map/react-ui/types";
import { BASE_URL, crtfckey } from "@geon-query/model";
import { createGeonAnalysisClient } from "@geon-query/model/restapi/analysis";
interface LayerFileDownloadPackageProps {
  layerFileDownloadInfo?: LayerFileDownloadParams;
  className?: string;
  buttonName?: string | "";
}

export default function LayerFileDownloadPackage({
  layerFileDownloadInfo,
  className = "",
  buttonName = "",
}: LayerFileDownloadPackageProps) {
  const apiClient = createGeonAnalysisClient({
    baseUrl: BASE_URL,
    crtfckey: crtfckey,
  });

  const { handleLayerFileDownload, isLoading } = useLayerFileDownload({
    apiClient,
    apiType: "geon",
  });

  return (
    <LayerFileDownloadWidget
      layerFileDownloadInfo={layerFileDownloadInfo}
      onClickLayerFileDownload={handleLayerFileDownload}
      isLoading={isLoading}
      className={className}
      buttonName={buttonName}
    />
  );
}
