"use client";

import { useEvent } from "@geon-map/react-odf";
import React, { useState } from "react";

export const MouseCoordDisplay = () => {
  const [enabled, setEnabled] = useState(false);
  const [coord, setCoord] = useState<{ x: number; y: number } | null>(null);
  const { registerListener } = useEvent();

  React.useEffect(() => {
    if (!enabled) {
      setCoord(null);
      return;
    }

    const map = (globalThis as any).odf?.map?.getMap?.();
    if (!map) return;

    const cleanup = registerListener(map, "pointermove", (event: any) => {
      try {
        const coordinate = map.getCoordinateFromPixel(event.pixel);
        if (coordinate) setCoord({ x: coordinate[0], y: coordinate[1] });
      } catch (e) {
        console.warn("좌표 업데이트 실패:", e);
      }
    });

    return cleanup;
  }, [enabled, registerListener]);

  return (
    <div className="w-fit rounded border p-2 text-sm">
      <button
        className="mb-2 rounded border bg-gray-100 px-3 py-1 hover:bg-gray-200"
        onClick={() => setEnabled((prev) => !prev)}
      >
        {enabled ? "좌표 표시 끄기" : "좌표 표시 켜기"}
      </button>

      {/* 좌표 표시 중일 때만 출력 */}
      {enabled && (
        <div>
          {coord ? (
            <>
              X: {coord.x}, Y: {coord.y}
            </>
          ) : (
            <>좌표를 불러오는 중...</>
          )}
        </div>
      )}
    </div>
  );
};
