"use client";

import { SidebarProvider, useSidebar } from "@geon-ui/react/primitives/sidebar";
import type { ReactNode } from "react";
import { createContext, useContext, useMemo, useState } from "react";

type AppSidebarContextProps = {
  // outer sidebar state
  outer: ReturnType<typeof useSidebar>;

  // inner sidebar state
  innerOpen: boolean;
  setInnerOpen: (open: boolean) => void;

  // navigation state
  active: string | null;
  setActive: (menu: string | null) => void;
  content: string | null;
  setContent: (content: string | null) => void;
};

const AppSidebarContext = createContext<AppSidebarContextProps | null>(null);

export function useAppSidebar() {
  const context = useContext(AppSidebarContext);
  if (!context) {
    throw new Error("useAppSidebar must be used within a AppSidebarProvider");
  }
  return context;
}

export function InnerSidebarProvider({ children }: { children: ReactNode }) {
  const { innerOpen, setInnerOpen } = useAppSidebar();

  return (
    <SidebarProvider open={innerOpen} onOpenChange={setInnerOpen}>
      {children}
    </SidebarProvider>
  );
}

export function AppSidebarProvider({ children }: { children: ReactNode }) {
  const outer = useSidebar();
  const [innerOpen, setInnerOpen] = useState(false);
  const [active, setActive] = useState<string | null>(null);
  const [content, setContent] = useState<string | null>(null);

  const contextValue = useMemo(
    () => ({
      outer,
      innerOpen,
      setInnerOpen,
      active,
      setActive,
      content,
      setContent,
    }),
    [outer, innerOpen, setInnerOpen, active, setActive, content, setContent],
  );

  return (
    <AppSidebarContext.Provider value={contextValue}>
      {children}
    </AppSidebarContext.Provider>
  );
}
