// For more info, see https://github.com/storybookjs/eslint-plugin-storybook#configuration-flat-config-format
import baseConfig from "@config/eslint/base";
import nextJsConfig from "@config/eslint/next-js";
import storybook from "eslint-plugin-storybook";

/** @type {import("eslint").Linter.Config[]} */
export default [
  ...baseConfig,
  ...nextJsConfig,
  ...storybook.configs["flat/recommended"],
];
