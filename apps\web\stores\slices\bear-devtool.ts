import { create } from "zustand";
import { devtools } from "zustand/middleware";

import { logger } from "@/stores/middleware/logger";

interface BearState {
  bears: number;
  increase: (by: number) => void;
  decrease: (by: number) => void;
}

/**
 * Redux Devtool 에서 표시할 수 있는 형태의 zustand store 예시
 */
const useReduxBearStore = create<BearState>()(
  devtools(
    logger(
      (set) => ({
        bears: 0,
        increase: (by) =>
          set((state) => ({ bears: state.bears + by }), false, {
            type: "bear/increase",
          }),
        decrease: (by) =>
          set((state) => ({ bears: state.bears - by }), false, {
            type: "bear/decrease",
          }),
      }),
      "bear-store",
    ),
    { name: "Bear Counter Store" },
  ),
);

export default useReduxBearStore;
