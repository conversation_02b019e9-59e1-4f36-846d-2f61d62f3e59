import { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import ContextMenuDemoSource from "!!raw-loader!./demo";
// @ts-expect-error Force loader addon
import NestedContextMenuSource from "!!raw-loader!./nested";

import { ContextMenuDemo } from "./demo";
import NestedContextMenu from "./nested";

const meta = {
  title: "Shadcn/ContextMenu",
  parameters: {
    layout: "centered",
  },
  tags: ["autodocs"],
} satisfies Meta;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: ContextMenuDemoSource,
      },
    },
  },
  render: () => <ContextMenuDemo />,
};

export const Nested: Story = {
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: NestedContextMenuSource,
      },
    },
  },
  render: () => <NestedContextMenu />,
};
