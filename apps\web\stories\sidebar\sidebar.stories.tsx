import type { Meta, StoryObj } from "@storybook/nextjs";

// @ts-expect-error Force loader addon
import NestedSource from "!!raw-loader!./nested";
// @ts-expect-error Force loader addon
import SimpleSource from "!!raw-loader!./simple";

import Nested from "./nested";
import Simple from "./simple";

const meta = {
  title: "Shadcn/Sidebar",
  parameters: {
    layout: "fullscreen",
  },
  tags: ["autodocs"],
  decorators: [
    (Story) => (
      <div className="max-h-[100svh] w-full overflow-hidden">
        <Story />
      </div>
    ),
  ],
} satisfies Meta<typeof Simple>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: SimpleSource,
      },
    },
  },
  render: () => <Simple />,
};

export const NestedSidebar: Story = {
  parameters: {
    docs: {
      source: {
        code: NestedSource,
      },
    },
  },
  render: () => <Nested />,
};
