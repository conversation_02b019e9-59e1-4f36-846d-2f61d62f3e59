import { Toggle } from "@geon-ui/react/primitives/toggle";
import { Meta, StoryObj } from "@storybook/nextjs";
import { Bold, Italic } from "lucide-react";

// @ts-expect-error Force loader addon
import GroupSource from "!!raw-loader!./group";

import Group from "./group";

const meta = {
  title: "shadcn/Toggle",
  component: Toggle,
  parameters: {
    layout: "centered",
  },
  args: {
    variant: "default",
    size: "default",
  },
  argTypes: {
    variant: {
      control: "inline-radio",
      options: ["default", "outline"],
      description: "토글 버튼 스타일",
      table: {
        type: {
          summary: "string | undefined",
          detail: `"default" | "outline"`,
        },
      },
    },
    size: {
      control: "inline-radio",
      options: ["sm", "default", "lg"],
      description: "토글 버튼 크기",
      table: {
        type: {
          summary: "string | undefined",
          detail: `"sm" | "default" | "lg"`,
        },
      },
    },
  },
  tags: ["autodocs"],
} satisfies Meta<typeof Toggle>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Single: Story = {
  args: {
    variant: "default",
    size: "default",
  },

  render: (args) => (
    <Toggle {...args}>
      <Bold className="size-4" />
    </Toggle>
  ),
};

export const WithText: Story = {
  args: {
    children: "Italic",
    variant: "default",
    size: "default",
  },
  argTypes: {
    children: {
      control: "text",
      table: {
        type: {
          summary: "ReactNode",
        },
      },
    },
  },
  render: (args) => (
    <Toggle {...args}>
      <Italic className="size-4" />
      {args.children}
    </Toggle>
  ),
};

export const ToggleGroup: Story = {
  parameters: {
    docs: {
      source: {
        language: "tsx",
        code: GroupSource,
      },
    },
  },
  render: () => <Group />,
};
