import { create } from "zustand";
import { createJSONStorage, persist, PersistOptions } from "zustand/middleware";
/**
 * @see {@link persist} {@link createJSONStorage createJSONStorage}
 */
type CreateZustandStoreOptions<T extends object> = {
  /**
   * persist 사용여부
   * 1. false(기본값) : 상태를 메모리에서만 유지 (새로고침 시 초기화됨)
   *    => 모달 알림 여부, 필터의 상태 등
   *
   * 2. true : 상태를 localStorage 등에 저장하여 새로고침 이후에도 복원
   *    => 로그인 정보, 장바구니 등 (민감정보는 저장 X)
   *
   * @default false
   */
  persist?: boolean;

  /** 상태 변경 메서드: set-get을 이용해 상태와 메서드 변경 */
  state: (set: (fn: (prev: T) => T) => void, get: () => T) => T;

  /**
   * SSR 환경에서 hydration mismatch 방지옵션 (persist: true일 때만 의미 있음)
   * 1. false(기본값) : 상태를 지정한 Storage 에서 가져옴, hydration mismatch 가능성 존재
   *    => use client 에서는 확률이 거의 없음.
   *
   * 2. true : 상태를 store 의 지정된 초기값으로 가져와 SSR Hydration 만을 회피하는 옵션
   * 
      @default false
   */
  skipHydration?: boolean;

  /**
   * 저장소 key 이름 (Ex> localStorage 등)
   *
   * @default 'zustand-store'
   */
  name?: string;

  /**
   * 사용할 storage 객체, 이 외에 스토리지는 일부 구현 필요
   *
   * @example
   * ```
   * storage = createJSONStorage(() => localStorage)
   * storage = createJSONStorage(() => sessionStorage)
   * ```
   * @default
   */
  storage?: PersistOptions<T>["storage"];
};

export function createZustandStore<T extends object>({
  persist: usePersist = false,
  state,
  skipHydration = false,
  name = "zustand-store",
  storage = createJSONStorage(() => localStorage),
}: CreateZustandStoreOptions<T>) {
  const initializer = (set: (fn: (prev: T) => T) => void, get: () => T) => ({
    ...state(set, get),
  });

  return usePersist
    ? create<T>()(
        persist(initializer, {
          skipHydration,
          name,
          storage,
        }),
      )
    : create<T>(initializer);
}
