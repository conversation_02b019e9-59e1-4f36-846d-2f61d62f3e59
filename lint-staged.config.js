/** @type {import("lint-staged").Configuration} */
export default {
  "apps/web/**/*.+(ts|tsx)": [
    // type check
    () => "pnpm tsc -p apps/web/tsconfig.json --noEmit",
    // Lint fix
    // () => "pnpm eslint --fix --cache",
  ],
  "packages/geon-ui/react/**/*.+(ts|tsx)": [
    // type check
    () => "pnpm tsc -p packages/geon-ui/react/tsconfig.json --noEmit",
    // Lint fix
    // () => "pnpm eslint --fix --cache",
  ],
  "packages/geon-map/react-odf/**/*.+(ts|tsx)": [  
    // type check
    () => "pnpm tsc -p packages/geon-map/react-ui/tsconfig.json --noEmit",
  ],
  "packages/geon-map/react-ui/**/*.+(ts|tsx)": [  
    // type check
    () => "pnpm tsc -p packages/geon-map/react-odf/tsconfig.json --noEmit",
  ],
  "packages/geon-query/model/**/*.+(ts|tsx)": [
    // type check
    () => "pnpm tsc -p packages/geon-query/model/tsconfig.json --noEmit",
  ],
  "packages/geon-query/reactQuery/**/*.+(ts|tsx)": [
    // type check
    () => "pnpm tsc -p packages/geon-query/reactQuery/tsconfig.json --noEmit",
  ],
}