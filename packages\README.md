# Monorepo Packages

npm 패키지 형태로 관리할 수 있는 공통 모듈 workspace로, 이 문서는 [`pnpm`](https://pnpm.io/) 기준으로 작성되었습니다.

## 패키지 추가

workspace 로 인식하기 위해서는 폴더 추가 외에 추가적인 설정들이 필요합니다.

### workspace

`pnpm-workspace.yaml` 에 추가한 workspace 만 패키지로 인식하여 import 할 수 있습니다.

```yaml
# pnpm-workspace.yaml
packages:
  # apps 의 한 단계 아래 폴더들(apps/web, apps/doc 등)을 모두 workspace로 인식
  - "apps/*" 
  # packages 아래 모든 폴더를 전부 workspace로 인식 (recursive)
  - "packages/**/*"
```

사용할 패키지들이 정의되면 해당 패키지만 정확하게 지정하는 것을 추천합니다.

### `package.json`

패키지를 import 할 수 있도록 하기 위해서는 필수 지정 항목들:

```json
{
  "name": "@scope/example",
  "version": "0.0.0",
  "exports": {
    ".": "./src/index.ts",
  },
}
```

- **name**: 패키지의 이름. `@패키지범위/패키지이름` 이 일반적인 규칙
- **version**: npm 이 패키지를 찾을 때 "이름" 과 "버전" 으로 매칭하기 때문에 필수 기재 항목. [semantic versioning](https://semver.org/lang/ko/)을 따르는 것이 일반적
- **exports**: 모노레포 구조에서는 packages 들의 빌드 불필요. 소스 레벨을 바로 export.
