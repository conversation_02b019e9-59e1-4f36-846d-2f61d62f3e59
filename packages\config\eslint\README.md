# `@config/eslint`

ESLint 9.x.x configs

- **base**: import / export 자동 정렬, js & ts 기본, prettier, turbo plugin 등이 포함된 기본 설정
- **next**: react, react-hooks, next 설정
- **react-internal**: react, react-hooks 설정
- **tailwind**: tailwindcss 설정

## Usage

포함할 eslint 설정을 하나씩 덧붙여 사용합니다.

```js
// eslint.config.js
import baseConfig from "@config/eslint/base";
import reactConfig from "@config/eslint/react-internal";
import tailwindConfig from "@config/eslint/tailwind";
import storybook from "eslint-plugin-storybook";

/** @type {import("eslint").Linter.Config[]} */
export default [
  ...baseConfig,
  ...tailwindConfig,
  ...reactConfig,
  ...storybook.configs["flat/recommended"],
  {
    /** @type {import("eslint").Linter.RulesRecord} */
    rules: {
      // custom rules here
    }
  }
];
```

필요에 따라 plugin 또는 eslint rules 를 추가로 extend 하여 사용할 수 있습니다.
