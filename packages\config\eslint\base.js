import js from "@eslint/js";
import eslint<PERSON>onfigPrettier from "eslint-config-prettier";
import turboPlugin from "eslint-plugin-turbo";
import simpleImportSort from "eslint-plugin-simple-import-sort";
import unusedImports from "eslint-plugin-unused-imports";
import tseslint from "typescript-eslint";
import eslintPluginPrettier from "eslint-plugin-prettier";

/** @type {import("eslint").Linter.RulesRecord} */
export const baseRules = {
  "import/extensions": "off",
  "no-param-reassign": "off",
  "no-underscore_dangle": "off",
  "prettier/prettier": [
    "error",
    {
      singleQuote: false,
      endOfLine: "auto",
    },
  ],
  "import/order": "off",
  "import/refer-default-export": "off",
  "simple-import-sort/imports": "error",
  "simple-import-sort/exports": "error",
  "unused-imports/no-unused-imports": "error",
  "unused-imports/no-unused-vars": ["warn", { argsIgnorePattern: "^_" }],
  "consistent-return": "off",
  // typescript rules
  "@typescript-eslint/no-namespace": "off",
  "@typescript-eslint/no-empty-function": "off",
  "@typescript-eslint/no-empty-interface": "off",
  "@typescript-eslint/no-empty-object-type": "off",
  "@typescript-eslint/no-explicit-any": "off",
};

/**
 * A shared ESLint configuration for the repository.
 *
 * @type {import("eslint").Linter.Config[]}
 * */
export default [
  js.configs.recommended,
  eslintConfigPrettier,
  ...tseslint.configs.recommended,
  {
    plugins: {
      turbo: turboPlugin,
      "unused-imports": unusedImports,
      "simple-import-sort": simpleImportSort,
      prettier: eslintPluginPrettier,
    },
    rules: {
      ...baseRules,
      "turbo/no-undeclared-env-vars": "warn",
    },
    ignores: ["dist/**"],
  },
];
