# `@config/tailwind`

tailwindcss V4 configuration

## 적용 방법

필요 패키지:

* prettier-plugin-tailwindcss: tailwindcss 클래스 자동 정렬

1. 적용할 곳에 필요 패키지 설치:

```bash
pnpm add -D prettier-plugin-tailwindcss
```

1. 적용할 곳의 package.json > dependencies 에 @config/tailwind 포함

```json
{
  ...
  "dependencies": {
    "@config/tailwind": "workspace:^",
  },
}
```

1. package.json 업데이트 후 pnpm-lockfile 업데이트 진행

```bash
pnpm --recursive i
```
