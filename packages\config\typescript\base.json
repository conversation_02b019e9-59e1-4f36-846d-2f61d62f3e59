{"$schema": "https://json.schemastore.org/tsconfig", "compilerOptions": {"lib": ["ESNext", "DOM", "DOM.Iterable"], "target": "esnext", "module": "esnext", "moduleResolution": "bundler", "moduleDetection": "auto", "declaration": true, "declarationMap": true, "esModuleInterop": true, "incremental": false, "isolatedModules": true, "noUncheckedIndexedAccess": true, "resolveJsonModule": true, "skipLibCheck": true, "strict": true}}