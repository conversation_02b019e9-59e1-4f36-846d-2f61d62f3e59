import { SingletonBase } from "./SingletonBase";

export class Logger extends SingletonBase {
  private logs: string[] = [];

  constructor(name = "Logger 인스턴스") {
    super(name);
  }

  log(message: string) {
    this.logs.push(message);
    console.log(`[LOG]: ${message}`);
  }

  getLogs() {
    return this.logs;
  }

  static getInstance(): Logger {
    return super._getInstance.call(this, "Logger 싱글톤") as Logger;
  }
}
