export abstract class SingletonBase {
  #className: string;

  constructor(className = "싱글톤 클래스 생성") {
    this.#className = className;
    console.log(`[INIT] ${this.#className}`);
  }

  protected static _getInstance(this: unknown, ...args: unknown[]): unknown {
    const cls = this as {
      new (...args: unknown[]): unknown;
      _instance?: unknown;
    };

    if (!cls._instance) {
      cls._instance = new cls(...args);
    }
    return cls._instance;
  }
}
