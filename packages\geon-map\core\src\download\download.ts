import {
  DownloadControlOptions,
  ODF,
  ODF_DOWNLOAD_CONTROL,
  ODF_MAP,
} from "../types";

export class Download {
  #downloadControl: ODF_DOWNLOAD_CONTROL;
  constructor(odf: ODF, options?: DownloadControlOptions) {
    this.#downloadControl = new odf.DownloadControl(options || {});
  }

  /**
   * DownloadControl과 map 연결
   */
  setMap(map: ODF_MAP, createElementFlag: boolean = false): void {
    this.#downloadControl.setMap(map, createElementFlag);
  }

  /**
   * DownloadControl과 map 연결 해제
   */
  removeMap(): void {
    this.#downloadControl.removeMap();
  }

  /**
   * 이미지 다운로드
   */
  downloadImage(): void {
    this.#downloadControl.downloadsImage();
  }

  /**
   * PDF 다운로드
   */
  downloadPDF(): void {
    this.#downloadControl.downloadsPDF();
  }

  /**
   * 지도 이미지를 base64로 반환
   */
  getBase64(): string {
    return this.#downloadControl.getBase64();
  }

  /**
   * 컨트롤 생성 옵션 반환
   */
  getConstructorOptions(): Array<object> {
    return this.#downloadControl.getConstructorOptions();
  }

  /**
   * DownloadControl 인스턴스에 직접 접근
   */
  get downloadControl() {
    return this.#downloadControl;
  }
}
