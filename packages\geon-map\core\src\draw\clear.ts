import { ODF, ODF_MAP } from "../types";

/**
 * Clear 클래스
 * 그리기 및 측정 결과 삭제 전담
 *
 * 단일 책임:
 * - ClearControl 관리
 * - 모든 그리기/측정 결과 삭제
 */
export class Clear {
  private clearControl: any = null;
  private map: ODF_MAP;
  private odf: ODF;

  constructor(map: ODF_MAP, odf: ODF, autoInitialize: boolean = true) {
    if (!map || !odf) {
      throw new Error("Clear 초기화 시 map과 odf가 필요합니다.");
    }
    this.map = map;
    this.odf = odf;

    // 생성자에서 자동 초기화
    if (autoInitialize) {
      this.initialize();
    }
  }

  /**
   * ClearControl 초기화 (내부 메서드)
   */
  private initialize(): void {
    if (this.clearControl) {
      console.warn("ClearControl is already initialized");
      return;
    }

    try {
      this.clearControl = new this.odf.ClearControl();
      this.clearControl.setMap(this.map);
    } catch (error) {
      console.error("Failed to initialize ClearControl:", error);
      throw error;
    }
  }

  /**
   * 모든 그리기 및 측정 결과 삭제
   */
  clear(): void {
    if (!this.clearControl) {
      console.warn("ClearControl is not initialized");
      return;
    }

    try {
      this.clearControl.clear();
      console.log("All drawings and measurements cleared");
    } catch (error) {
      console.error("Failed to clear drawings:", error);
      throw error;
    }
  }

  /**
   * ClearControl 인스턴스 반환
   */
  getClearControl(): any {
    return this.clearControl;
  }

  /**
   * 초기화 상태 확인
   */
  isInitialized(): boolean {
    return !!this.clearControl;
  }

  /**
   * 지도에서 제거
   */
  destroy(): void {
    if (this.clearControl) {
      try {
        // this.clearControl.removeMap(); // 주석 처리 (사용자가 문제 발견)
        this.clearControl = null;
        console.log("ClearControl destroyed");
      } catch (error) {
        console.error("Failed to destroy ClearControl:", error);
      }
    }
  }
}
