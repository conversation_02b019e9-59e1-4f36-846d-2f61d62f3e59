import { Feature } from "../feature/feature";
import { ODF, ODF_MAP } from "../types";
import type {
  DrawControlOptions,
  DrawingMode,
  DrawToolType,
  IDraw,
} from "../types/draw";

// 기본 옵션 설정
const defaultOptions: DrawControlOptions = {
  continuity: false,
  measure: false,
  createNewLayer: false,
  editFeatureMenu: [],
  tools: ["text", "polygon", "lineString", "box", "point", "circle", "curve"],
};

// DrawingMode를 DrawToolType으로 매핑 (측정 모드 포함)
const modeToToolMap: Record<string, DrawToolType> = {
  text: "text",
  polygon: "polygon",
  lineString: "lineString",
  point: "point",
  circle: "circle",
  curve: "curve",
  box: "box",
  buffer: "buffer",
};

/**
 * Draw 클래스
 * 그리기 도구 전담 관리 (단일 책임)
 *
 * 책임:
 * - DrawControl 관리
 * - 그리기 이벤트 처리
 * - 그리기 스타일 제어
 * - Feature 조작 (생성, 삭제, 수정)
 */
export class Draw implements IDraw {
  private drawControl: any = null;
  map: ODF_MAP;
  odf: ODF;

  constructor(map: ODF_MAP, odf: ODF, options?: DrawControlOptions) {
    if (!map || !odf) {
      throw new Error("Draw 초기화 시 map과 odf가 필요합니다.");
    }
    this.map = map;
    this.odf = odf;

    // 생성자에서 자동 초기화
    if (options) {
      this.initialize(options);
    }
  }

  /**
   * DrawControl 인스턴스 초기화 (Draw 전용)
   */
  initialize(options: DrawControlOptions = {}): void {
    if (this.drawControl) {
      console.warn("DrawControl is already initialized");
      return;
    }

    try {
      const mergedOptions = { ...defaultOptions, ...options };

      // ODF DrawControl 인스턴스 생성
      this.drawControl = new this.odf.DrawControl(mergedOptions);

      // 지도에 연결
      this.drawControl.setMap(this.map);
    } catch (error) {
      console.error("Failed to initialize DrawControl:", error);
      throw error;
    }
  }

  /**
   * DrawingMode를 DrawToolType으로 매핑
   */
  private mapDrawingModeToToolType(mode: DrawingMode): DrawToolType | null {
    return modeToToolMap[mode] || null;
  }

  /**
   * 그리기 시작 (Draw 전용)
   */
  startDrawing(mode: DrawingMode): void {
    if (!mode) {
      throw new Error("Invalid mode");
    }

    if (!this.drawControl) {
      throw new Error("DrawControl is not initialized");
    }

    const toolType = this.mapDrawingModeToToolType(mode);
    if (!toolType) {
      throw new Error(`Unsupported drawing mode: ${mode}`);
    }

    try {
      // ODF DrawControl의 해당 메서드 호출
      switch (toolType) {
        case "text":
          this.drawControl.drawText();
          break;
        case "polygon":
          this.drawControl.drawPolygon();
          break;
        case "lineString":
          this.drawControl.drawLineString();
          break;
        case "box":
          this.drawControl.drawBox();
          break;
        case "point":
          this.drawControl.drawPoint();
          break;
        case "circle":
          this.drawControl.drawCircle();
          break;
        case "curve":
          this.drawControl.drawCurve();
          break;
        case "buffer":
          this.drawControl.drawBuffer();
          break;
        default:
          throw new Error(`Unsupported tool type: ${toolType}`);
      }

      console.log(`Drawing started with mode: ${mode}`);
    } catch (error) {
      console.error(`Failed to start drawing with mode ${mode}:`, error);
      throw error;
    }
  }

  /**
   * 그리기 중지 (Draw 전용)
   */
  stopDrawing(): void {
    if (this.drawControl) {
      try {
        this.drawControl.clear();
        console.log("Drawing stopped");
      } catch (error) {
        console.error("Failed to stop drawing:", error);
      }
    }
  }

  /**
   * 현재 그리기 취소 (진행 중인 작업만 취소)
   */
  cancelCurrentDrawing(): void {
    if (this.drawControl) {
      try {
        this.drawControl.clear();
        console.log("Current drawing cancelled");
      } catch (error) {
        console.error("Failed to cancel drawing:", error);
      }
    }
  }

  /**
   * 모든 그리기 삭제 (Stateless)
   */
  clear(): void {
    console.log("Clearing all drawings");

    if (this.drawControl) {
      try {
        this.drawControl.clear();
        console.log("All drawings cleared");
      } catch (error) {
        console.error("Failed to clear drawings:", error);
      }
    }
  }

  /**
   * 그리기 레이어 조회
   */
  getDrawLayer(): any {
    if (!this.drawControl) return null;
    return this.drawControl.findDrawVectorLayer();
  }

  /**
   * DrawControl 인스턴스 반환
   */
  getDrawControl(): any {
    return this.drawControl;
  }

  /**
   * feature 삭제 (Feature 위임)
   */
  deleteFeature(feature: any): boolean {
    if (!feature || !this.drawControl) {
      return false;
    }
    const drawLayer = this.getDrawLayer();
    return Feature.deleteFeature(drawLayer, feature);
  }

  /**
   * feature 스타일 변경 (Feature 위임)
   */
  changeFeatureStyle(feature: any, styleOptions: any): boolean {
    return Feature.changeFeatureStyle(feature, styleOptions);
  }

  /**
   * GeoJSON으로 내보내기
   */
  /**
   * 현재 그리기 데이터를 GeoJSON 형태로 내보내기
   */
  exportToGeoJSON(): any {
    const drawLayer = this.getDrawLayer();
    if (!drawLayer) {
      console.warn("Draw layer not found");
      return null;
    }

    try {
      // ODF Layer의 toGeoJson 메서드 사용
      const geoJsonData = drawLayer.toGeoJson();
      return geoJsonData;
    } catch (error) {
      console.error("Failed to export to GeoJSON:", error);
      return null;
    }
  }

  /**
   * GeoJSON에서 가져오기
   */
  importFromGeoJSON(geojson: any): boolean {
    if (!this.drawControl || !geojson) {
      console.warn("DrawControl is not initialized or invalid GeoJSON");
      return false;
    }

    try {
      const drawLayer = this.getDrawLayer();
      if (!drawLayer) {
        console.warn("Draw layer not found");
        return false;
      }

      // GeoJSON 파싱 및 피처 추가
      if (geojson.type === "FeatureCollection" && geojson.features) {
        geojson.features.forEach((featureData: any) => {
          if (featureData.type === "Feature" && featureData.geometry) {
            // ODF Feature 생성 및 레이어에 추가
            const format = new (globalThis as any).ol.format.GeoJSON();
            const feature = format.readFeature(featureData);
            drawLayer.getSource().addFeature(feature);
          }
        });
      } else if (geojson.type === "Feature") {
        // 단일 Feature 처리
        const format = new (globalThis as any).ol.format.GeoJSON();
        const feature = format.readFeature(geojson);
        drawLayer.getSource().addFeature(feature);
      }

      console.log("GeoJSON imported successfully");
      return true;
    } catch (error) {
      console.error("Failed to import GeoJSON:", error);
      return false;
    }
  }

  /**
   * 지도 인스턴스 반환 (외부에서 접근 가능)
   */
  getMap(): any {
    return this.map;
  }

  /**
   * 지도에서 제거 (단일책임: 그리기 기능만 정리)
   */
  destroy(): void {
    if (this.drawControl) {
      // DrawControl 제거 (이벤트 리스너는 React-ODF에서 관리)
      try {
        this.drawControl.removeMap();
      } catch (error) {
        console.warn("Failed to remove DrawControl from map:", error);
      }
      this.drawControl = null;
    }

    console.log("Draw instance destroyed");
  }
}
