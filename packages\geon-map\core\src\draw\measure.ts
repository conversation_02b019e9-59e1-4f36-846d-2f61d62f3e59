import { Feature } from "../feature/feature";
import { ODF } from "../odf";
import type {
  IMeasure,
  MeasureControlOptions,
  MeasureEventType,
  MeasureFeature,
  MeasureMode,
} from "../types/draw";

/**
 * ODF Measure 핵심 클래스
 * 측정 도구 관리, 이벤트 처리를 담당
 *
 * Draw 클래스와 통합되어 사용되며, 측정과 그리기를 동일한 카테고리로 처리
 */
export class Measure implements IMeasure {
  private measureControl: any = null;
  private map: any = null;
  private eventListeners: Map<string, string> = new Map();
  private currentOptions: MeasureControlOptions | null = null;

  /**
   * 생성자 - 자동 초기화
   */
  constructor(map: any, options: MeasureControlOptions = {}) {
    this.initialize(map, options);
  }

  /**
   * MeasureControl 인스턴스 초기화 (내부 메서드)
   */
  private initialize(map: any, options: MeasureControlOptions = {}): void {
    if (!map) {
      throw new Error("Map instance is required");
    }

    if (!ODF.isAvailable()) {
      throw new Error("ODF library is not available");
    }

    this.map = map;

    // 기본 옵션 설정
    const defaultOptions: MeasureControlOptions = {
      displayOption: {
        area: {
          decimalPoint: 2,
          transformUnit: 1000000,
        },
        distance: {
          decimalPoint: 2,
          transformUnit: 1000,
        },
        round: {
          decimalPoint: 2,
          transformUnit: 1000,
        },
      },
      style: {
        fill: {
          color: [255, 255, 0, 0.3],
        },
        stroke: {
          color: [255, 165, 0, 0.8],
          width: 2,
        },
      },
      message: {
        DRAWSTART: "클릭하여 측정을 시작하세요",
        DRAWEND_POLYGON:
          "클릭하여 폴리곤을 그리거나, 더블클릭하여 그리기를 종료하세요",
        DRAWEND_LINE:
          "클릭하여 라인을 그리거나, 더블클릭하여 그리기를 종료하세요",
      },
      continuity: false,
      rightClickDelete: false,
      tools: ["distance", "area", "round", "spot"],
      spotProjection: undefined, // 지도의 기본 좌표계 사용
    };

    const mergedOptions = { ...defaultOptions, ...options };
    this.currentOptions = mergedOptions;

    // ODF MeasureControl 인스턴스 생성
    this.measureControl = new (globalThis as any).odf.MeasureControl(
      mergedOptions,
    );

    // 지도에 연결
    this.measureControl.setMap(map);
  }

  /**
   * MeasureMode를 ODF MeasureControl 메서드로 매핑
   */
  private mapMeasureModeToMethod(mode: MeasureMode): string | null {
    const modeMap: Record<string, string> = {
      distance: "executeDistance",
      area: "executeArea",
      round: "executeRound",
      spot: "executeSpot",
    };

    return modeMap[mode] || null;
  }

  /**
   * 측정 시작
   */
  startMeasuring(mode: MeasureMode): void {
    if (!this.measureControl || !mode) {
      throw new Error("MeasureControl is not initialized or invalid mode");
    }

    const methodName = this.mapMeasureModeToMethod(mode);
    if (!methodName) {
      throw new Error(`Unsupported measure mode: ${mode}`);
    }

    // ODF MeasureControl의 해당 메서드 호출
    if (typeof this.measureControl[methodName] === "function") {
      this.measureControl[methodName]();
    } else {
      throw new Error(`Method ${methodName} not found on MeasureControl`);
    }
  }

  /**
   * 측정 중지
   */
  stopMeasuring(): void {
    if (!this.measureControl) return;

    // MeasureControl의 측정 종료
    this.measureControl.executeOff();
  }

  /**
   * 모든 측정 결과 삭제
   */
  clear(): void {
    if (!this.measureControl) return;

    console.log("Clearing all measurements");
    this.measureControl.clear();
  }

  /**
   * 측정 레이어 조회
   */
  getMeasureLayer(): any {
    if (!this.measureControl) return null;
    return this.measureControl.findDrawVectorLayer();
  }

  /**
   * 툴팁 제거
   */
  removeHelpTooltip(): void {
    if (!this.measureControl) return;
    this.measureControl.removeHelpTooltip();
  }

  /**
   * 현재 측정 옵션 반환
   */
  getCurrentOptions(): MeasureControlOptions | null {
    return this.currentOptions;
  }

  /**
   * 측정 옵션 업데이트
   */
  updateOptions(options: Partial<MeasureControlOptions>): void {
    if (!this.measureControl) {
      console.warn("MeasureControl is not initialized");
      return;
    }

    this.currentOptions = { ...this.currentOptions, ...options };

    // MeasureControl 재초기화가 필요할 수 있음
    // ODF MeasureControl이 런타임 옵션 변경을 지원하지 않는 경우
    console.log("MeasureControl options updated", this.currentOptions);
  }

  /**
   * MeasureControl 인스턴스 반환
   */
  getMeasureControl(): any {
    return this.measureControl;
  }

  /**
   * 측정 이벤트 리스너 등록
   */
  addEventListener(
    eventType: MeasureEventType,
    handler: (feature: any) => void,
  ): string {
    if (!this.measureControl) {
      throw new Error("MeasureControl is not initialized");
    }

    console.log("measureControl", this.measureControl);

    const eventId = (globalThis as any).odf.event.addListener(
      this.measureControl,
      eventType,
      handler,
      false, // expiration: false (영구 리스너)
    );

    const listenerId = `${eventType}_${Date.now()}`;
    this.eventListeners.set(listenerId, eventId);

    return listenerId;
  }

  /**
   * 측정 이벤트 리스너 제거
   */
  removeEventListener(listenerId: string): void {
    const eventId = this.eventListeners.get(listenerId);
    if (eventId) {
      (globalThis as any).odf.event.removeListener(eventId);
      this.eventListeners.delete(listenerId);
    }
  }

  /**
   * 측정 결과를 MeasureFeature로 변환
   */
  convertToMeasureFeature(
    feature: any,
    measureType: MeasureMode,
  ): MeasureFeature {
    const coordinates = Feature.extractCoordinates(feature);
    const properties = feature?.getProperties?.() || {};

    // 측정 값과 단위 추출 (ODF MeasureControl의 속성에서)
    let measurementValue = 0;
    let measurementUnit = "";

    // ODF MeasureControl에서 측정 결과 추출 로직
    // 실제 구현에서는 ODF의 측정 결과 속성을 확인해야 함
    if (properties.measurement) {
      measurementValue = properties.measurement.value || 0;
      measurementUnit = properties.measurement.unit || "";
    }

    const measureFeature: MeasureFeature = {
      id:
        feature?.getId?.() ||
        `measure_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type: measureType,
      coordinates,
      geometry: feature?.getGeometry?.() || null,
      properties,
      measurementType: measureType,
      measurementValue,
      measurementUnit,
      createdAt: new Date(),
    };

    return measureFeature;
  }

  /**
   * 지도에서 제거
   */
  destroy(): void {
    if (this.measureControl) {
      // 모든 이벤트 리스너 제거
      this.eventListeners.forEach((eventId, key) => {
        if (eventId) {
          try {
            (globalThis as any).odf.event.removeListener(eventId);
            console.log(`Measure event listener removed: ${key} -> ${eventId}`);
          } catch (error) {
            console.warn(
              `Failed to remove measure event listener ${key}:`,
              error,
            );
          }
        }
      });
      this.eventListeners.clear();

      // MeasureControl 제거
      try {
        this.measureControl.removeMap();
      } catch (error) {
        console.warn("Failed to remove MeasureControl from map:", error);
      }
      this.measureControl = null;
    }

    this.map = null;
    this.currentOptions = null;
  }
}
