import { ODF, ODF_MAP } from "../types";
import { MapSystemEventType, OneTimeEventOption } from "./../types/event";

/**
 * Event 클래스
 * map, odf 인스턴스를 한 번 받아서 재사용하는 방식
 * Store에서 한 번만 생성하여 메모리 효율성과 사용성을 모두 확보
 */
export class Event {
  private map: ODF_MAP;
  private odf: ODF;
  private mousePositionId: string | null = null;
  private coordinateCallback?: (coord: { x: number; y: number }) => void;

  constructor(map: ODF_MAP, odf: ODF) {
    if (!map || !odf) {
      throw new Error("Event 초기화 시 map과 odf가 필요합니다.");
    }
    this.map = map;
    this.odf = odf;
  }

  /**
   * 이벤트 리스너 추가
   */
  addListener(
    target: ODF_MAP | any | string,
    eventType: MapSystemEventType,
    callback: (evt: any) => void,
    expiration: OneTimeEventOption = false,
  ): string {
    // target이 없으면 기본적으로 map 사용
    const actualTarget = target || this.map;
    return this.odf.event.addListener(
      actualTarget,
      eventType,
      callback,
      expiration,
    );
  }

  /**
   * 이벤트 리스너 제거
   */
  removeListener(eventId: string): void {
    this.odf.event.removeListener(eventId);
  }

  /**
   * 좌표 콜백 설정
   */
  setCoordinateCallback(
    callback: (coord: { x: number; y: number }) => void,
  ): void {
    this.coordinateCallback = callback;
  }

  /**
   * 마우스 위치 추적 시작
   */
  mousePositionOn(projCoord: number = 4326): void {
    this.mousePositionId = this.addListener(
      this.map,
      "pointermove",
      (evt: any) => {
        const coord = this.map
          .getProjection()
          .unproject(evt.coordinate, projCoord);
        this.coordinateCallback?.({ x: coord[0], y: coord[1] });
      },
    );
  }

  /**
   * 마우스 위치 추적 중지
   */
  mousePositionOff(): void {
    if (this.mousePositionId !== null) {
      this.removeListener(this.mousePositionId);
      this.mousePositionId = null;
    }
  }
}
