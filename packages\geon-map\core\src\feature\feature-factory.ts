declare const odf: any;

import type { PointFeatureOptions } from "../types/feature-types";

/**
 * ODF 피쳐 생성을 위한 팩토리 클래스
 */
export class FeatureFactory {
  /**
   * WKT에서 피쳐 생성
   */
  static fromWKT(wkt: string, properties?: Record<string, any>): any {
    return odf.FeatureFactory.fromWKT(wkt, properties);
  }
  /**
   * 포인트 피쳐 생성
   */
  static createPoint(options: PointFeatureOptions): any {
    return odf.FeatureFactory.produce({
      geometryType: "point",
      coordinates: options.coordinates,
      ...(options.properties && { properties: options.properties }),
    });
  }
}
