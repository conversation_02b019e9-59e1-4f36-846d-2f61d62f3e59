import type { DrawingMode, DrawingFeature } from "../types/draw";

/**
 * ODF Feature 핵심 클래스
 * Feature 조작, 변환, 스타일링을 담당
 */
export class Feature {
  /**
   * 피처에서 좌표 추출
   */
  static extractCoordinates(feature: any): any {
    const geometry = feature.getGeometry();
    if (!geometry) return null;

    const geometryType = geometry.getType();

    switch (geometryType) {
      case "Point":
        return geometry.getCoordinates();
      case "LineString":
        return geometry.getCoordinates();
      case "Polygon":
        return geometry.getCoordinates();
      case "Circle":
        return {
          center: geometry.getCenter(),
          radius: geometry.getRadius(),
        };
      default:
        return geometry.getCoordinates();
    }
  }

  /**
   * ODF 지오메트리 타입을 DrawingMode로 매핑
   */
  static mapGeometryTypeToDrawingMode(geometryType: string): DrawingMode {
    const typeMap: Record<string, DrawingMode> = {
      Point: "point",
      LineString: "lineString",
      Polygon: "polygon",
      Circle: "circle",
    };

    return typeMap[geometryType] || "point";
  }

  /**
   * DrawingMode를 ODF 지오메트리 타입으로 매핑
   */
  static mapDrawingModeToGeometryType(
    mode: "point" | "lineString" | "polygon" | "circle"
  ): string {
    const typeMap: Record<
      "point" | "lineString" | "polygon" | "circle",
      string
    > = {
      point: "Point",
      lineString: "LineString",
      polygon: "Polygon",
      circle: "Circle",
    };

    return typeMap[mode] || "Point";
  }

  /**
   * ODF Feature를 DrawingFeature로 변환
   */
  static convertToDrawingFeature(feature: any): DrawingFeature {
    const geometry = feature.getGeometry();
    const geometryType = geometry?.getType();

    return {
      id:
        (feature.getId && feature.getId()) ||
        `feature_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      type: this.mapGeometryTypeToDrawingMode(geometryType),
      coordinates: this.extractCoordinates(feature),
      geometry: feature.getGeometry && feature.getGeometry(),
      properties: (feature.getProperties && feature.getProperties()) || {},
      createdAt: new Date(),
    };
  }

  /**
   * Feature 삭제
   */
  static deleteFeature(drawLayer: any, feature: any): boolean {
    if (!drawLayer || !feature) {
      return false;
    }

    try {
      drawLayer.removeFeature(feature);
      return true;
    } catch (error) {
      console.error("Failed to delete feature:", error);
      return false;
    }
  }

  /**
   * Feature 스타일 변경
   */
  static changeFeatureStyle(feature: any, styleOptions: any): boolean {
    if (!feature || !styleOptions) {
      return false;
    }

    try {
      if (feature && feature.setStyle) {
        // ODF StyleFactory를 사용하여 스타일 객체 생성
        const odfStyle = (globalThis as any).odf.StyleFactory.produce(
          styleOptions
        );
        feature.setStyle(odfStyle);
        return true;
      }
      return false;
    } catch (error) {
      console.error("Failed to change feature style:", error);
      return false;
    }
  }

  /**
   * SelectedFeature 객체 생성
   */
  static createSelectedFeature(feature: any): any {
    const geometry = feature.getGeometry();
    const geometryType = geometry?.getType();

    return {
      feature,
      id: feature.getId() || `feature_${Date.now()}`,
      type: this.mapGeometryTypeToDrawingMode(geometryType),
      coordinates: this.extractCoordinates(feature),
      properties: feature.getProperties() || {},
    };
  }

  /**
   * ContextMenuInfo 객체 생성
   */
  static createContextMenuInfo(
    feature: any,
    coordinates: { x: number; y: number },
    position: [number, number],
    layerType?: "draw" | "measure" | null
  ): any {
    const selectedFeature = this.createSelectedFeature(feature);

    // layerType이 제공된 경우 selectedFeature에 추가
    if (selectedFeature && layerType) {
      selectedFeature.layerType = layerType;
    }

    return {
      pixel: [coordinates.x, coordinates.y],
      position: position,
      feature: selectedFeature,
      visible: true,
    };
  }
}
