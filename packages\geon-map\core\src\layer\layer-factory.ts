import type {
  BasemapInfo,
  GeoserverLayerOptions,
  LayerOptions,
  LayerType,
  ODF,
  ODF_MAP,
} from "../types";

/**
 * 레이어 옵션을 ODF 파라미터로 변환하는 팩토리 클래스
 */
const BASEMAP_WIDGET_ID = "odf-basemap-widget";
export class LayerFactory {
  // private 필드 선언
  #map: ODF_MAP;
  #odf: ODF;

  constructor(map: ODF_MAP, odf: ODF) {
    this.#map = map;
    this.#odf = odf;
  }
  setBasemapInfo(basemapInfo: BasemapInfo) {
    this.#map.removeLayer(BASEMAP_WIDGET_ID);
    const apiParameter = {
      ...JSON.parse(basemapInfo.mapUrlparamtr),
      service: basemapInfo.lyrStleCodeNm?.toLocaleLowerCase(),
      server: basemapInfo.mapUrl,
    };

    const basemapLayer = this.#odf.LayerFactory.produce("api", apiParameter);
    basemapLayer.setODFId(BASEMAP_WIDGET_ID);
    basemapLayer.setMap(this.#map);
    return basemapLayer;
  }
  /**
   * 레이어 옵션을 ODF 파라미터로 변환
   */
  static convertToODFParams(options: LayerOptions): {
    type: LayerType;
    params: any;
  } {
    const { type, renderOptions } = options;

    switch (type) {
      case "geoserver": {
        const geoserverOptions = options as GeoserverLayerOptions;
        return {
          type,
          params: {
            server:
              typeof geoserverOptions.server === "string"
                ? geoserverOptions.server
                : geoserverOptions.server.url,
            layer: geoserverOptions.layer,
            service: geoserverOptions.service,
            method: geoserverOptions.method || "get",
            bbox: geoserverOptions.bbox || false,
            crtfckey: geoserverOptions.crtfckey || "",
            projection: geoserverOptions.projection || "EPSG:5186",
            limit: geoserverOptions.limit,
            tiled: geoserverOptions.tiled,
            geometryType: geoserverOptions.geometryType,
            serviceTy: geoserverOptions.serviceTy,
            ...(typeof geoserverOptions.server !== "string" && {
              version: geoserverOptions.server.version,
              proxyURL: geoserverOptions.server.proxyURL,
              proxyParam: geoserverOptions.server.proxyParam,
            }),
          },
        };
      }

      case "geotiff": {
        const geotiffOptions = options as any;
        return {
          type,
          params: {
            sources: geotiffOptions.sources,
            normalize: geotiffOptions.normalize ?? true,
            wrapX: geotiffOptions.wrapX,
            opaque: geotiffOptions.opaque,
            transition: geotiffOptions.transition,
            renderOptions,
          },
        };
      }

      case "geojson": {
        const geojsonOptions = options as any;
        return {
          type,
          params: {
            data: geojsonOptions.data,
            dataProjectionCode: geojsonOptions.dataProjectionCode,
            featureProjectionCode: geojsonOptions.featureProjectionCode,
            service: geojsonOptions.service,
            renderOptions,
          },
        };
      }

      case "kml": {
        const kmlOptions = options as any;
        return {
          type,
          params: {
            data: kmlOptions.data,
            dataProjectionCode: kmlOptions.dataProjectionCode,
            featureProjectionCode: kmlOptions.featureProjectionCode,
            renderOptions,
          },
        };
      }

      case "csv": {
        const csvOptions = options as any;
        return {
          type,
          params: {
            data: csvOptions.data,
            dataProjectionCode: csvOptions.dataProjectionCode,
            featureProjectionCode: csvOptions.featureProjectionCode,
            geometryColumnName: csvOptions.geometryColumnName,
            delimiter: csvOptions.delimiter,
            renderOptions,
          },
        };
      }

      case "api": {
        const apiOptions = options as any;
        return {
          type,
          params: {
            server:
              typeof apiOptions.server === "string"
                ? apiOptions.server
                : apiOptions.server.url,
            service: apiOptions.service,
            bbox: apiOptions.bbox,
            tiled: apiOptions.tiled,
            tileGrid: apiOptions.tileGrid,
            originalOption: apiOptions.originalOption,
            parameterFilter: apiOptions.parameterFilter,
            ...(typeof apiOptions.server !== "string" && {
              proxyURL: apiOptions.server.proxyURL,
              proxyParam: apiOptions.server.proxyParam,
            }),
            renderOptions,
          },
        };
      }

      case "svg": {
        const svgOptions = options as any;
        return {
          type,
          params: {
            svgContainer: svgOptions.svgContainer,
            extent: svgOptions.extent,
            renderOptions,
          },
        };
      }

      default:
        throw new Error(`Unsupported layer type: ${type}`);
    }
  }

  /**
   * ODF 레이어 인스턴스 생성
   */
  static createODFLayer(type: LayerType, params: any): any {
    if (typeof (globalThis as any).odf === "undefined") {
      throw new Error("ODF library is not loaded");
    }

    return (globalThis as any).odf.LayerFactory.produce(type, params);
  }
}
