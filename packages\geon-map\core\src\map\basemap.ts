import type { ODF, ODF_MAP } from "../types";

/**
 * 베이스맵 제어를 담당하는 Controller 클래스
 *
 * 책임: 베이스맵 변경, 베이스맵 정보 관리
 */
export class Basemap {
  private map: ODF_MAP;
  private odf: ODF;
  private basemapControl: any;

  constructor(map: ODF_MAP, odf: ODF) {
    if (!map || !odf) {
      throw new Error("Basemap 초기화 시 map과 odf가 필요합니다.");
    }
    this.map = map;
    this.odf = odf;
    this.initializeBasemapControl();
  }

  /**
   * 베이스맵 컨트롤 초기화
   */
  private initializeBasemapControl(): void {
    try {
      // ODF BasemapControl 생성
      this.basemapControl = new this.odf.BasemapControl();

      // 지도에 베이스맵 컨트롤 설정
      if (this.map.setBasemapControl) {
        this.map.setBasemapControl(this.basemapControl);
      }
    } catch (error) {
      console.error("베이스맵 컨트롤 초기화 실패:", error);
    }
  }

  /**
   * 베이스맵 변경
   */
  switchBaseLayer(basemapId: string): void {
    try {
      if (!this.basemapControl) {
        console.warn("베이스맵 컨트롤이 초기화되지 않았습니다.");
        return;
      }

      if (typeof this.basemapControl.switchBaseLayer === "function") {
        this.basemapControl.switchBaseLayer(basemapId);
      } else {
        console.warn("switchBaseLayer 메서드를 사용할 수 없습니다.");
      }
    } catch (error) {
      console.error("베이스맵 변경 실패:", error);
    }
  }

  /**
   * 현재 베이스맵 ID 조회
   */
  getCurrentBasemap(): string | null {
    try {
      if (!this.basemapControl) {
        return null;
      }

      if (typeof this.basemapControl.getCurrentBasemap === "function") {
        return this.basemapControl.getCurrentBasemap();
      }

      return null;
    } catch (error) {
      console.error("현재 베이스맵 조회 실패:", error);
      return null;
    }
  }

  /**
   * 사용 가능한 베이스맵 목록 조회
   */
  getAvailableBasemaps(): string[] {
    try {
      if (!this.basemapControl) {
        return [];
      }

      if (typeof this.basemapControl.getAvailableBasemaps === "function") {
        return this.basemapControl.getAvailableBasemaps();
      }

      return [];
    } catch (error) {
      console.error("베이스맵 목록 조회 실패:", error);
      return [];
    }
  }

  /**
   * 베이스맵 컨트롤 인스턴스 반환 (고급 사용자용)
   */
  getBasemapControl(): any {
    return this.basemapControl;
  }

  /**
   * 베이스맵 컨트롤 해제
   */
  destroy(): void {
    try {
      if (
        this.basemapControl &&
        typeof this.basemapControl.destroy === "function"
      ) {
        this.basemapControl.destroy();
      }
      this.basemapControl = null;
    } catch (error) {
      console.error("베이스맵 컨트롤 해제 실패:", error);
    }
  }
}
