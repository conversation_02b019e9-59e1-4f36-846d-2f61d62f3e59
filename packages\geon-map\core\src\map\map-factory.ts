import { checkODFAvailability, ODF } from "../odf";
import type { MapInitializeOptions, ODF_MAP } from "../types";
import { createODFCoordinate } from "../utils/coordinate";

/**
 * 지도 생성을 담당하는 Factory 클래스
 *
 * 책임: 지도 인스턴스 생성만 담당
 */
export class MapFactory {
  /**
   * 지도 초기화 옵션 생성
   */
  static createMapOptions(options: MapInitializeOptions): any {
    return {
      center: createODFCoordinate(
        options.center ?? [152347.00887367074, 266168.33661099506],
      ),
      //center: options.center ?? [899587.8889468038, 1664175.9854401087],
      zoom: options.zoom ?? 11,
      projection: options.projection ?? "EPSG:5186",
      baroEMapURL:
        options.baroEMapURL ??
        "https://geon-gateway.geon.kr/map/api/map/baroemap",
      baroEMapAirURL:
        options.baroEMapAirURL ??
        "https://geon-gateway.geon.kr/map/api/map/ngisair",
      basemap: options.basemap ?? {
        baroEMap: ["eMapBasic", "eMapAIR", "eMapColor", "eMapWhite"],
      },
      optimization: options.optimization ?? true,
    };
  }

  /**
   * 지도 인스턴스 생성
   */
  static async createMap(
    container: HTMLElement,
    options: MapInitializeOptions,
  ): Promise<{
    map: ODF_MAP;
    odf: ODF;
    // view: any;
    // layer: any;
    // control: any;
  }> {
    if (!container) {
      throw new Error("Container element is required");
    }

    try {
      // ODF 라이브러리 가용성 확인
      const odf = await checkODFAvailability();

      // 지도 옵션 생성
      const mapOptions = this.createMapOptions(options);

      // ODF 지도 인스턴스 생성
      const map = new odf.Map(container, mapOptions);

      return {
        map,
        odf,
      };
    } catch (error) {
      console.error("Failed to create map:", error);
      throw error;
    }
  }
}
