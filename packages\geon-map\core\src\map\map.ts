import type { Coordinate, ProjectionCode } from "../types";
import { createODFCoordinate, fromODFCoordinate } from "../utils/coordinate";

/**
 * 지도 조작을 담당하는 Controller 클래스
 *
 * 책임: 생성된 지도 인스턴스의 상태 조작만 담당
 *
 * 사용법:
 * ```tsx
 * const controller = new Map(map);
 * controller.setCenter([127, 37]);
 * controller.setZoom(10);
 * ```
 */
export class Map {
  private map: any;

  constructor(map: any) {
    if (!map) {
      throw new Error("Map instance is required");
    }
    this.map = map;
  }

  /**
   * 지도 중심점 설정
   */
  setCenter(center: Coordinate): void {
    try {
      const odfCenter = createODFCoordinate(center);
      this.map.setCenter(odfCenter);
    } catch (error) {
      console.error("Failed to set center:", error);
      throw error;
    }
  }

  /**
   * 지도 줌 레벨 설정
   */
  setZoom(zoom: number): void {
    try {
      this.map.setZoom(zoom);
    } catch (error) {
      console.error("Failed to set zoom:", error);
      throw error;
    }
  }

  /**
   * 좌표 변환
   */
  transformCoordinate(
    coords: Coordinate,
    sourceProjection: ProjectionCode,
  ): Coordinate {
    try {
      const projection = this.map.getProjection();
      return projection.project(coords, sourceProjection);
    } catch (error) {
      console.error("Failed to transform coordinate:", error);
      throw error;
    }
  }

  /**
   * 지도 인스턴스 반환 (필요시)
   */
  getMapInstance(): any {
    return this.map;
  }

  /**
   * 지도 중심점 조회
   */
  getCenter(): Coordinate {
    try {
      const center = this.map.getCenter();
      return fromODFCoordinate(center);
    } catch (error) {
      console.error("Failed to get center:", error);
      throw error;
    }
  }

  /**
   * 지도 줌 레벨 조회
   */
  getZoom(): number {
    try {
      return this.map.getZoom();
    } catch (error) {
      console.error("Failed to get zoom:", error);
      throw error;
    }
  }

  /**
   * 지도 투영법 조회
   */
  getProjection(): ProjectionCode {
    try {
      return this.map.getProjection().getCode();
    } catch (error) {
      console.error("Failed to get projection:", error);
      throw error;
    }
  }
}
