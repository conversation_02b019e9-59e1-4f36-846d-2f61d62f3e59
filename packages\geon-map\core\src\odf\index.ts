/**
 * ODF 라이브러리 래퍼
 * 외부 ODF 라이브러리에 대한 타입 안전한 인터페이스 제공
 */

import { ODF as ODF_TYPE } from "../types";

// ODF 전역 타입 선언
declare global {
  interface Window {
    odf: ODF_TYPE | any;
  }
}

declare const odf: ODF_TYPE;

/**
 * ODF 라이브러리 가용성 체크
 */
export function checkODFAvailability(): Promise<ODF_TYPE> {
  return new Promise<ODF_TYPE>((resolve, reject) => {
    let attempts = 0;
    const maxAttempts = 30; // 3초 동안 100ms 간격으로 체크
    const interval = setInterval(() => {
      attempts++;
      if (typeof (globalThis as ODF_TYPE).odf !== "undefined") {
        clearInterval(interval);
        resolve(odf);
      } else if (attempts >= maxAttempts) {
        clearInterval(interval);
        reject(
          new Error(
            "ODF가 프로젝트에 추가되지 않았습니다. ODF를 프로젝트에 추가해주세요.",
          ),
        );
      }
    }, 100);
  });
}

/**
 * ODF 객체에 대한 타입 안전한 접근자
 */
export class ODF {
  /**
   * ODF 라이브러리가 로드되었는지 확인
   */
  static isAvailable(): boolean {
    return typeof (globalThis as any).odf !== "undefined";
  }

  /**
   * ODF 인스턴스 반환
   */
  static getInstance(): any {
    if (!this.isAvailable()) {
      throw new Error("ODF library is not loaded");
    }
    return (globalThis as any).odf;
  }

  // Map 관련
  static get Map() {
    return this.getInstance().Map;
  }

  static get Coordinate() {
    return this.getInstance().Coordinate;
  }

  static get Projection() {
    return this.getInstance().Projection;
  }

  // Layer 관련
  static get LayerFactory() {
    return this.getInstance().LayerFactory;
  }

  // Style 관련
  static get StyleFactory() {
    return this.getInstance().StyleFactory;
  }

  // Marker 관련
  static get Marker() {
    return this.getInstance().Marker;
  }

  // Event 관련
  static get event() {
    return this.getInstance().event;
  }

  // Control 관련
  static get BasemapControl() {
    return this.getInstance().BasemapControl;
  }

  static get ZoomControl() {
    return this.getInstance().ZoomControl;
  }

  static get MoveControl() {
    return this.getInstance().MoveControl;
  }

  static get MeasureControl() {
    return this.getInstance().MeasureControl;
  }

  static get DrawControl() {
    return this.getInstance().DrawControl;
  }

  static get ClearControl() {
    return this.getInstance().ClearControl;
  }

  static get MousePositionControl() {
    return this.getInstance().MousePositionControl;
  }

  static get ScaleControl() {
    return this.getInstance().ScaleControl;
  }

  static get PrintControl() {
    return this.getInstance().PrintControl;
  }

  static get DownloadControl() {
    return this.getInstance().DownloadControl;
  }

  static get LayerInfoControl() {
    return this.getInstance().LayerInfoControl;
  }

  static get AreaNameControl() {
    return this.getInstance().AreaNameControl;
  }

  static get DivideMapControl() {
    return this.getInstance().DivideMapControl;
  }

  static get ZipControl() {
    return this.getInstance().ZipControl;
  }

  static get FullScreenControl() {
    return this.getInstance().FullScreenControl;
  }

  static get RotationControl() {
    return this.getInstance().RotationControl;
  }

  static get BookmarkControl() {
    return this.getInstance().BookmarkControl;
  }

  static get HomeControl() {
    return this.getInstance().HomeControl;
  }

  static get TimeSliderControl() {
    return this.getInstance().TimeSliderControl;
  }

  // Factory 관련
  static get ColorFactory() {
    return this.getInstance().ColorFactory;
  }

  static get FeatureFactory() {
    return this.getInstance().FeatureFactory;
  }

  static get GeometryFactory() {
    return this.getInstance().GeometryFactory;
  }

  static get FormatFactory() {
    return this.getInstance().FormatFactory;
  }

  // 기타
  static get Extent() {
    return this.getInstance().Extent;
  }

  static get Easing() {
    return this.getInstance().Easing;
  }

  static get Render() {
    return this.getInstance().Render;
  }

  static get Popup() {
    return this.getInstance().Popup;
  }
}

// 편의 함수들 (ODF 원본 API와 동일한 인터페이스)
export function map(mapContainer: HTMLElement, mapOption: any) {
  return new ODF.Map(mapContainer, mapOption);
}

export function coordinate(x: number, y: number) {
  return new ODF.Coordinate(x, y);
}

export function marker(options: any) {
  return new ODF.Marker(options);
}

export function basemapControl(basemapList?: any, urls?: any) {
  return new ODF.BasemapControl(basemapList, urls);
}

export function zoomControl(options?: any) {
  return new ODF.ZoomControl(options);
}

export function measureControl(options?: any) {
  return new ODF.MeasureControl(options);
}

export function drawControl(options?: any) {
  return new ODF.DrawControl(options);
}

// 설정 관련 함수들
export function init(configUrl: string) {
  const odfInstance = ODF.getInstance();
  if (odfInstance.Configs && odfInstance.Configs.init) {
    odfInstance.Configs.init(configUrl);
  }
}

export function initConfig(configJSON: any) {
  const odfInstance = ODF.getInstance();
  if (odfInstance.Configs && odfInstance.Configs.initConfig) {
    odfInstance.Configs.initConfig(configJSON);
  }
}
