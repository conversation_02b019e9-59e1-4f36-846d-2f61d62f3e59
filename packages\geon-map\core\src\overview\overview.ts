
import { ODF, ODF_MAP } from "../types";
import { OverviewMapPositionType } from "../types/overview";

export class Overview {
  #overviewMapControl: any = null;
  #map: ODF_MAP;
  #odf: ODF;
  constructor(map: ODF_MAP, odf: ODF) {
    if (!map || !odf) {
      throw new Error("Overview 초기화 시 map과 odf가 필요합니다.");
    }
    this.#map = map;
    this.#odf = odf;
  }
  setControl(): void {
    if (this.#overviewMapControl === null) {
      this.#overviewMapControl = new this.#odf.OverviewMapControl();
    }
  }
  getState(): boolean {
    if (!this.#overviewMapControl) {
      console.warn("OverviewMapControl이 초기화되지 않았습니다.");
      return false;
    }
    return this.#overviewMapControl.getState();
  }
  changeState(): void {
    if (!this.#overviewMapControl) {
      console.warn("OverviewMapControl이 초기화되지 않았습니다. setMap()을 먼저 호출하세요.");
      return;
    }
    this.#overviewMapControl.changeState();
  }
  setMap(createElementFlag: boolean = false): void {
    this.setControl();
    this.#overviewMapControl.setMap(this.#map, createElementFlag);
  }
  getOverviewMapControl() {
    if (!this.#overviewMapControl) {
      console.warn("OverviewMapControl이 초기화되지 않았습니다. setMap()을 먼저 호출하세요.");
      return null;
    }
    return this.#overviewMapControl;
  }
  changeOverviewMapPosition(positionType: OverviewMapPositionType): void {
    // this.#overviewMapControl.changeOverviewMapPosition(positionType);
    const el = document.querySelector(
      ".ol-overviewmap-map",
    ) as HTMLElement | null;
    if (!el) return;
    // 기존 위치 제거
    el.style.removeProperty("top");
    el.style.removeProperty("bottom");
    el.style.removeProperty("left");
    el.style.removeProperty("right");
    const gap = "0.5em";
    // 새 위치 적용 (강제 적용: !important)
    switch (positionType) {
      case "left-up":
        el.style.setProperty("top", gap, "important");
        el.style.setProperty("left", gap, "important");
        break;
      case "left-down":
        el.style.setProperty("bottom", gap, "important");
        el.style.setProperty("left", gap, "important");
        break;
      case "right-up":
        el.style.setProperty("top", gap, "important");
        el.style.setProperty("right", gap, "important");
        break;
      case "right-down":
        el.style.setProperty("bottom", gap, "important");
        el.style.setProperty("right", gap, "important");
        break;
    }
  }

}
