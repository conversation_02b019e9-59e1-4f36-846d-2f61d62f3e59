/**
 * 포인트 타입 정의
 */
export type Point = [number, number];

/**
 * 범위 타입 정의
 */
export type Extent = [number, number, number, number]; // [minX, minY, maxX, maxY]

/**
 * 맵에서 투영 객체를 가져와서 사용하기 쉽게 래핑하는 클래스
 */
export class MapProjection {
  private projection: any;

  constructor(map: any) {
    this.projection = map.getProjection();
  }

  /**
   * 지오메트리 투영 (입력 좌표계 -> 지도 좌표계)
   */
  projectGeom(feature: any, epsgCd: string): any {
    return this.projection.projectGeom(feature, epsgCd);
  }

  /**
   * 포인트의 좌표계를 지도 좌표계 -> 입력 좌표계로 변경한다.
   */
  unprojectPoint(point: [number, number], epsgCd: string): [number, number] {
    return this.projection.unproject(point, epsgCd);
  }
}

/**
 * 맵 투영 객체 생성 함수
 */
export function createMapProjection(map: any): MapProjection {
  return new MapProjection(map);
}
