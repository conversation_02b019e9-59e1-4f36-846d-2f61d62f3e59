
import { ODF, ODF_MAP } from "../types";
import { ScaleOption } from "./../types/scale";

export class Scale {
  private scaleControl: any = null;
  private map: ODF_MAP;
  private odf: ODF;

  constructor(map: ODF_MAP, odf: ODF) {
    if (!map || !odf) {
      throw new Error("Scale 초기화 시 map과 odf가 필요합니다.");
    }
    this.map = map;
    this.odf = odf;
  }
  //ScaleControl 생성자
  setControl(option: ScaleOption = { size: 100, scaleInput: false }): void {
    if (this.scaleControl === null) {
      this.scaleControl = new this.odf.ScaleControl(option);
    }
  }
  //현재 지도 축척 산출 기준에따른 축척 값 조회
  setScaleValue(scale: number): void {
    //축척값 ex) 1:5000 -> 5000 입력10
    this.scaleControl.setScaleValue(scale);
  }
  //ScaleControl의 기준 픽셀 크기 변경
  setPixelSize() {
    this.scaleControl.setPixelSize(); //ScaleControl 기준 픽셀 크기 변경
  }
  setMap(createElementFlag: boolean = false) {
    this.setControl();
    this.scaleControl.setMap(this.map, createElementFlag);
  }
  //현재 지도 축척 산출 기준에따른 축척 값 조회
  getScaleValue(): string {
    return this.scaleControl.getScaleValue();
  }
  //ScaleControl의 축척 산출 기준 size(단위: pixel) 조회
  getPixelSize(size: number): number {
    return this.scaleControl.getPixelSize(size);
  }
  //컨트롤 반환
  getScaleControl() {
    return this.scaleControl;
  }

}
