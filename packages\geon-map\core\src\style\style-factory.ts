import type { LayerType } from '../types/layer';

/**
 * 스타일 팩토리 클래스
 */
export class StyleFactory {
  /**
   * ODF 스타일 객체 생성
   */
  static createODFStyle(style: any): any {
    if (typeof (globalThis as any).odf === 'undefined') {
      throw new Error('ODF library is not loaded');
    }
    
    return (globalThis as any).odf.StyleFactory.produce(style);
  }

  /**
   * ODF SLD 스타일 객체 생성
   */
  static createODFSLDStyle(style: any): any {
    if (typeof (globalThis as any).odf === 'undefined') {
      throw new Error('ODF library is not loaded');
    }
    
    return (globalThis as any).odf.StyleFactory.produceSLD(style);
  }
}

/**
 * 레이어에 스타일 적용
 */
export function applyLayerStyle(
  odfLayer: any, 
  style: any, 
  layerType: LayerType, 
  service?: string
): void {
  if (!style) return;

  try {
    if (layerType === 'geoserver' && service === 'wms') {
      const sldStyle = StyleFactory.createODFSLDStyle(style);
      odfLayer.setSLD(sldStyle);
    } else {
      const odfStyle = StyleFactory.createODFStyle(style);
      odfLayer.setStyle(odfStyle);
    }
  } catch (error) {
    console.error('Failed to apply style:', error);
  }
}
