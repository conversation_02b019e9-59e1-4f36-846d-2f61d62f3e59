/**
 * ODF Style 핵심 클래스
 * 스타일 파싱, 검증, 변환을 담당
 */
export class Style {
  /**
   * 스타일 객체를 안전하게 파싱
   */
  static parseStyle(style: any): any {
    if (typeof style === "string") {
      try {
        return JSON.parse(style);
      } catch (error) {
        console.error("Failed to parse style string:", error);
        return null;
      }
    }
    return style;
  }

  /**
   * 스타일 객체 유효성 검사
   */
  static validateStyle(style: any): boolean {
    if (!style || typeof style !== "object") {
      return false;
    }

    // 기본적인 스타일 속성 검사
    // 실제 ODF 스타일 스키마에 맞게 조정 필요
    return true;
  }

  /**
   * 기본 스타일 생성
   */
  static createDefaultStyle(layerType: string): any {
    switch (layerType) {
      case "geoserver":
        return {
          fill: {
            color: "rgba(255, 255, 255, 0.6)",
          },
          stroke: {
            color: "#319FD3",
            width: 1,
          },
        };

      case "geojson":
      case "kml":
        return {
          fill: {
            color: "rgba(255, 255, 255, 0.6)",
          },
          stroke: {
            color: "#319FD3",
            width: 1,
          },
          circle: {
            radius: 5,
            fill: {
              color: "#319FD3",
            },
          },
        };

      default:
        return {};
    }
  }
}
