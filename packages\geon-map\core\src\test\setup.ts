// 테스트 환경 설정
import { vi } from 'vitest';

// ODF 모킹
const mockODF = {
  Map: vi.fn(),
  Coordinate: vi.fn(),
  BasemapControl: vi.fn(),
  LayerFactory: {
    produce: vi.fn(),
  },
  StyleFactory: {
    produce: vi.fn(),
    produceSLD: vi.fn(),
  },
  Marker: vi.fn(),
  event: {
    addListener: vi.fn(),
    removeListener: vi.fn(),
  },
};

// 전역 odf 객체 모킹
(global as any).odf = mockODF;
(global as any).window = { odf: mockODF };
