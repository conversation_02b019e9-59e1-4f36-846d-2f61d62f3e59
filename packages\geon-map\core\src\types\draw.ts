// import type { Coordinate } from "./common"; // 현재 사용하지 않음

/**
 * ODF DrawControl에서 지원하는 그리기 도구 타입
 */
export type DrawToolType =
  | "text"
  | "polygon"
  | "lineString"
  | "point"
  | "circle"
  | "curve"
  | "box"
  | "rectangle"
  | "square"
  | "buffer";

/**
 * 그리기 모드 (React 컴포넌트에서 사용)
 */
export type DrawingMode =
  | "point"
  | "lineString"
  | "polygon"
  | "box"
  | "circle"
  | "text"
  | "curve"
  | "buffer"
  | "measure-distance"
  | "measure-area"
  | "measure-round"
  | "measure-spot";

/**
 * 측정 모드 (MeasureControl에서 사용)
 */
export type MeasureMode =
  | "measure-distance"
  | "measure-area"
  | "measure-round"
  | "measure-spot";

/**
 * ODF DrawControl 스타일 옵션
 */
export interface DrawStyleOptions {
  fill?: {
    color: [number, number, number, number];
  };
  stroke?: {
    color: [number, number, number, number];
    width: number;
  };
  image?: {
    circle?: {
      fill?: {
        color: [number, number, number, number];
      };
      stroke?: {
        color: [number, number, number, number];
        width: number;
      };
      radius: number;
    };
  };
  text?: {
    textAlign: string;
    font: string;
    fill?: {
      color: [number, number, number, number];
    };
    stroke?: {
      color: [number, number, number, number];
    };
  };
}

/**
 * ODF DrawControl 메시지 옵션
 */
export interface DrawMessageOptions {
  DRAWSTART_POINT?: string;
  DRAWSTART_LINESTRING?: string;
  DRAWSTART_POLYGON?: string;
  DRAWSTART_CURVE?: string;
  DRAWSTART_TEXT?: string;
  DRAWSTART_BUFFER?: string;
  DRAWSTART_CIRCLE?: string;
  DRAWSTART_BOX?: string;
  DRAWEND_DRAG?: string;
  DRAWEND_DBCLICK?: string;
}

/**
 * ODF DrawControl 생성 옵션
 */
export interface DrawControlOptions {
  /** 연속 측정 여부 */
  continuity?: boolean;
  /** 측정 옵션 활성화 여부 (선 그리기/원그리기 툴에서 활성화) */
  measure?: boolean;
  /** drawControl 생성 시 새 레이어 생성 여부 */
  createNewLayer?: boolean;
  /** 우클릭 편집 기능 */
  editFeatureMenu?: string[];
  /** 생성할 툴 배열 */
  tools?: DrawToolType[];
  /** 툴팁 메세지 변경 */
  message?: DrawMessageOptions;
  /** 그리기 도형 스타일 */
  style?: DrawStyleOptions;
  /** 버퍼 도형 스타일 */
  bufferStyle?: DrawStyleOptions;
}

/**
 * 그리기 이벤트 타입
 */
export type DrawEventType =
  | "drawstart"
  | "drawend"
  | "featureselect"
  | "contextmenu";

/**
 * 그리기 이벤트 핸들러
 */
export interface DrawEventHandlers {
  onDrawStart?: (feature: any) => void;
  onDrawEnd?: (feature: any) => void;
  onFeatureSelect?: (feature: any) => void;
  onContextMenu?: (info: any) => void;
}

/**
 * 그린 도형 정보
 */
export interface DrawingFeature {
  id: string;
  type: DrawingMode;
  coordinates: any;
  geometry?: any;
  properties?: Record<string, any>;
  style?: DrawStyleOptions;
  createdAt: Date;
}

/**
 * 선택된 feature 정보
 */
export interface SelectedFeature {
  feature: any;
  id: string;
  type: DrawingMode;
  layerType: "draw" | "measure"; // 레이어 구분 추가
  coordinates: any[];
  properties: any;
}

/**
 * 컨텍스트 메뉴 정보
 */
export interface ContextMenuInfo {
  pixel: [number, number]; // pixel 좌표 [x, y]
  position: [number, number]; // 지리적 좌표 [longitude, latitude]
  feature: SelectedFeature | null;
  visible: boolean;
}

/**
 * 그리기 상태
 */
export interface DrawState {
  /** 현재 활성화된 그리기 모드 */
  activeMode: DrawingMode;
  /** 그린 도형들 */
  features: DrawingFeature[];
  /** 그리기 중인지 여부 */
  isDrawing: boolean;
  /** 연속 그리기 모드인지 여부 */
  isContinuous: boolean;
}

/**
 * 그리기 인터페이스 (단일 책임)
 */
export interface IDraw {
  /** DrawControl 인스턴스 초기화 */
  initialize(options?: DrawControlOptions): void;

  /** 그리기 시작 */
  startDrawing(mode: DrawingMode): void;

  /** 그리기 중지 */
  stopDrawing(): void;

  /** 모든 그리기 삭제 */
  clear(): void;

  /** 그리기 레이어 조회 */
  getDrawLayer(): any;

  /** GeoJSON 내보내기 */
  exportToGeoJSON(): any;

  /** GeoJSON 불러오기 */
  importFromGeoJSON(geojson: any): boolean;

  /** 그리기 컨트롤 인스턴스 반환 */
  getDrawControl(): any;

  /** feature 삭제 */
  deleteFeature(feature: any): boolean;

  /** feature 스타일 변경 */
  changeFeatureStyle(feature: any, styleOptions: any): boolean;

  /** 지도에서 제거 */
  destroy(): void;
}

/**
 * MeasureControl 표시 옵션
 */
export interface MeasureDisplayOption {
  area?: {
    decimalPoint?: number;
    transformUnit?: number;
  };
  distance?: {
    decimalPoint?: number;
    transformUnit?: number;
  };
  round?: {
    decimalPoint?: number;
    transformUnit?: number;
  };
}

/**
 * MeasureControl 메시지 옵션
 */
export interface MeasureMessageOptions {
  DRAWSTART?: string;
  DRAWEND_POLYGON?: string;
  DRAWEND_LINE?: string;
}

/**
 * MeasureControl 생성 옵션
 */
export interface MeasureControlOptions {
  /** 거리 측정 표시 옵션 */
  displayOption?: MeasureDisplayOption;
  /** 측정 도형 style 설정 옵션 */
  style?: DrawStyleOptions;
  /** 툴팁 메세지 */
  message?: MeasureMessageOptions;
  /** 연속 측정 여부 */
  continuity?: boolean;
  /** 우클릭 삭제 기능 활성화 여부 */
  rightClickDelete?: boolean;
  /** 생성할 툴 배열 */
  tools?: Array<"distance" | "area" | "round" | "spot">;
  /** 좌표측정시 사용할 좌표계 */
  spotProjection?: string;
}

/**
 * 측정 이벤트 타입
 */
export type MeasureEventType = "drawstart" | "drawend";

/**
 * 측정 결과 정보
 */
export interface MeasureFeature extends DrawingFeature {
  /** 측정 타입 */
  measurementType: MeasureMode;
  /** 측정 값 */
  measurementValue: number;
  /** 측정 단위 */
  measurementUnit: string;
}

/**
 * 측정 인터페이스 (단일 책임)
 */
export interface IMeasure {
  /** 측정 시작 */
  startMeasuring(mode: MeasureMode): void;

  /** 측정 중지 */
  stopMeasuring(): void;

  /** 모든 측정 결과 삭제 */
  clear(): void;

  /** 측정 레이어 조회 */
  getMeasureLayer(): any;

  /** 툴팁 제거 */
  removeHelpTooltip(): void;

  /** 현재 측정 옵션 반환 */
  getCurrentOptions(): MeasureControlOptions | null;

  /** 측정 옵션 업데이트 */
  updateOptions(options: Partial<MeasureControlOptions>): void;

  /** MeasureControl 인스턴스 반환 */
  getMeasureControl(): any;

  /** 측정 결과를 MeasureFeature로 변환 */
  convertToMeasureFeature(
    feature: any,
    measureType: MeasureMode,
  ): MeasureFeature;

  /** 지도에서 제거 */
  destroy(): void;
}
