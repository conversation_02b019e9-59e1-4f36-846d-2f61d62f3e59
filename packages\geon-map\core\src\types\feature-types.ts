// 지오메트리 타입 정의
export type GeometryType =
  | "point"
  | "multipoint"
  | "linestring"
  | "multilinestring"
  | "polygon"
  | "multipolygon"
  | "circle";

// 좌표 타입 정의
export type Coordinate = [number, number]; // [x, y]
export type CoordinateArray = Coordinate[]; // [[x, y], [x, y], [x, y]]
export type CoordinateNestedArray = CoordinateArray[]; // [[[x, y], [x, y]], [[x, y], [x, y]]]
export type CoordinateDoubleNestedArray = CoordinateNestedArray[]; // [[[[x, y], [x, y]], [[x, y], [x, y]]], [[[x, y], [x, y]]]]

// 피쳐 생성 옵션 인터페이스
export interface FeatureOptions {
  geometryType: GeometryType;
  coordinates:
    | Coordinate
    | CoordinateArray
    | CoordinateNestedArray
    | CoordinateDoubleNestedArray;
  circleSize?: number; // circle 타입일 때만 사용
  properties?: Record<string, any>;
}

// 포인트 피쳐 생성 옵션
export interface PointFeatureOptions {
  coordinates: Coordinate;
  properties?: Record<string, any>;
}

// 멀티포인트 피쳐 생성 옵션
export interface MultiPointFeatureOptions {
  coordinates: CoordinateArray;
  properties?: Record<string, any>;
}

// 라인 피쳐 생성 옵션
export interface LineFeatureOptions {
  coordinates: CoordinateArray;
  properties?: Record<string, any>;
}

// 멀티라인 피쳐 생성 옵션
export interface MultiLineFeatureOptions {
  coordinates: CoordinateNestedArray;
  properties?: Record<string, any>;
}

// 폴리곤 피쳐 생성 옵션
export interface PolygonFeatureOptions {
  coordinates: CoordinateNestedArray;
  properties?: Record<string, any>;
}

// 멀티폴리곤 피쳐 생성 옵션
export interface MultiPolygonFeatureOptions {
  coordinates: CoordinateDoubleNestedArray;
  properties?: Record<string, any>;
}

// 원 피쳐 생성 옵션
export interface CircleFeatureOptions {
  coordinates: Coordinate;
  circleSize: number;
  properties?: Record<string, any>;
}
