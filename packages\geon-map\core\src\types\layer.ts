import type { BaseOptions, ProjectionCode } from './common';

export type LayerType = 'geoserver' | 'geotiff' | 'geojson' | 'kml' | 'csv' | 'api' | 'svg';

export interface LayerInfo {
  lyrId: string;
  lyrNm: string;
}

export interface RenderOptions {
  style?: any;
  [key: string]: any;
}

export interface BaseLayerOptions extends BaseOptions {
  type: LayerType;
  name?: string;
  renderOptions?: RenderOptions;
  autoFit?: boolean;
  fitDuration?: number;
}

export interface GeoserverLayerOptions extends BaseLayerOptions {
  type: 'geoserver';
  server: string | {
    url: string;
    version?: string;
    proxyURL?: string;
    proxyParam?: string;
  };
  layer: string;
  service: 'wms' | 'wfs';
  method?: 'get' | 'post';
  bbox?: boolean;
  crtfckey?: string;
  projection?: ProjectionCode;
  limit?: number;
  tiled?: boolean;
  geometryType?: string;
  serviceTy?: string;
  info?: LayerInfo;
}

export interface GeoTiffLayerOptions extends BaseLayerOptions {
  type: 'geotiff';
  sources: any[];
  normalize?: boolean;
  wrapX?: boolean;
  opaque?: boolean;
  transition?: number;
}

export interface GeoJSONLayerOptions extends BaseLayerOptions {
  type: 'geojson';
  data: any;
  dataProjectionCode?: ProjectionCode;
  featureProjectionCode?: ProjectionCode;
  service?: string;
}

export interface KMLLayerOptions extends BaseLayerOptions {
  type: 'kml';
  data: any;
  dataProjectionCode?: ProjectionCode;
  featureProjectionCode?: ProjectionCode;
}

export interface CSVLayerOptions extends BaseLayerOptions {
  type: 'csv';
  data: any;
  dataProjectionCode?: ProjectionCode;
  featureProjectionCode?: ProjectionCode;
  geometryColumnName?: string;
  delimiter?: string;
}

export interface APILayerOptions extends BaseLayerOptions {
  type: 'api';
  server: string | {
    url: string;
    proxyURL?: string;
    proxyParam?: string;
  };
  service: string;
  bbox?: boolean;
  tiled?: boolean;
  tileGrid?: any;
  originalOption?: any;
  parameterFilter?: any;
}

export interface SVGLayerOptions extends BaseLayerOptions {
  type: 'svg';
  svgContainer: any;
  extent?: any;
}

export type LayerOptions =
  | GeoserverLayerOptions
  | GeoTiffLayerOptions
  | GeoJSONLayerOptions
  | KMLLayerOptions
  | CSVLayerOptions
  | APILayerOptions
  | SVGLayerOptions;

export interface LayerState {
  id: string;
  name: string;
  type: LayerType;
  visible: boolean;
  opacity: number;
  zIndex: number;
  style?: any;
  filter?: string;
  params: any;
  info?: LayerInfo;
}
