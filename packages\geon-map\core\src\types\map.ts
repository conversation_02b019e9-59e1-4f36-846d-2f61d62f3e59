import type { Coordinate, ProjectionCode } from "./common";

//TODO ODF 타입 any로 설정 추후에 type화??
export interface ODF {
  [key: string]: any;
}
//TODO ODF_MAP 타입 any로 설정 추후에 type화??
export interface ODF_MAP {
  [key: string]: any;
}
export interface BasemapConfig {
  baroEMap?: string[];
  [key: string]: any;
}

export interface MapInitializeOptions {
  center?: Coordinate;
  zoom?: number;
  projection?: ProjectionCode;
  basemap?: BasemapConfig;
  baroEMapURL?: string;
  baroEMapAirURL?: string;
  optimization?: boolean;
}

export interface ViewState {
  center: Coordinate;
  zoom: number;
  projection: ProjectionCode;
}

export interface MapEventHandlers {
  onMapInit?: (map: any) => void;
  onViewChange?: (viewState: ViewState) => void;
  onClick?: (event: any) => void;
  onMoveEnd?: (event: any) => void;
}

export type BasemapInfo = {
  bcrnMapClCodeNm: string;
  bcrnMapNm: string;
  base64: string;
  mapUrl: string;
  lyrStleCodeNm: string;
  mapUrlparamtr: string;
};
