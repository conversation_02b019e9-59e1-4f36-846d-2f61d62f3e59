import type { BaseOptions, Coordinate, EventCallback } from "./common";

export interface MarkerOptions extends BaseOptions {
  position: Coordinate;
  draggable?: boolean;
  positioning?:
    | "bottom-center"
    | "center"
    | "top-center"
    | "bottom-left"
    | "bottom-right";
  offset?: [number, number];
  stopEvent?: boolean;
  style?: any;
  autoPan?: boolean;
  autoPanAnimation?: number;
  autoPanMargin?: number;
}

export interface MarkerEventHandlers {
  onClick?: EventCallback;
  onDragStart?: EventCallback;
  onDrag?: EventCallback;
  onDragEnd?: EventCallback;
  onPositionChange?: EventCallback;
  onMapChange?: EventCallback;
}

export interface MarkerState {
  id: string;
  position: Coordinate;
  visible: boolean;
  draggable: boolean;
  style?: any;
}
