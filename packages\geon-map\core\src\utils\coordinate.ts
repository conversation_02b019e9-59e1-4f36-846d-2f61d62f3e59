import type { Coordinate, ProjectionCode } from '../types/common';

/**
 * 배열 유틸리티 함수
 */
export function asArray<T>(value: T | T[] | undefined): T[] {
  return value === undefined ? [] : Array.isArray(value) ? value : [value];
}

/**
 * 객체 깊은 병합 유틸리티
 */
export function deepMerge<T extends Record<string, any>>(target: T, source: Partial<T>): T {
  const result = { ...target };

  for (const key in source) {
    if (source[key] !== undefined) {
      if (typeof source[key] === 'object' && source[key] !== null && !Array.isArray(source[key])) {
        result[key] = deepMerge(result[key] || {} as any, source[key]);
      } else {
        result[key] = source[key] as T[Extract<keyof T, string>];
      }
    }
  }

  return result;
}

/**
 * 고유 ID 생성 유틸리티
 */
export function generateId(prefix: string = 'id'): string {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
}



/**
 * ODF Coordinate 객체 생성
 */
export function createODFCoordinate(coords: Coordinate): any {
  if (typeof (globalThis as any).odf === 'undefined') {
    throw new Error('ODF library is not loaded');
  }
  return new (globalThis as any).odf.Coordinate(coords[0], coords[1]);
}

/**
 * 좌표 변환
 */
export function transformCoordinate(
  map: any,
  coords: Coordinate,
  sourceProjection: ProjectionCode,
): Coordinate {
  if (!map) {
    throw new Error('Map instance is required for coordinate transformation');
  }

  const projection = map.getProjection();
  const transformed = projection.project(coords, sourceProjection);
  return transformed;
}

/**
 * ODF Coordinate를 일반 좌표 배열로 변환
 */
export function fromODFCoordinate(odfCoordinate: any): Coordinate {
  // null이나 undefined인 경우
  if (!odfCoordinate) {
    console.warn('ODF Coordinate is null or undefined');
    return [0, 0];
  }

  // 이미 배열인 경우 (일부 ODF 버전에서 배열로 반환)
  if (Array.isArray(odfCoordinate) && odfCoordinate.length >= 2) {
    return [odfCoordinate[0], odfCoordinate[1]];
  }

  // ODF Coordinate 객체인 경우
  if (typeof odfCoordinate.getX === 'function' && typeof odfCoordinate.getY === 'function') {
    return [odfCoordinate.getX(), odfCoordinate.getY()];
  }

  // 객체에 x, y 속성이 있는 경우
  if (typeof odfCoordinate.x === 'number' && typeof odfCoordinate.y === 'number') {
    return [odfCoordinate.x, odfCoordinate.y];
  }

  // _x, _y 속성이 있는 경우 (일부 ODF 내부 구조)
  if (typeof odfCoordinate._x === 'number' && typeof odfCoordinate._y === 'number') {
    return [odfCoordinate._x, odfCoordinate._y];
  }

  // 모든 경우에 해당하지 않으면 경고 후 기본값 반환
  console.warn('Unknown ODF Coordinate format:', odfCoordinate);
  return [0, 0];
}
