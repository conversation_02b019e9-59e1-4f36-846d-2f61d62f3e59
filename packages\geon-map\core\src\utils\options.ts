/**
 * 옵션 객체에서 특정 키의 활성화 여부를 결정하는 유틸리티
 *
 * @param options - 옵션 객체 (키: boolean | undefined)
 * @param key - 확인할 키
 * @param defaultWhenEmpty - 옵션이 비어있을 때 기본값 (기본값: true)
 * @returns 해당 키가 활성화되어 있는지 여부
 *
 * @example
 * // 명시적으로 설정된 경우
 * const searchTypes = { integrated: true, jibun: false, road: undefined };
 * isOptionEnabled(searchTypes, 'integrated'); // true
 * isOptionEnabled(searchTypes, 'jibun'); // false
 * isOptionEnabled(searchTypes, 'road'); // false (다른 키가 명시적으로 true이므로)
 *
 * @example
 * // 아무것도 설정하지 않은 경우 - 기본값 true (모든 키 활성화)
 * const searchTypes = {};
 * isOptionEnabled(searchTypes, 'integrated'); // true
 * isOptionEnabled(searchTypes, 'jibun'); // true
 *
 * @example
 * // 아무것도 설정하지 않은 경우 - 기본값 false (모든 키 비활성화)
 * const permissions = {};
 * isOptionEnabled(permissions, 'admin', false); // false
 * isOptionEnabled(permissions, 'delete', false); // false
 *
 * @example
 * // 일부만 명시적으로 설정한 경우
 * const visibleFields = { roadAddress: true };
 * isOptionEnabled(visibleFields, 'roadAddress'); // true
 * isOptionEnabled(visibleFields, 'jibunAddress'); // false (다른 키가 명시적으로 true이므로)
 */
export function isOptionEnabled<T extends Record<string, boolean | undefined>>(
  options: T,
  key: keyof T,
  defaultWhenEmpty: boolean = true,
): boolean {
  // 명시적으로 설정된 경우 그 값을 반환
  if (options[key] !== undefined) {
    return options[key] === true;
  }

  // 해당 키가 options에 없는 경우
  // 다른 키들이 명시적으로 true로 설정되어 있으면 false로 처리
  const hasExplicitTrue = Object.values(options).some((v) => v === true);

  if (hasExplicitTrue) {
    return false; // 다른 키가 명시적으로 true면 이 키는 false
  }

  // 아무것도 명시적으로 설정되지 않은 경우 defaultWhenEmpty 반환
  return defaultWhenEmpty;
}
