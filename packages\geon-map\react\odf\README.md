# `@geon/map/react/odf`

`odf` 를 사용한 `React 19` 컴포넌트 패키지.

## Usage

`package.json` 에서 dependencies 에 사용하려는 app 의 React 버전과 같은 디렉토리에서 import 합니다.

```json
{
  ...
  "dependencies": {
    ...
    "@geon-ui/react": "workspace:^",
    "@types/react": "19.1.8",
    "@types/react-dom": "19.1.6",
    "react": "^19.0.0",
    ...
  },
  ...
}
```

해당 패키지 안의 컴포넌트를 `import` 하여 사용할 수 있습니다.

```tsx
"use client";

import { MapProvider } from "@geon/map/react/odf/contexts";


export function Widget() {
  return <></>;
}

```
