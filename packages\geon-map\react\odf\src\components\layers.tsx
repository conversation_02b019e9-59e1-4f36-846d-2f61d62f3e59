"use client";

import React from "react";

import type { LayerProps } from "../types/layer-types";
import { Layer } from "./layer";

interface LayersProps {
  layers: LayerProps[];
  children?: React.ReactNode;
}

export function Layers({ layers, children }: LayersProps) {
  return (
    <>
      {layers.map((layerProps, index) => (
        <Layer key={layerProps.id || index} {...layerProps}>
          {children}
        </Layer>
      ))}
    </>
  );
}
