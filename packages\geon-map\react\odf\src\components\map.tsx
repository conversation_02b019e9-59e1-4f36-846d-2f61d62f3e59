import React, { useEffect } from "react";

import { useMap } from "../hooks/use-map";
import { useMapActions } from "../hooks/use-map-actions";
import { cn } from "../lib/utils";
import type { MapInitializeOptions, UseMapReturn } from "../types/map-types";

interface MapInital extends Partial<MapInitializeOptions> {
  className?: string;
  style?: React.CSSProperties;
  children: React.ReactNode;
  containerRef?: React.RefObject<HTMLDivElement>;
  autoInit?: boolean;
  onMapInit?: (mapState: UseMapReturn) => void;
}

/**
 * Map 컴포넌트 (Components 레이어)
 * useMap, useMapActions 훅을 사용하여 ODF 지도를 초기화하고 상태를 관리합니다.
 * hook 또는 해당 컴포넌트를 사용하여 지도를 렌더링할 수 있습니다.
 *
 * @example
 * ```tsx
 * // layout.tsx에서 전역 설정
 * <MapProvider defaultOptions={{ projection: 'EPSG:4326' }}>
 *   <App />
 * </MapProvider>
 *
 * // 컴포넌트에서 사용
 * <Map
 *   className="w-full h-96"
 *   center={[127, 37]} // 전역 설정보다 우선
 * >
 *   <Layer ... />
 * </Map>
 * ```
 */
export const Map = ({
  className,
  style,
  children,
  onMapInit,
  center,
  zoom,
  projection,
  ...mapInitializeOptions
}: MapInital) => {
  const id = React.useId();
  const containerRef = React.useRef<HTMLDivElement>(null);

  // 통합 지도 훅을 통한 초기화 및 상태 관리
  const { isReady, isLoading, error } = useMap({
    containerRef,
    center,
    zoom,
    projection,
    ...mapInitializeOptions,
    onMapInit,
  });

  // 지도 조작 액션들
  const { setCenter, setZoom } = useMapActions();

  useEffect(() => {
    if (isReady && center && setCenter) {
      setCenter(center);
    }
  }, [center, isReady, setCenter]);

  useEffect(() => {
    if (isReady && zoom !== undefined && setZoom) {
      setZoom(zoom);
    }
  }, [zoom, isReady, setZoom]);

  // 에러 상태 표시
  if (error) {
    return (
      <div
        className={cn("relative flex items-center justify-center", className)}
        style={style}
      >
        <div className="text-red-500">지도 로딩 실패: {error}</div>
      </div>
    );
  }

  return (
    <div className={cn("relative", className)} style={style}>
      <div id={`map-${id}`} ref={containerRef} className="w-full h-full" />
      {/* 지도가 완전히 준비된 후에만 children 렌더링 */}
      {isReady && children}
      {/* 로딩 상태 표시 (선택적) */}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 bg-opacity-75">
          <div className="text-gray-600">지도 로딩 중...</div>
        </div>
      )}
    </div>
  );
};
