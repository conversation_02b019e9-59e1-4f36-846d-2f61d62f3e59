"use client";

import { useEffect, useRef } from "react";

import { useMap } from "../hooks/use-map";
import type { MarkerProps } from "../types/marker-types";

export function Marker({ children, ...props }: MarkerProps) {
  const { map } = useMap();
  const markerRef = useRef<any>(null);

  useEffect(() => {
    if (!map || !window.odf) return;

    const marker = new window.odf.Marker({
      position: props.position,
      draggable: props.draggable ?? false,
      positioning: props.positioning ?? "bottom-center",
      offset: props.offset ?? [0, 0],
      stopEvent: props.stopEvent ?? false,
      style: props.style,
      autoPan: props.autoPan ?? false,
      autoPanAnimation: props.autoPanAnimation ?? 250,
      autoPanMargin: props.autoPanMargin ?? 20,
    });

    marker.setMap(map);
    markerRef.current = marker;

    // 이벤트 리스너 등록
    if (props.onClick) {
      window.odf.event.addListener(marker, "click", (e: any) =>
        props.onClick?.(e, marker),
      );
    }
    if (props.onDragStart) {
      window.odf.event.addListener(marker, "markerdragstart", (e: any) =>
        props.onDragStart?.(e, marker),
      );
    }
    if (props.onDrag) {
      window.odf.event.addListener(marker, "markerdrag", (e: any) =>
        props.onDrag?.(e, marker),
      );
    }
    if (props.onDragEnd) {
      window.odf.event.addListener(marker, "markerdragend", (e: any) =>
        props.onDragEnd?.(e, marker),
      );
    }
    if (props.onPositionChange) {
      window.odf.event.addListener(marker, "change:position", (e: any) =>
        props.onPositionChange?.(e, marker),
      );
    }

    return () => {
      if (markerRef.current) {
        markerRef.current.removeMap();
      }
    };
  }, [
    map,
    props.position,
    props.draggable,
    props.positioning,
    props.offset,
    props.stopEvent,
    props.style,
    props.autoPan,
    props.autoPanAnimation,
    props.autoPanMargin,
  ]);

  return <>{children}</>;
}
