import { useEffect, useRef, useState } from "react";

import { useMap } from "../hooks/use-map";
import type { PopupProps } from "../types/popup-types";

/**
 * 개선된 Popup 컴포넌트
 *
 * 주요 개선사항:
 * - 언마운트 대신 visibility로 제어하여 DOM 조작 에러 방지
 * - 마커 재사용으로 성능 최적화
 * - onOpen/onClose 이벤트 핸들러 지원
 * - 애니메이션 지원
 * - 안전한 cleanup 로직
 */
export function Popup({
  children,
  visible = true,
  onOpen,
  onClose,
  popupKey,
  animationDuration = 200,
  zIndex = 1000,
  ...props
}: PopupProps) {
  const { map } = useMap();
  const containerRef = useRef<HTMLDivElement>(null);
  const markerRef = useRef<any>(null);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isVisible, setIsVisible] = useState(false);
  const cleanupTimeoutRef = useRef<NodeJS.Timeout | null>(null);

  // 마커 초기화 - visible이 true이고 필요한 조건이 충족될 때만 실행
  useEffect(() => {
    // visible이 false이거나 이미 초기화되었으면 스킵
    if (
      !visible ||
      !map ||
      !window.odf ||
      !containerRef.current ||
      isInitialized
    ) {
      return;
    }

    // position이 유효하지 않으면 스킵 (새로고침 시 [0,0] 방지)
    if (
      !props.position ||
      (Array.isArray(props.position) && props.position.every((v) => v === 0))
    ) {
      return;
    }

    try {
      const marker = new window.odf.Marker({
        position: props.position,
        positioning: props.positioning ?? "bottom-center",
        offset: props.offset ?? [0, 0],
        stopEvent: true,
        style: {
          element: containerRef.current,
        },
        autoPan: props.autoPan ?? false,
        autoPanAnimation: props.autoPanAnimation ?? 250,
        autoPanMargin: props.autoPanMargin ?? 20,
      });

      marker.setMap(map);
      markerRef.current = marker;
      setIsInitialized(true);

      console.log(
        `Popup 마커 초기화 완료 ${popupKey ? `(key: ${popupKey})` : ""}`,
      );
    } catch (error) {
      console.error("Popup 마커 초기화 실패:", error);
    }
  }, [map, visible, props.position, isInitialized, popupKey]);

  // 위치 및 옵션 업데이트
  useEffect(() => {
    if (!markerRef.current || !isInitialized) return;

    try {
      markerRef.current.setPosition(props.position);
      if (props.positioning) {
        markerRef.current.setPositioning(props.positioning);
      }
      if (props.offset) {
        markerRef.current.setOffset(props.offset);
      }
    } catch (error) {
      console.warn("Popup 속성 업데이트 중 오류:", error);
    }
  }, [props.position, props.positioning, props.offset, isInitialized]);

  // visible prop 변화에 따른 상태 업데이트 (React 방식)
  useEffect(() => {
    if (visible && isInitialized) {
      // 마커가 초기화된 상태에서만 표시
      setIsVisible(true);
      onOpen?.();
    } else {
      // 숨기기
      setIsVisible(false);
      onClose?.();
    }
  }, [visible, isInitialized, onOpen, onClose]);

  // 컴포넌트 언마운트 시에만 cleanup 실행
  useEffect(() => {
    return () => {
      // timeout 정리
      if (cleanupTimeoutRef.current) {
        clearTimeout(cleanupTimeoutRef.current);
      }

      // 마커가 실제로 존재하고 초기화된 경우에만 제거
      if (markerRef.current && isInitialized) {
        cleanupTimeoutRef.current = setTimeout(() => {
          if (markerRef.current) {
            try {
              if (markerRef.current.getMap && markerRef.current.getMap()) {
                markerRef.current.removeMap();
                console.log(
                  `Popup 마커 제거 완료 ${popupKey ? `(key: ${popupKey})` : ""}`,
                );
              }
            } catch (error) {
              console.warn("Popup cleanup 중 오류 (무시됨):", error);
            } finally {
              markerRef.current = null;
            }
          }
        }, 50);
      }
    };
  }, []); // 의존성 배열을 빈 배열로 변경하여 언마운트 시에만 실행

  return (
    <div
      ref={containerRef}
      className="popup-content"
      style={{
        // React 방식: 상태 기반 조건부 스타일링
        display: isVisible ? "block" : "none",
        opacity: isVisible ? 1 : 0,
        visibility: isVisible ? "visible" : "hidden",
        transition: `opacity ${animationDuration}ms ease-in-out`,
        zIndex,
        pointerEvents: isVisible ? "auto" : "none",
      }}
    >
      {children}
    </div>
  );
}
