"use client";
import React, { memo, useContext, useEffect } from "react";

import { useLayer } from "../providers/layer-provider";
import { LayerContext, LayerContextValue } from "./layer";

export interface StyleProps {
  /**
   * 스타일 값 (GeoJSON 스타일 객체 또는 스타일 문자열)
   */
  style?: any;
  /**
   * 자식 요소 (스타일 문자열인 경우 사용)
   */
  children?: React.ReactNode;
}

/**
 * Style 컴포넌트의 비메모이제이션 버전
 * Layer 컴포넌트 내부에서 스타일을 적용하기 위한 컴포넌트
 */
function NonMemoizedStyle({ children, style }: StyleProps) {
  const layerContext = useContext<LayerContextValue | null>(LayerContext);
  const { updateLayerStyle } = useLayer();

  // children이 문자열이면 style로 사용, 아니면 제공된 style 사용
  const styleValue =
    typeof children === "string"
      ? children
      : style && typeof style === "object" && Object.keys(style).length === 0
        ? null
        : style;

  // 스타일 적용 - styleValue나 layerId가 변경될 때만 실행
  useEffect(() => {
    // 필수 값 확인
    if (!layerContext?.layerId || !styleValue) {
      return;
    }

    try {
      const success = updateLayerStyle(layerContext.layerId, styleValue);

      if (success) {
        console.log("스타일 적용 성공:", layerContext.layerId);
      }
    } catch (error) {
      console.error("스타일 적용 중 오류:", error);
    }
  }, [style]);

  return null;
}

/**
 * 메모이제이션된 Style 컴포넌트
 * props가 변경되지 않으면 컴포넌트를 재렌더링하지 않음
 */
export const Style = memo(NonMemoizedStyle, (prevProps, nextProps) => {
  if (prevProps.children !== nextProps.children) {
    return false;
  }

  if (prevProps.style && nextProps.style) {
    return JSON.stringify(prevProps.style) === JSON.stringify(nextProps.style);
  }

  return prevProps.style === nextProps.style;
});
