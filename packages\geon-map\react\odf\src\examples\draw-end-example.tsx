/**
 * drawEnd 사용 예제
 * 
 * 기존 core에서 분리된 drawEnd 로직을 useDraw 훅에서 사용하는 방법
 */
import React from "react";
import { useDraw } from "../hooks/use-draw";

export function DrawEndExample() {
  const { drawEnd, isDrawing, drawMode } = useDraw();

  const handlePolygonDraw = () => {
    const drawEndResult = drawEnd("polygon");
    
    drawEndResult.completed((feature, eventId) => {
      console.log("폴리곤 그리기 완료!", {
        feature,
        eventId,
        geometry: feature.getGeometry(),
      });
      
      // 여기서 추가 처리 (저장, 분석 등)
      // 예: 면적 계산, 서버 전송 등
    });
  };

  const handlePointDraw = () => {
    const drawEndResult = drawEnd("point");
    
    drawEndResult.completed((feature, eventId) => {
      console.log("포인트 그리기 완료!", {
        feature,
        eventId,
        coordinates: feature.getGeometry().getCoordinates(),
      });
    });
  };

  return (
    <div className="p-4 space-y-4">
      <h3 className="text-lg font-semibold">DrawEnd 예제</h3>
      
      <div className="space-x-2">
        <button
          onClick={handlePolygonDraw}
          disabled={isDrawing}
          className="px-4 py-2 bg-blue-500 text-white rounded disabled:opacity-50"
        >
          폴리곤 그리기
        </button>
        
        <button
          onClick={handlePointDraw}
          disabled={isDrawing}
          className="px-4 py-2 bg-green-500 text-white rounded disabled:opacity-50"
        >
          포인트 그리기
        </button>
      </div>
      
      {isDrawing && (
        <div className="p-2 bg-yellow-100 rounded">
          현재 그리기 중: {drawMode}
        </div>
      )}
    </div>
  );
}
