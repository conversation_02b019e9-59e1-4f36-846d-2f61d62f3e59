import { DrawingMode } from "@geon-map/core";
import { useCallback } from "react";

import { useDrawProviderStatus } from "../providers/draw-provider";
import { useDrawStore } from "../stores/draw-store";
import { useEvent } from "./use-event";

/**
 * 🎯 새로운 useDraw 훅 (Zustand Store 기반)
 *
 * DrawProvider(설정) + DrawStore(상태/인스턴스) + Hook(상호작용)을 조합하여
 * 완전한 그리기 기능을 제공합니다.
 *
 * @example
 * ```tsx
 * <DrawProvider drawOptions={{ tools: ['polygon'] }}>
 *   <DrawingComponent />
 * </DrawProvider>
 *
 * function DrawingComponent() {
 *   const {
 *     startDrawing,
 *     stopDrawing,
 *     selectedFeature,
 *     contextMenuInfo,
 *     deleteSelectedFeature
 *   } = useDrawNew({
 *     enableSelection: true,
 *     enableContextMenu: true
 *   });
 *
 *   return (
 *     <div>
 *       <button onClick={() => startDrawing('polygon')}>
 *         Draw Polygon
 *       </button>
 *       {selectedFeature && (
 *         <div>Selected: {selectedFeature.type}</div>
 *       )}
 *     </div>
 *   );
 * }
 * ```
 */

export interface UseDrawNewOptions {
  /** 피처 선택 기능 활성화 */
  enableSelection?: boolean;
  /** 컨텍스트 메뉴 기능 활성화 */
  enableContextMenu?: boolean;
  /** 다중 선택 허용 */
  multiSelect?: boolean;
  /** 피처 삭제 시 콜백 */
  onFeatureDelete?: (feature: any) => void;
  /** 피처 편집 시 콜백 */
  onFeatureEdit?: (feature: any) => void;
}

// drawEnd 콜백 타입
export type DrawEndCallback = (feature: any, eventId: string) => void;

// drawEnd 반환 타입
export interface DrawEndResult {
  drawend: (callback: DrawEndCallback) => void;
}

export function useDraw(options: UseDrawNewOptions = {}) {
  const {
    enableSelection = true,
    enableContextMenu = true,
    multiSelect = false,
  } = options;

  // useEvent 훅 사용 (이벤트 관리)
  const { registerListener } = useEvent();

  // DrawProvider 설정 여부 확인
  const { isProviderSet } = useDrawProviderStatus();

  // ✅ Draw Store에서 상태 및 액션 가져오기 (4개 Core 통합)
  const drawCore = useDrawStore((state) => state.drawCore);
  const measureCore = useDrawStore((state) => state.measureCore);
  const clearCore = useDrawStore((state) => state.clearCore);
  const drawControl = useDrawStore((state) => state.drawControl);
  const measureControl = useDrawStore((state) => state.measureControl);
  const clearControl = useDrawStore((state) => state.clearControl);
  const isReady = useDrawStore((state) => state.isReady);
  const error = useDrawStore((state) => state.error);
  const isDrawing = useDrawStore((state) => state.isDrawing);
  const drawMode = useDrawStore((state) => state.drawMode);
  const isMeasuring = useDrawStore((state) => state.isMeasuring);
  const measureMode = useDrawStore((state) => state.measureMode);

  // ✅ Feature 상태들 추가
  const drawnFeatures = useDrawStore((state) => state.drawnFeatures);
  const measureResults = useDrawStore((state) => state.measureResults);

  // ✅ Store Actions (Provider에서 초기화 완료된 상태)
  const startDrawingAction = useDrawStore((state) => state.startDrawing);
  const stopDrawingAction = useDrawStore((state) => state.stopDrawing);
  const getDebugInfo = useDrawStore((state) => state.getDebugInfo);

  /**
   * 🎯 startDrawingWithCallback - 그리기/측정 시작 + 완료 이벤트 처리
   *
   * 기존 startDrawing을 대체하여 이벤트까지 통합 관리
   * - draw 모드: drawControl + drawend 이벤트
   * - measure 모드: measureControl + measureend 이벤트
   */
  const startDrawingWithCallback = useCallback(
    (mode: DrawingMode = "point"): DrawEndResult => {
      let callbackFn: DrawEndCallback | null = null;

      // 측정 모드인지 확인
      const isMeasureMode =
        typeof mode === "string" && mode.startsWith("measure-");

      if (isMeasureMode) {
        // 측정 모드 처리
        if (!measureControl) {
          console.warn(
            "⚠️ 측정 기능을 사용할 수 없습니다. MeasureControl이 초기화되지 않음",
          );
          return {
            drawend() {
              console.warn("측정 기능이 비활성화된 상태입니다.");
            },
          };
        }

        // 측정 시작 (Store Action 사용)
        startDrawingAction(mode);

        // 고유한 이벤트 ID 생성
        const eventId = `measureend_${Date.now()}_${Math.random()}`;

        // drawend 이벤트 등록 (측정도 drawend 이벤트 사용)
        registerListener(
          measureControl,
          "drawend", // 측정도 drawend 이벤트 사용
          (feature: any) => {
            // 측정 상태 업데이트
            stopDrawingAction();

            if (callbackFn) {
              callbackFn(feature, eventId);
            }
          },
          { once: true, listenerId: eventId },
        );
      } else {
        // 그리기 모드 처리
        if (!drawControl) {
          console.warn(
            "⚠️ 그리기 기능을 사용할 수 없습니다. DrawControl이 초기화되지 않음",
          );
          return {
            drawend() {
              console.warn("그리기 기능이 비활성화된 상태입니다.");
            },
          };
        }

        // 그리기 시작 (Store Action 사용)
        startDrawingAction(mode as DrawingMode);

        // 고유한 이벤트 ID 생성
        const eventId = `drawend_${Date.now()}_${Math.random()}`;

        // drawend 이벤트 등록
        registerListener(
          drawControl,
          "drawend",
          (feature: any) => {
            // 그리기 상태 업데이트
            stopDrawingAction();

            if (callbackFn) {
              callbackFn(feature, eventId);
            }
          },
          { once: true, listenerId: eventId },
        );
      }

      return {
        drawend(fn: DrawEndCallback) {
          callbackFn = fn;
        },
      };
    },
    [
      drawControl,
      measureControl,
      startDrawingAction,
      stopDrawingAction,
      registerListener,
    ],
  );

  /**
   * Core별 상태 확인 유틸리티 (DrawProvider 설정 여부 포함)
   */
  const coreStatus = {
    // Provider 설정 상태
    isProviderSet,
    providerMessage: isProviderSet
      ? "DrawProvider가 설정되어 있습니다."
      : "⚠️ DrawProvider가 설정되지 않았습니다. 그리기 기능을 사용하려면 <DrawProvider>를 추가하세요.",

    // Core별 상태
    draw: {
      available: !!drawCore,
      ready: !!drawCore && !!drawControl,
    },
    measure: {
      available: !!measureCore,
      ready: !!measureCore && !!measureControl,
    },
    clear: {
      available: !!clearCore,
      ready: !!clearCore && !!clearControl,
    },
    // 종합 상태
    isReady: isProviderSet ? isReady : false,
    canDraw: isProviderSet && isReady && !!drawCore,
    canMeasure: isProviderSet && isReady && !!measureCore,
    statusSummary: isProviderSet
      ? isReady
        ? "✅ 그리기 기능 사용 가능"
        : "⏳ 초기화 중..."
      : "❌ DrawProvider 설정 필요",
  };

  return {
    // 🎯 4개 Core 상태 (단일책임 분리)
    drawCore,
    measureCore,
    clearCore,
    drawControl,
    measureControl,
    clearControl,
    isReady,
    error,

    // 그리기 상태
    isDrawing,
    drawMode,

    // 측정 상태
    isMeasuring,
    measureMode,

    // ✅ deprecated: Feature
    drawnFeatures,
    measureResults,

    // Store Actions
    startDrawing: startDrawingWithCallback,
    stopDrawing: stopDrawingAction,

    // Core 상태 확인
    coreStatus,

    // 개발자 도구
    getDebugInfo,

    // 설정
    options: {
      enableSelection,
      enableContextMenu,
      multiSelect,
    },
  };
}
