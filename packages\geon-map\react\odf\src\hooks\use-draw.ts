import { useCallback, useRef } from "react";

import { useDrawProviderStatus } from "../providers/draw-provider";
import { useDrawStore } from "../stores/draw-store";
import { useEvent } from "./use-event";

/**
 * 🎯 새로운 useDraw 훅 (Zustand Store 기반)
 *
 * DrawProvider(설정) + DrawStore(상태/인스턴스) + Hook(상호작용)을 조합하여
 * 완전한 그리기 기능을 제공합니다.
 *
 * @example
 * ```tsx
 * <DrawProvider drawOptions={{ tools: ['polygon'] }}>
 *   <DrawingComponent />
 * </DrawProvider>
 *
 * function DrawingComponent() {
 *   const {
 *     startDrawing,
 *     stopDrawing,
 *     selectedFeature,
 *     contextMenuInfo,
 *     deleteSelectedFeature
 *   } = useDrawNew({
 *     enableSelection: true,
 *     enableContextMenu: true
 *   });
 *
 *   return (
 *     <div>
 *       <button onClick={() => startDrawing('polygon')}>
 *         Draw Polygon
 *       </button>
 *       {selectedFeature && (
 *         <div>Selected: {selectedFeature.type}</div>
 *       )}
 *     </div>
 *   );
 * }
 * ```
 */

export interface UseDrawNewOptions {
  /** 피처 선택 기능 활성화 */
  enableSelection?: boolean;
  /** 컨텍스트 메뉴 기능 활성화 */
  enableContextMenu?: boolean;
  /** 다중 선택 허용 */
  multiSelect?: boolean;
  /** 피처 삭제 시 콜백 */
  onFeatureDelete?: (feature: any) => void;
  /** 피처 편집 시 콜백 */
  onFeatureEdit?: (feature: any) => void;
}

// 그리기 모드 타입 (기존 DrawingMode와 동일)
export type DrawingMode =
  | "point"
  | "lineString"
  | "polygon"
  | "circle"
  | "box"
  | "text"
  | "curve";

// drawEnd 콜백 타입
export type DrawEndCallback = (feature: any, eventId: string) => void;

// drawEnd 반환 타입
export interface DrawEndResult {
  completed: (callback: DrawEndCallback) => void;
}

export function useDraw(options: UseDrawNewOptions = {}) {
  const {
    enableSelection = true,
    enableContextMenu = true,
    multiSelect = false,
  } = options;

  // useEvent 훅 사용 (이벤트 관리)
  const { registerListener } = useEvent();

  // drawEnd 콜백 참조 저장
  const drawEndCallbackRef = useRef<DrawEndCallback | null>(null);

  // DrawProvider 설정 여부 확인
  const { isProviderSet } = useDrawProviderStatus();

  // ✅ Draw Store에서 상태 및 액션 가져오기 (4개 Core 통합)
  const drawCore = useDrawStore((state) => state.drawCore);
  const measureCore = useDrawStore((state) => state.measureCore);
  const clearCore = useDrawStore((state) => state.clearCore);
  const drawControl = useDrawStore((state) => state.drawControl);
  const measureControl = useDrawStore((state) => state.measureControl);
  const clearControl = useDrawStore((state) => state.clearControl);
  const isReady = useDrawStore((state) => state.isReady);
  const error = useDrawStore((state) => state.error);
  const isDrawing = useDrawStore((state) => state.isDrawing);
  const drawMode = useDrawStore((state) => state.drawMode);
  const isMeasuring = useDrawStore((state) => state.isMeasuring);
  const measureMode = useDrawStore((state) => state.measureMode);

  // ✅ Feature 상태들 추가
  const drawnFeatures = useDrawStore((state) => state.drawnFeatures);
  const measureResults = useDrawStore((state) => state.measureResults);

  // ✅ Store Actions (Provider에서 초기화 완료된 상태)
  const startDrawingAction = useDrawStore((state) => state.startDrawing);
  const stopDrawingAction = useDrawStore((state) => state.stopDrawing);
  const getDebugInfo = useDrawStore((state) => state.getDebugInfo);

  /**
   * 🎯 drawEnd - 그리기 완료 이벤트 처리
   *
   * 기존 core의 drawEnd 로직을 useDraw 훅으로 이동
   * draw와 event의 결합도를 useDraw 내부로 캡슐화
   */
  const drawEnd = useCallback((mode: DrawingMode = "point"): DrawEndResult => {
    // 콜백 타입을 정확히 정의
    let callbackFn: DrawEndCallback | null = null;

    // 그리기 시작
    startDrawingAction(mode);

    // 고유한 이벤트 ID 생성
    const eventId = `drawend_${Date.now()}_${Math.random()}`;

    // drawend 이벤트 등록
    const cleanup = registerListener(
      drawControl, // target: drawControl 사용
      "drawend",
      (feature: any) => {
        if (callbackFn) {
          callbackFn(feature, eventId);
        }
      },
      { once: false, listenerId: eventId } // 일회성 이벤트
    );

    return {
      completed(fn: DrawEndCallback) {
        callbackFn = fn;
      },
    };
  }, [drawControl, startDrawingAction, registerListener]);

  /**
   * 선택된 피처 삭제
   */
  // const deleteSelectedFeature = useCallback(() => {
  //   if (!interaction.selectedFeature) return;

  //   const success = deleteFeatureAction(interaction.selectedFeature.feature);
  //   if (success) {
  //     onFeatureDelete?.(interaction.selectedFeature);
  //     interaction.clearSelection();
  //   }
  // }, [
  //   interaction.selectedFeature,
  //   interaction.clearSelection,
  //   onFeatureDelete,
  //   deleteFeatureAction,
  // ]);

  /**
   * 모든 피처 삭제 (Store Action 사용)
   */
  // const clearAllFeatures = useCallback(() => {
  //   clearAllFeaturesAction();
  //   interaction.clearSelection();
  // }, [clearAllFeaturesAction, interaction]);

  /**
   * Core별 상태 확인 유틸리티 (DrawProvider 설정 여부 포함)
   */
  const coreStatus = {
    // Provider 설정 상태
    isProviderSet,
    providerMessage: isProviderSet
      ? "DrawProvider가 설정되어 있습니다."
      : "⚠️ DrawProvider가 설정되지 않았습니다. 그리기 기능을 사용하려면 <DrawProvider>를 추가하세요.",

    // Core별 상태
    draw: {
      available: !!drawCore,
      ready: !!drawCore && !!drawControl,
    },
    measure: {
      available: !!measureCore,
      ready: !!measureCore && !!measureControl,
    },
    clear: {
      available: !!clearCore,
      ready: !!clearCore && !!clearControl,
    },
    // 종합 상태
    isReady: isProviderSet ? isReady : false,
    canDraw: isProviderSet && isReady && !!drawCore,
    canMeasure: isProviderSet && isReady && !!measureCore,
    statusSummary: isProviderSet
      ? isReady
        ? "✅ 그리기 기능 사용 가능"
        : "⏳ 초기화 중..."
      : "❌ DrawProvider 설정 필요",
  };

  return {
    // 🎯 4개 Core 상태 (단일책임 분리)
    drawCore,
    measureCore,
    clearCore,
    drawControl,
    measureControl,
    clearControl,
    isReady,
    error,

    // 그리기 상태
    isDrawing,
    drawMode,

    // 측정 상태
    isMeasuring,
    measureMode,

    // ✅ deprecated: Feature
    drawnFeatures,
    measureResults,

    // Store Actions
    startDrawing: startDrawingAction,
    stopDrawing: stopDrawingAction,

    // 🎯 이벤트 통합 메서드
    drawEnd, // 그리기 완료 이벤트 처리

    // Core 상태 확인
    coreStatus,

    // 개발자 도구
    getDebugInfo,

    // 설정
    options: {
      enableSelection,
      enableContextMenu,
      multiSelect,
    },
  };
}
