/**
 * 🎯 Feature 조작 훅
 *
 * feature-actions 유틸과 DrawStore를 조합하여
 * 앱에서 바로 사용할 수 있는 편의 훅을 제공합니다.
 *
 * 핵심 특징:
 * 1. Core 메서드 + Store 동기화를 한 번에 처리
 * 2. 명확한 에러 처리와 로깅
 * 3. TypeScript 친화적인 인터페이스
 */

import { useCallback } from "react";

import { useDrawStore } from "../stores/draw-store";
import {
  changeFeatureStyle,
  deleteFeature,
  extractFeatureId,
  validateStyleOptions,
} from "../utils/feature-actions";
import { useLayer } from "./use-layer";

/**
 * Feature 조작 훅의 반환 타입
 */
export interface UseFeatureActionsReturn {
  /** ✅ 통합된 피처 삭제 (레이어 타입 자동 판별) */
  deleteFeature: (
    feature: any,
    layerType?: "draw" | "measure" | "clear",
  ) => boolean;
  /** 피처 스타일 변경 */
  changeFeatureStyle: (feature: any, styleOptions: any) => boolean;
  /** 피처 ID 추출 */
  getFeatureId: (feature: any, prefix?: string) => string;
  /** 스타일 옵션 검증 */
  isValidStyle: (styleOptions: any) => boolean;
  /** 사용 가능 여부 */
  isReady: boolean;
}

/**
 * Feature 조작 훅
 *
 * @returns Feature 조작 관련 함수들
 *
 * @example
 * ```typescript
 * function ContextMenu({ selectedFeature }) {
 *   const { deleteDrawFeature, changeFeatureStyle, isReady } = useFeatureActions();
 *
 *   const handleDelete = () => {
 *     if (selectedFeature) {
 *       const success = deleteDrawFeature(selectedFeature);
 *       if (success) {
 *         console.log('피처 삭제 완료');
 *       }
 *     }
 *   };
 *
 *   const handleStyleChange = (color: string) => {
 *     if (selectedFeature) {
 *       const styleOptions = {
 *         fill: { color: [255, 0, 0, 1] },
 *         stroke: { color: [255, 0, 0, 1], width: 2 }
 *       };
 *       changeFeatureStyle(selectedFeature, styleOptions);
 *     }
 *   };
 *
 *   if (!isReady) return <div>Loading...</div>;
 *
 *   return (
 *     <div>
 *       <button onClick={handleDelete}>Delete</button>
 *       <button onClick={() => handleStyleChange('red')}>Red Style</button>
 *     </div>
 *   );
 * }
 * ```
 */
export function useFeatureActions(): UseFeatureActionsReturn {
  // ✅ 새로운 아키텍처: LayerStore에서 레이어 가져오기
  const { drawLayer, measureLayer, clearLayer } = useLayer();
  const removeDrawnFeature = useDrawStore((state) => state.removeDrawnFeature);
  const removeMeasureResult = useDrawStore(
    (state) => state.removeMeasureResult,
  );

  // 사용 가능 여부 (하나라도 레이어가 있으면 사용 가능)
  const isReady = !!(drawLayer || measureLayer || clearLayer);

  /**
   * ✅ 통합된 피처 삭제 (레이어 타입 자동 판별)
   */
  const deleteFeatureAction = useCallback(
    (feature: any, layerType?: "draw" | "measure" | "clear"): boolean => {
      if (!isReady) {
        console.warn("useFeatureActions: 레이어가 준비되지 않았습니다.");
        return false;
      }

      // 레이어 타입이 명시되지 않은 경우 자동 판별 로직 필요
      // 현재는 drawLayer 우선으로 처리
      let targetLayer = null;
      let targetType: "draw" | "measure" | "clear" | "other" = "other";

      if (layerType) {
        // 명시적 타입이 있는 경우
        if (layerType === "draw" && drawLayer) {
          targetLayer = drawLayer;
          targetType = "draw";
        } else if (layerType === "measure" && measureLayer) {
          targetLayer = measureLayer;
          targetType = "measure";
        } else if (layerType === "clear" && clearLayer) {
          targetLayer = clearLayer;
          targetType = "clear";
        }
      } else {
        // 자동 판별: draw > measure > clear 순서
        if (drawLayer) {
          targetLayer = drawLayer;
          targetType = "draw";
        } else if (measureLayer) {
          targetLayer = measureLayer;
          targetType = "measure";
        } else if (clearLayer) {
          targetLayer = clearLayer;
          targetType = "clear";
        }
      }

      if (!targetLayer) {
        console.warn("useFeatureActions: 적절한 레이어를 찾을 수 없습니다.");
        return false;
      }

      return deleteFeature(targetLayer, feature, (featureId, detectedType) => {
        // Store 업데이트 (감지된 타입 또는 명시된 타입 사용)
        const finalType = detectedType !== "other" ? detectedType : targetType;

        if (finalType === "draw") {
          removeDrawnFeature(featureId);
          console.log(`그리기 피처 삭제 완료: ${featureId}`);
        } else if (finalType === "measure") {
          removeMeasureResult(featureId);
          console.log(`측정 피처 삭제 완료: ${featureId}`);
        }
      });
    },
    [
      isReady,
      drawLayer,
      measureLayer,
      clearLayer,
      removeDrawnFeature,
      removeMeasureResult,
    ],
  );

  /**
   * 피처 스타일 변경
   */
  const changeFeatureStyleAction = useCallback(
    (feature: any, styleOptions: any): boolean => {
      if (!isReady) {
        console.warn("useFeatureActions: 레이어가 준비되지 않았습니다.");
        return false;
      }

      if (!validateStyleOptions(styleOptions)) {
        console.warn(
          "useFeatureActions: 유효하지 않은 스타일 옵션입니다.",
          styleOptions,
        );
        return false;
      }

      return changeFeatureStyle(
        feature,
        styleOptions,
        (featureId: string, newStyle: any) => {
          // 필요시 Store에 스타일 메타데이터 저장
          // 현재는 로깅만 수행
          console.log(`피처 스타일 변경 완료: ${featureId}`, newStyle);
        },
      );
    },
    [isReady],
  );

  /**
   * 피처 ID 추출 헬퍼
   */
  const getFeatureId = useCallback((feature: any, prefix?: string): string => {
    return extractFeatureId(feature, prefix);
  }, []);

  /**
   * 스타일 옵션 검증 헬퍼
   */
  const isValidStyle = useCallback((styleOptions: any): boolean => {
    return validateStyleOptions(styleOptions);
  }, []);

  return {
    deleteFeature: deleteFeatureAction,
    changeFeatureStyle: changeFeatureStyleAction,
    getFeatureId,
    isValidStyle,
    isReady,
  };
}

/**
 * 스타일 프리셋 생성 헬퍼
 *
 * @param color - 색상 이름 또는 RGBA 배열
 * @returns 스타일 옵션 객체
 */
export function createStylePreset(
  color: string | [number, number, number, number],
): any {
  const colorMap = {
    red: [255, 0, 0, 1] as [number, number, number, number],
    blue: [0, 0, 255, 1] as [number, number, number, number],
    green: [0, 255, 0, 1] as [number, number, number, number],
    yellow: [255, 255, 0, 1] as [number, number, number, number],
    purple: [128, 0, 128, 1] as [number, number, number, number],
    orange: [255, 165, 0, 1] as [number, number, number, number],
    black: [0, 0, 0, 1] as [number, number, number, number],
    white: [255, 255, 255, 1] as [number, number, number, number],
  };

  const fillColor: [number, number, number, number] =
    typeof color === "string"
      ? colorMap[color as keyof typeof colorMap] || colorMap.red
      : color;
  const strokeColor = [...fillColor.slice(0, 3), 0.8] as [
    number,
    number,
    number,
    number,
  ];

  return {
    fill: { color: fillColor },
    stroke: { color: strokeColor, width: 2 },
  };
}

/**
 * 기본 스타일 프리셋들
 */
export const STYLE_PRESETS = {
  red: createStylePreset("red"),
  blue: createStylePreset("blue"),
  green: createStylePreset("green"),
  yellow: createStylePreset("yellow"),
  purple: createStylePreset("purple"),
  orange: createStylePreset("orange"),
} as const;
