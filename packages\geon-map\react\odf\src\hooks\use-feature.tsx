import { FeatureFactory } from "@geon-map/core";
import { useCallback } from "react";

/**
 * 피쳐 생성을 위한 범용 React 훅
 */
export function useFeature() {
  /**
   * WKT(Well-Known Text) 문자열에서 피쳐 생성
   *
   * 표준 WKT 형식을 지원하며, 선택적으로 속성(properties)을 추가할 수 있습니다.
   * 데이터베이스나 다른 GIS 시스템에서 가져온 WKT 데이터를 변환할 때 사용합니다.
   *
   * @param wkt - WKT 문자열 (예: "POINT(126.9 37.5)")
   * @param properties - 피쳐 속성 객체 (선택사항)
   * @returns 생성된 피쳐 객체
   *
   * @example
   * ```typescript
   * const feature = useFeature();
   *
   * // 포인트 WKT
   * const point = feature.fromWKT(
   *   "POINT(126.9780 37.5665)",
   *   { name: "서울시청", category: "government" }
   * );
   *
   * // 폴리곤 WKT
   * const polygon = feature.fromWKT(
   *   "POLYGON((126.9 37.5, 127.0 37.5, 127.0 37.6, 126.9 37.6, 126.9 37.5))",
   *   { area: "강남구", population: 561052 }
   * );
   *
   * // 라인스트링 WKT
   * const line = feature.fromWKT(
   *   "LINESTRING(126.9780 37.5665, 127.0276 37.4979)",
   *   { route: "서울-강남" }
   * );
   * ```
   */
  const fromWKT = useCallback(
    (wkt: string, properties?: Record<string, any>) => {
      return FeatureFactory.fromWKT(wkt, properties);
    },
    [],
  );

  /**
   * 좌표로 간편한 포인트 생성
   *
   * 옵션 객체 없이 좌표만으로 포인트를 빠르게 생성합니다.
   * 간단한 마커나 위치 표시가 필요할 때 사용합니다.
   *
   * @param coordinates - [경도, 위도] 배열
   * @param properties - 피쳐 속성 (선택사항)
   * @returns 포인트 피쳐 객체
   *
   * @example
   * ```typescript
   * const feature = useFeature();
   *
   * // 간단한 포인트 생성
   * const point = feature.createPointFromCoordinates([126.9780, 37.5665]);
   *
   * // 속성을 포함한 포인트 생성
   * const pointWithProps = feature.createPointFromCoordinates(
   *   [126.9780, 37.5665],
   *   { name: "서울시청", type: "government" }
   * );
   *
   * // 배열 반복으로 여러 포인트 생성
   * const locations = [
   *   [126.9780, 37.5665],
   *   [127.0270, 37.4979],
   *   [126.9120, 37.5510]
   * ];
   *
   * const points = locations.map(coord =>
   *   feature.createPointFromCoordinates(coord, { id: Math.random() })
   * );
   * ```
   */
  const createPointFromCoordinates = useCallback(
    (coordinates: [number, number], properties?: Record<string, any>) => {
      return FeatureFactory.createPoint({ coordinates, properties });
    },
    [],
  );

  return {
    // 핵심 생성 메서드 - 가장 자주 사용되는 기본 기능들
    fromWKT,

    // 편의 헬퍼 메서드 - 간편한 사용을 위한 래퍼 함수들
    createPointFromCoordinates,
  };
}
