/**
 * 🎯 통합 Layer 관리 훅
 *
 * LayerStore와 연동하여 모든 레이어 타입에 대한 통합된 접근 인터페이스를 제공합니다.
 *
 * 핵심 특징:
 * 1. 기존 Layer 인터페이스 패턴 준수 (metadata + odfLayer)
 * 2. Draw/Measure/Clear 레이어 전용 관리
 * 3. 일반 레이어 관리 (Geoserver, GeoJSON, KML 등)
 * 4. 타입 안전한 레이어 접근
 * 5. 상태 복원 가능한 메타데이터 관리
 */

"use client";

import { MapProjection } from "@geon-map/core";
import { useCallback } from "react";

import { useLayerStore } from "../stores/layer-store";
import type {
  AddFeatureOptions,
  APILayerProps,
  CSVLayerProps,
  GeoJSONLayerProps,
  GeoserverLayerProps,
  GeoTiffLayerProps,
  KMLLayerProps,
  Layer,
  LayerProps,
  LayerType,
  SVGLayerProps,
} from "../types/layer-types";
import { useMapInstanceRequired } from "./use-map-instance";

/**
 * Layer 관리 훅의 반환 타입
 */
export interface UseLayerReturn {
  /** 모든 레이어 목록 */
  layers: Layer[];

  /** Draw 레이어 (단일) */
  drawLayer: Layer | null;

  /** Measure 레이어 (단일) */
  measureLayer: Layer | null;

  /** Clear 레이어 (단일) */
  clearLayer: Layer | null;

  /** ID로 레이어 찾기 */
  getLayerById: (layerId: string) => Layer | null;

  /** 타입으로 레이어 찾기 */
  getLayerByType: (type: "draw" | "measure" | "clear") => Layer | null;

  /** 레이어 ID로 타입 확인 */
  getLayerTypeById: (layerId: string) => "draw" | "measure" | "clear" | "other";

  /** Draw 레이어 추가/업데이트 */
  setDrawLayer: (
    drawLayer: any,
    options?: { name?: string; visible?: boolean },
  ) => void;

  /** Measure 레이어 추가/업데이트 */
  setMeasureLayer: (
    measureLayer: any,
    options?: { name?: string; visible?: boolean },
  ) => void;

  /** Clear 레이어 추가/업데이트 */
  setClearLayer: (
    clearLayer: any,
    options?: { name?: string; visible?: boolean },
  ) => void;

  /** 레이어 가시성 토글 */
  toggleLayerVisibility: (layerId: string) => void;

  /** 레이어 제거 */
  removeLayer: (layerId: string) => void;

  // === 일반 레이어 관리 ===
  /** 일반 레이어 추가 (Geoserver, GeoJSON 등) */
  addLayer: (props: LayerProps) => Promise<string | void>;

  /** 레이어 스타일 업데이트 */
  updateStyle: (layerId: string, style: any) => void;

  /** 레이어 가시성 설정 */
  setVisible: (layerId: string, visible: boolean) => void;

  /** 레이어 Z-Index 설정 */
  setZIndex: (layerId: string, zIndex: number) => void;

  /** 레이어 투명도 설정 */
  setOpacity: (layerId: string, opacity: number) => void;

  /** 레이어에 맞춰 지도 확대/축소 */
  fitToLayer: (layerId: string, duration?: number) => boolean;

  /** 레이어를 최상위로 이동 */
  setMaxZIndex: (layerId: string) => void;

  /** 레이어 스타일 업데이트 (확장) */
  updateLayerStyle: (layerId: string, style: any) => boolean;

  /** 레이어에 피처 추가 */
  addFeature: (
    layerId: string,
    feature: any,
    options?: AddFeatureOptions,
  ) => boolean;

  /** 레이어의 모든 피처 제거 */
  clearFeatures: (layerId: string) => boolean;

  // === 레이어 선택 관리 ===
  /** 선택된 레이어 ID */
  selectedLayerId?: string;

  /** 레이어 선택 */
  selectLayer: (layerId: string) => void;

  /** 확장된 그룹 목록 */
  expandedGroups: Set<string>;

  /** 그룹 토글 */
  toggleGroup: (groupId: string) => void;
  /** 레이어 찾기 */
  findLayer: (layerId: string) => Layer | undefined;
}

/**
 * LayerProps를 ODF 레이어 파라미터로 변환하는 헬퍼 함수
 */
function convertToLayerParams(props: LayerProps): {
  type: LayerType;
  params: any;
} {
  const {
    type,
    renderOptions,

    ...rest
  } = props;

  switch (type) {
    case "empty": {
      return {
        type,
        params: {
          renderOptions,
        },
      };
    }
    case "geoserver": {
      const geoserverProps = rest as GeoserverLayerProps;
      return {
        type,
        params: {
          server:
            typeof geoserverProps.server === "string"
              ? geoserverProps.server
              : geoserverProps.server.url,
          layer: geoserverProps.layer,
          service: geoserverProps.service,
          method: geoserverProps.method || "get",
          bbox: geoserverProps.bbox || false,
          crtfckey: geoserverProps.crtfckey || "",
          projection: geoserverProps.projection || "EPSG:5186",
          limit: geoserverProps.limit,
          tiled: geoserverProps.tiled,
          geometryType: geoserverProps.geometryType,
          serviceTy: geoserverProps.serviceTy,
          ...(typeof geoserverProps.server !== "string" && {
            version: geoserverProps.server.version,
            proxyURL: geoserverProps.server.proxyURL,
            proxyParam: geoserverProps.server.proxyParam,
          }),
        },
      };
    }
    case "geotiff": {
      const geotiffProps = rest as GeoTiffLayerProps;
      return {
        type,
        params: {
          sources: geotiffProps.sources,
          normalize: geotiffProps.normalize ?? true,
          wrapX: geotiffProps.wrapX,
          opaque: geotiffProps.opaque,
          transition: geotiffProps.transition,
          renderOptions,
        },
      };
    }
    case "geojson": {
      const geojsonProps = rest as GeoJSONLayerProps;
      return {
        type,
        params: {
          data: geojsonProps.data,
          dataProjectionCode: geojsonProps.dataProjectionCode,
          featureProjectionCode: geojsonProps.featureProjectionCode,
          service: geojsonProps.service,
          renderOptions,
        },
      };
    }
    case "kml": {
      const kmlProps = rest as KMLLayerProps;
      return {
        type,
        params: {
          data: kmlProps.data,
          dataProjectionCode: kmlProps.dataProjectionCode,
          featureProjectionCode: kmlProps.featureProjectionCode,
          renderOptions,
        },
      };
    }
    case "csv": {
      const csvProps = rest as CSVLayerProps;
      return {
        type,
        params: {
          data: csvProps.data,
          dataProjectionCode: csvProps.dataProjectionCode,
          featureProjectionCode: csvProps.featureProjectionCode,
          geometryColumnName: csvProps.geometryColumnName,
          delimiter: csvProps.delimiter,
          renderOptions,
        },
      };
    }
    case "api": {
      const apiProps = rest as APILayerProps;
      return {
        type,
        params: {
          server:
            typeof apiProps.server === "string"
              ? apiProps.server
              : apiProps.server.url,
          service: apiProps.service,
          bbox: apiProps.bbox,
          tiled: apiProps.tiled,
          tileGrid: apiProps.tileGrid,
          originalOption: apiProps.originalOption,
          parameterFilter: apiProps.parameterFilter,
          ...(typeof apiProps.server !== "string" && {
            proxyURL: apiProps.server.proxyURL,
            proxyParam: apiProps.server.proxyParam,
          }),
          renderOptions,
        },
      };
    }
    case "svg": {
      const svgProps = rest as SVGLayerProps;
      return {
        type,
        params: {
          svgContainer: svgProps.svgContainer,
          extent: svgProps.extent,
          renderOptions,
        },
      };
    }
    default:
      throw new Error(`Unsupported layer type: ${type}`);
  }
}

/**
 * 통합 Layer 관리 훅
 *
 * @returns Layer 관리 관련 상태와 함수들
 *
 * @example
 * ```typescript
 * function FeatureManager() {
 *   const {
 *     drawLayer,
 *     measureLayer,
 *     getLayerTypeById,
 *     setDrawLayer,
 *     addLayer
 *   } = useLayer();
 *
 *   // Draw 레이어 설정
 *   useEffect(() => {
 *     if (drawCore) {
 *       const layer = drawCore.getDrawLayer();
 *       setDrawLayer(layer, { name: 'My Draw Layer' });
 *     }
 *   }, [drawCore, setDrawLayer]);
 *
 *   // 일반 레이어 추가
 *   const handleAddGeoserver = async () => {
 *     await addLayer({
 *       type: 'geoserver',
 *       server: 'http://example.com/geoserver',
 *       layer: 'workspace:layer',
 *       service: 'wms'
 *     });
 *   };
 *
 *   return <div>Layer management UI</div>;
 * }
 * ```
 */
export function useLayer(): UseLayerReturn {
  // Map과 ODF 인스턴스 가져오기
  const { map, odf } = useMapInstanceRequired();

  // LayerStore에서 상태와 액션 가져오기
  const layers = useLayerStore((state) => state.layers);
  const selectedLayerId = useLayerStore((state) => state.selectedLayerId);
  const expandedGroups = useLayerStore((state) => state.expandedGroups);
  const getLayerByType = useLayerStore((state) => state.getLayerByType);
  const getLayerById = useLayerStore((state) => state.getLayerById);
  const getLayerTypeById = useLayerStore((state) => state.getLayerTypeById);
  const addDrawLayer = useLayerStore((state) => state.addDrawLayer);
  const addMeasureLayer = useLayerStore((state) => state.addMeasureLayer);
  const addClearLayer = useLayerStore((state) => state.addClearLayer);
  const toggleLayerVisibility = useLayerStore(
    (state) => state.toggleLayerVisibility,
  );
  const removeLayerFromStore = useLayerStore((state) => state.removeLayer);
  const addLayerToStore = useLayerStore((state) => state.addLayer);
  const updateLayerInStore = useLayerStore((state) => state.updateLayer);
  const setSelectedLayer = useLayerStore((state) => state.setSelectedLayer);
  const toggleGroup = useLayerStore((state) => state.toggleGroup);

  // 개별 레이어 상태 (computed)
  const drawLayer = getLayerByType("draw");
  const measureLayer = getLayerByType("measure");
  const clearLayer = getLayerByType("clear");

  // 레이어 설정 헬퍼 함수들
  const setDrawLayer = useCallback(
    (drawLayer: any, options?: { name?: string; visible?: boolean }) => {
      addDrawLayer(drawLayer, options);
    },
    [addDrawLayer],
  );

  const setMeasureLayer = useCallback(
    (measureLayer: any, options?: { name?: string; visible?: boolean }) => {
      addMeasureLayer(measureLayer, options);
    },
    [addMeasureLayer],
  );

  const setClearLayer = useCallback(
    (clearLayer: any, options?: { name?: string; visible?: boolean }) => {
      addClearLayer(clearLayer, options);
    },
    [addClearLayer],
  );

  // === 일반 레이어 관리 함수들 ===
  const addLayer = useCallback(
    async (props: LayerProps) => {
      const layerId = props.type === "geoserver" ? props.layer : props.id;

      // 기존 ODF 레이어들과 비교
      const existingODFLayers = map.getODFLayers();
      const existingLayer = existingODFLayers.find(
        (odfLayer: { getInitialOption: () => any }) => {
          const initialOption = odfLayer.getInitialOption();
          return (
            props.type === "geoserver" &&
            initialOption?.params?.layer ===
              (props as GeoserverLayerProps).layer
          );
        },
      );

      if (existingLayer) {
        return;
      }
      const { type, params } = convertToLayerParams(props);

      const odfLayer = odf.LayerFactory.produce(type, {
        ...params,
      });

      if (props.renderOptions?.style) {
        if (type === "geoserver" && params.service === "wms") {
          const sldStyle = odf.StyleFactory.produceSLD(
            props.renderOptions.style,
          );
          odfLayer.setSLD(sldStyle);
        } else {
          const style = odf.StyleFactory.produce(props.renderOptions.style);
          odfLayer.setStyle(style);
        }
      }

      odfLayer.setMap(map);
      odfLayer.fit();

      console.log(props);

      const newLayer: Layer = {
        id: odfLayer.getODFId(),
        name:
          props.type === "geoserver"
            ? (props as GeoserverLayerProps).name
            : layerId || odfLayer.getODFId(),
        type,
        visible: true,
        zIndex: props.zIndex ?? 0, // layers.length 대신 기본값 0 사용
        odfLayer,
        params,
        info:
          props.type === "geoserver"
            ? (props as GeoserverLayerProps).info
            : {
                lyrId: "",
                lyrNm: "",
              },
      };

      addLayerToStore(newLayer);
      if (odfLayer) return odfLayer.getODFId();
    },
    [map, odf, addLayerToStore], // layers.length 제거
  );

  const removeLayer = useCallback(
    (layerId: string) => {
      const currentLayers = useLayerStore.getState().layers;
      const layer = currentLayers.find((l) => l.id === layerId);
      if (layer?.odfLayer) {
        map?.removeLayer(layer.odfLayer);
        removeLayerFromStore(layerId);
      }
    },
    [map, removeLayerFromStore],
  );

  const updateStyle = useCallback(
    (layerId: string, style: any) => {
      const currentLayers = useLayerStore.getState().layers;
      const layer = currentLayers.find((l) => l.id === layerId);
      if (!layer) return;

      try {
        const parsedStyle =
          typeof style === "string" ? JSON.parse(style) : style;

        if (layer.type === "geoserver" && layer.params.service === "wms") {
          const sldStyle = odf.StyleFactory.produceSLD(parsedStyle);
          layer.odfLayer.setSLD(sldStyle);
        } else {
          const odfStyle = odf.StyleFactory.produce(parsedStyle);
          layer.odfLayer.setStyle(odfStyle);
        }

        updateLayerInStore(layerId, { style: parsedStyle });
      } catch (error) {
        console.error("Failed to update layer style:", error);
      }
    },
    [odf, updateLayerInStore],
  );

  const setVisible = useCallback(
    (layerId: string, visible: boolean) => {
      const currentLayers = useLayerStore.getState().layers;
      const layer = currentLayers.find((l) => l.id === layerId);
      if (layer) {
        layer.odfLayer.setVisible(visible);
        updateLayerInStore(layerId, { visible });
      }
    },
    [updateLayerInStore],
  );

  const setZIndex = useCallback(
    (layerId: string, zIndex: number) => {
      const currentLayers = useLayerStore.getState().layers;
      const layer = currentLayers.find((l) => l.id === layerId);
      if (layer) {
        map?.setZIndex(layer.odfLayer.getODFId(), zIndex);
        updateLayerInStore(layerId, { zIndex });
      }
    },
    [map, updateLayerInStore],
  );

  const setOpacity = useCallback(
    (layerId: string, opacity: number) => {
      const currentLayers = useLayerStore.getState().layers;
      const layer = currentLayers.find((l) => l.id === layerId);
      if (layer?.odfLayer) {
        layer.odfLayer.setOpacity(opacity);
        updateLayerInStore(layerId, { opacity });
      }
    },
    [updateLayerInStore],
  );

  const fitToLayer = useCallback(
    (layerId: string, duration: number = 0): boolean => {
      const currentLayers = useLayerStore.getState().layers;
      const layer = currentLayers.find((l) => l.id === layerId);
      if (layer) {
        layer.odfLayer.fit(duration);
        return true;
      }
      return false;
    },
    [],
  );

  const setMaxZIndex = useCallback(
    (layerId: string) => {
      const currentLayers = useLayerStore.getState().layers;
      const layer = currentLayers.find((l) => l.id === layerId);
      if (layer) {
        map?.setZIndex(layerId, map.getMaxZIndex());
      }
    },
    [map],
  );

  const updateLayerStyle = useCallback(
    (layerId: string, style: any) => {
      const currentLayers = useLayerStore.getState().layers;
      const layer = currentLayers.find((l) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없음 (ID: ${layerId})`);
        return false;
      }

      try {
        const parsedStyle =
          typeof style === "string" ? JSON.parse(style) : style;

        if (layer.type === "geoserver" && layer.params.service === "wms") {
          const sldStyle = odf.StyleFactory.produceSLD(parsedStyle);
          layer.odfLayer.setSLD(sldStyle);
        } else {
          layer.odfLayer.setStyle(parsedStyle);
        }

        updateLayerInStore(layerId, { style: parsedStyle });
        return true;
      } catch (error) {
        console.error(`스타일 업데이트 중 오류 (ID: ${layerId}):`, error);
        return false;
      }
    },
    [odf, updateLayerInStore],
  );

  const addFeature = useCallback(
    (
      layerId: string,
      feature: any,
      options: AddFeatureOptions = {},
    ): boolean => {
      const currentLayers = useLayerStore.getState().layers;
      const layer = currentLayers.find((l) => l.id === layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
        return false;
      }
      let targetFeature = feature;

      const vectorLayerTypes = ["empty", "geojson", "kml", "csv"];
      if (!vectorLayerTypes.includes(layer.type)) {
        console.error(
          `피쳐 추가는 벡터 레이어에서만 가능합니다. 현재 레이어 타입: ${layer.type}`,
        );
        return false;
      }

      try {
        const { srid } = options;
        if (srid) {
          const mapProjection = new MapProjection(map);
          targetFeature = mapProjection.projectGeom(feature, srid);
        }

        layer.odfLayer.addFeature(targetFeature);
        return true;
      } catch (error) {
        console.error(`피쳐 추가 중 오류 발생 (레이어 ID: ${layerId}):`, error);
        return false;
      }
    },
    [map],
  );

  const clearFeatures = useCallback((layerId: string): boolean => {
    const currentLayers = useLayerStore.getState().layers;
    const layer = currentLayers.find((l) => l.id === layerId);
    if (!layer) {
      console.error(`레이어를 찾을 수 없습니다 (ID: ${layerId})`);
      return false;
    }
    try {
      layer.odfLayer.clearFeatures();
      return true;
    } catch (error) {
      console.error(`피쳐 제거 중 오류 발생 (레이어 ID: ${layerId}):`, error);
      return false;
    }
  }, []);

  const findLayer = useCallback(
    (layerId: string) => {
      return layers.find((l) => l.id === layerId);
    },
    [layers],
  );

  return {
    // 상태
    layers,
    drawLayer,
    measureLayer,
    clearLayer,
    selectedLayerId,
    expandedGroups,

    // Draw/Measure/Clear 레이어 설정 함수
    setDrawLayer,
    setMeasureLayer,
    setClearLayer,

    // 일반 레이어 관리 함수
    addLayer,
    updateStyle,
    setVisible,
    setZIndex,
    setOpacity,
    fitToLayer,
    setMaxZIndex,
    updateLayerStyle,
    addFeature,
    clearFeatures,

    // 공통 조회/관리 함수
    getLayerById,
    getLayerByType,
    getLayerTypeById,
    toggleLayerVisibility,
    removeLayer,
    selectLayer: setSelectedLayer,
    toggleGroup,
    findLayer,
  };
}

/**
 * 레이어 ODF ID 추출 헬퍼
 *
 * @param layer - Layer 인터페이스 객체
 * @returns ODF Layer ID 또는 null
 */
export function getODFLayerId(layer: Layer | null): string | null {
  if (!layer?.odfLayer) return null;

  try {
    return layer.odfLayer.getODFId?.() || null;
  } catch (error) {
    console.error("Failed to get ODF Layer ID:", error);
    return null;
  }
}

/**
 * 레이어 타입 판별 헬퍼 (ODF ID 기반)
 *
 * @param odfLayerId - ODF Layer ID
 * @returns 레이어 타입
 */
export function determineLayerTypeByODFId(
  odfLayerId: string,
): "draw" | "measure" | "clear" | "other" {
  if (!odfLayerId) return "other";

  const lowerCaseId = odfLayerId.toLowerCase();

  if (lowerCaseId.includes("draw")) return "draw";
  if (lowerCaseId.includes("measure")) return "measure";
  if (lowerCaseId.includes("clear")) return "clear";

  return "other";
}

/**
 * 레이어 메타데이터 생성 헬퍼
 *
 * @param type - 레이어 타입
 * @param odfLayer - ODF 레이어 인스턴스
 * @param options - 추가 옵션
 * @returns Layer 인터페이스 객체
 */
export function createLayerMetadata(
  type: "draw" | "measure" | "clear",
  odfLayer: any,
  options: {
    id?: string;
    name?: string;
    visible?: boolean;
    zIndex?: number;
    params?: any;
  } = {},
): Layer {
  const {
    id = `${type}_${Date.now()}`,
    name = `${type.charAt(0).toUpperCase() + type.slice(1)} Layer`,
    visible = true,
    zIndex = type === "clear" ? 1002 : type === "measure" ? 1001 : 1000,
    params = {},
  } = options;

  return {
    id,
    name,
    type,
    visible,
    zIndex,
    odfLayer,
    params: {
      [`${type}Options`]: {},
      ...params,
    },
  };
}
