import { BasemapInfo, LayerFactory } from "@geon-map/core";
import { useCallback } from "react";

import { useMapStore } from "../stores/map-store";

declare const odf: any;

/**
 * 🎯 지도 조작 액션들을 제공하는 훅
 *
 * ✅ Store Actions 패턴 사용
 * Hook → Store Actions → Core 인스턴스 조작
 * 깔끔하고 일관된 API 제공
 */
export function useMapActions() {
  // ✅ Store Actions 가져오기 (Core 인스턴스 직접 접근 X)
  const setCenter = useMapStore((state) => state.setCenter);
  const setZoom = useMapStore((state) => state.setZoom);
  const panTo = useMapStore((state) => state.panTo);
  const moveToCurrentLocation = useMapStore(
    (state) => state.moveToCurrentLocation,
  );

  const map = useMapStore((state) => state.map);

  // Store Actions에서 베이스맵 액션들 가져오기
  const switchBasemap = useMapStore((state) => state.switchBasemap);
  const getCurrentBasemap = useMapStore((state) => state.getCurrentBasemap);
  const getAvailableBasemaps = useMapStore((state) => state.getAvailableBasemaps);

  // 베이스맵 변경 (Store Actions 사용)
  const setBasemap = useCallback(
    (basemapId: string) => {
      switchBasemap(basemapId);
    },
    [switchBasemap],
  );

  // 베이스맵 정보 설정
  const setBasemapInfo = useCallback(
    (basemapInfo: BasemapInfo) => {
      if (!map) return;
      const layerFactory = new LayerFactory(map, odf);
      layerFactory.setBasemapInfo(basemapInfo);
    },
    [map],
  );

  // 좌표 변환 유틸리티 (Store Actions에 없는 유틸리티 함수)
  const mapInstance = useMapStore((state) => state.mapInstance);
  const transformCoordinate = useCallback(
    (coords: [number, number], sourceProjection: string): [number, number] => {
      if (!mapInstance) {
        console.warn("MapController가 초기화되지 않았습니다.");
        return coords;
      }

      try {
        return mapInstance.transformCoordinate(coords, sourceProjection);
      } catch (error) {
        console.error("좌표 변환 실패:", error);
        return coords;
      }
    },
    [mapInstance],
  );

  return {
    // Store Actions (권장 사용법)
    setCenter,
    setZoom,
    panTo,
    moveToCurrentLocation,

    // 베이스맵 관련 (Store Actions 기반)
    setBasemap,
    getCurrentBasemap,
    getAvailableBasemaps,
    setBasemapInfo,

    // 유틸리티 함수
    transformCoordinate,

    // 고급 사용자용 (직접 접근)
    mapInstance,
  };
}
