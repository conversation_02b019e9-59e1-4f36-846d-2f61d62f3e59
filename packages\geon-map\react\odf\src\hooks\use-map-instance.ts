import { useMapStore } from "../stores/map-store";

/**
 * 지도 인스턴스에 안전하게 접근할 수 있는 훅
 * 초기화되지 않은 경우 에러를 던지는 대신 null을 반환하여 더 안전한 패턴 제공
 */
export function useMapInstance() {
  const map = useMapStore((state) => state.map);
  const odf = useMapStore((state) => state.odf);
  const isLoading = useMapStore((state) => state.isLoading);

  return {
    map,
    odf,
    isLoading,
    isReady: !!(map && odf && !isLoading),
  };
}

/**
 * 지도 인스턴스가 준비될 때까지 기다리는 훅
 * 초기화되지 않은 경우 에러를 던짐 (기존 패턴 유지)
 */
export function useMapInstanceRequired() {
  const { map, odf, isLoading, isReady } = useMapInstance();

  if (isLoading) {
    throw new Error("Map is still initializing. Please wait.");
  }

  if (!isReady || !map || !odf) {
    throw new Error(
      "Map instance has not been initialized. Make sure you're using this hook inside a Map component.",
    );
  }

  return { map, odf };
}
