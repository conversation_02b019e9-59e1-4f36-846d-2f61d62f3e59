// use-scale.ts
import { useMapStore } from "@geon-map/react-odf";
import { useEffect, useState } from "react";

/**
 * 축척 값을 반환하는 커스텀 훅
 */
export const useScale = () => {
  const map = useMapStore((s) => s.map);
  const scaleInstance = useMapStore((s) => s.scaleInstance);
  const eventInstance = useMapStore((s) => s.eventInstance);
  const [currentScale, setCurrentScale] = useState<string>("");

  useEffect(() => {
    if (map) {
      const initialScale = map.getView().getResolution() * 100;
      const scale = initialScale / 1000;
      const unit = "km";
      const scaleText = `${scale.toFixed(2)}${unit}`;
      setCurrentScale(scaleText);
    }
  }, [map]);

  useEffect(() => {
    if (!scaleInstance || !eventInstance || !map) return;

    scaleInstance.setMap();

    const eventId = eventInstance.addListener(
      map.getView(),
      "change:resolution",
      () => {
        setCurrentScale(scaleInstance.getScaleValue());
      },
    );
    return () => {
      if (eventId) eventInstance.removeListener(eventId);
    };
  }, [scaleInstance, eventInstance, map]);

  return currentScale;
};
