"use client";

// Components
export { Layer } from "./components/layer";
export { Map } from "./components/map";
export { Marker } from "./components/marker";
export { Popup } from "./components/popup";
export { Style } from "./components/style";

// Hooks
export { useDownload } from "./hooks/use-download";
export { useDraw } from "./hooks/use-draw";
export { useFeature } from "./hooks/use-feature";
export {
  createStylePreset,
  STYLE_PRESETS,
  useFeatureActions,
} from "./hooks/use-feature-actions";
export {
  createLayerMetadata,
  determineLayerTypeByODFId,
  getODFLayerId,
  useLayer,
} from "./hooks/use-layer";
export { useMap } from "./hooks/use-map";
export { useMapActions } from "./hooks/use-map-actions";
export {
  useMapInstance,
  useMapInstanceRequired,
} from "./hooks/use-map-instance";
export { useOverview } from "./hooks/use-overview";
export { useProjection } from "./hooks/use-projection";
export { useScale } from "./hooks/use-scale";

// Draw Types
export type { UseDrawNewOptions } from "./hooks/use-draw";

// New Providers (recommended)
export { DrawProvider } from "./providers/draw-provider";

// 세분화된 Control Provider들
export { BasemapProvider } from "./providers/basemap-provider";
export { ClearProvider } from "./providers/clear-provider";
export {
  MapProvider,
  useMapConfig,
  useMapProviderStatus,
} from "./providers/map-provider";
export { MeasureProvider } from "./providers/measure-provider";
export { ScaleProvider } from "./providers/scale-provider";

// Stores
export {
  useDrawActions,
  useDrawState,
  useDrawStore,
} from "./stores/draw-store";
export {
  useMapState,
  useMapStore,
  useMapStoreActions,
} from "./stores/map-store"; // 새로운 통합 Store

// Event Management
export {
  type LayerDetectionResult,
  type LayerFeatureInfo,
  type LayerQueueItem,
  useEvent,
} from "./hooks/use-event";
export { useEventState, useEventStoreActions } from "./stores/event-store";

// Utils
export {
  changeFeatureStyle,
  deleteFeature,
  extractFeatureId,
  validateStyleOptions,
} from "./utils/feature-actions";

// Types
export type * from "./types/event-types";
export type * from "./types/layer-types";
export type * from "./types/map-types";
export type * from "./types/marker-types";
export type * from "./types/popup-types";

// Explicit exports for commonly used types
export type { BasemapId } from "./types/map-types";
