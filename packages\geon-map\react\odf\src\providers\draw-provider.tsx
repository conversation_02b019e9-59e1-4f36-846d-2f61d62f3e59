"use client";

import React, { useEffect } from "react";

import { CoreInstanceManager } from "../stores/core-instances";
import { useDrawStore } from "../stores/draw-store";
import { useLayerStore } from "../stores/layer-store";
import { useMapStore } from "../stores/map-store";
import type { DrawControlOptions } from "../types/draw-types";

/**
 * DrawProvider 설정 옵션
 */
export interface DrawProviderOptions {
  /** Draw Control 초기화 옵션 */
  drawOptions?: DrawControlOptions;
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * 🎯 DrawControlProvider (Draw Control 전용)
 *
 * Draw Control만 초기화하는 독립적인 Provider입니다.
 * 그리기 기능이 필요한 경우에만 선언하세요.
 *
 * @example
 * ```tsx
 * <MapProvider>
 *   <DrawControlProvider drawOptions={{ tools: ["polygon", "point"] }}>
 *     <DrawingPanel />
 *   </DrawControlProvider>
 * </MapProvider>
 * ```
 */
export function DrawProvider({
  children,
  drawOptions = {
    continuity: false,
    createNewLayer: false,
    tools: ["text", "polygon", "lineString", "box", "point", "circle", "curve"],
  },
  autoInitialize = true,
  onError,
}: React.PropsWithChildren<DrawProviderOptions>) {
  const map = useMapStore((state) => state.map);
  const odf = useMapStore((state) => state.odf);
  const isLoading = useMapStore((state) => state.isLoading);
  const setDrawCore = useDrawStore((state) => state.setDrawCore);

  useEffect(() => {
    if (!autoInitialize) return;

    // Map이 준비되면 Draw Core 초기화
    if (map && odf && !isLoading) {
      try {
        const { drawCore, errors } = CoreInstanceManager.createDrawCore(
          map,
          odf,
          drawOptions,
        );

        if (drawCore) {
          setDrawCore(drawCore);

          // ✅ LayerStore에 Draw 레이어 등록
          const drawLayer = drawCore.getDrawLayer();
          if (drawLayer) {
            useLayerStore.getState().addDrawLayer(drawLayer, {
              name: "Draw Layer",
              visible: true,
            });
          }
        }

        if (errors.length > 0) {
          const error = new Error(
            `Draw Core 초기화 실패: ${errors.join(", ")}`,
          );
          console.error("❌ Draw Core initialization failed:", errors);
          onError?.(error);
        }
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        console.error("❌ Failed to initialize Draw core:", err);
        onError?.(err);
      }
    } else if (!map || !odf) {
      // Map이 초기화되지 않은 경우 경고 (로딩 중이 아닐 때만)
      if (!isLoading && process.env.NODE_ENV === "development") {
        console.warn(
          "⚠️ DrawControlProvider: Map 인스턴스가 준비되지 않았습니다.\n" +
            "확인사항: MapProvider가 DrawControlProvider보다 상위에 있는지 확인하세요.\n\n" +
            "올바른 구조:\n" +
            "<MapProvider>\n" +
            "  <DrawControlProvider>\n" +
            "    <App />\n" +
            "  </DrawControlProvider>\n" +
            "</MapProvider>",
        );
      }
    }

    // 컴포넌트 언마운트 시 정리
    return () => {
      setDrawCore(null);
    };
  }, [isLoading]);

  return <>{children}</>;
}

// Provider 상태 노출 훅 (단순 플래그)
export function useDrawProviderStatus() {
  // 현재는 Core 초기화 여부를 drawCore 존재로 판단
  const isReady = useDrawStore((state) => !!state.drawCore);
  return { isProviderSet: isReady };
}
