"use client";

import { LayerFactory } from "@geon-map/core";
import React, {
  createContext,
  useCallback,
  useContext,
  useEffect,
  useReducer,
} from "react";

import { useMapStore } from "../stores/map-store";
import type { Layer } from "../types/layer-types";

/**
 * Layer Provider 상태
 */
export interface LayerState {
  layers: Layer[];
  selectedLayerId?: string;
  expandedGroups: Set<string>;
  isLoading: boolean;
  error: Error | null;
}

/**
 * Layer Provider 액션
 */
export type LayerAction =
  | { type: "ADD_LAYER"; payload: Layer }
  | { type: "REMOVE_LAYER"; payload: string }
  | { type: "UPDATE_LAYER"; payload: { id: string; updates: Partial<Layer> } }
  | { type: "SET_LAYERS"; payload: Layer[] }
  | { type: "TOGGLE_LAYER_VISIBILITY"; payload: string }
  | { type: "SET_LAYER_FILTER"; payload: { id: string; filter: string } }
  | { type: "SET_SELECTED_LAYER"; payload: string }
  | { type: "TOGGLE_GROUP"; payload: string }
  | { type: "SET_LOADING"; payload: boolean }
  | { type: "SET_ERROR"; payload: Error | null };

/**
 * Layer Provider 옵션
 */
export interface LayerProviderOptions {
  /** 초기 레이어 목록 */
  initialLayers?: Layer[];
  /** 자동 레이어 로드 여부 */
  autoLoad?: boolean;
  /** 레이어 변경 시 콜백 */
  onLayerChange?: (layers: Layer[]) => void;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * Layer Provider Context 값
 */
export interface LayerContextValue {
  // 상태
  state: LayerState;
  layerFactory: LayerFactory | null;

  // 액션
  addLayer: (layer: Layer) => string | void;
  removeLayer: (layerId: string) => void;
  updateLayer: (layerId: string, updates: Partial<Layer>) => void;
  setLayers: (layers: Layer[]) => void;
  toggleLayerVisibility: (layerId: string) => void;
  setLayerFilter: (layerId: string, filter: string) => void;
  setSelectedLayer: (layerId: string) => void;
  toggleGroup: (groupId: string) => void;

  // 유틸리티
  getLayer: (layerId: string) => Layer | undefined;
  getVisibleLayers: () => Layer[];
  isLayerVisible: (layerId: string) => boolean;
  isGroupExpanded: (groupId: string) => boolean;
  updateLayerStyle: (layerId: string, style: any) => boolean;
}

// 초기 상태
const initialState: LayerState = {
  layers: [],
  selectedLayerId: undefined,
  expandedGroups: new Set(),
  isLoading: false,
  error: null,
};

// Reducer
function layerReducer(state: LayerState, action: LayerAction): LayerState {
  switch (action.type) {
    case "ADD_LAYER":
      return {
        ...state,
        layers: [...state.layers, action.payload],
      };

    case "REMOVE_LAYER":
      return {
        ...state,
        layers: state.layers.filter((layer) => layer.id !== action.payload),
        selectedLayerId:
          state.selectedLayerId === action.payload
            ? undefined
            : state.selectedLayerId,
      };

    case "UPDATE_LAYER":
      return {
        ...state,
        layers: state.layers.map((layer) =>
          layer.id === action.payload.id
            ? { ...layer, ...action.payload.updates }
            : layer
        ),
      };

    case "SET_LAYERS":
      return {
        ...state,
        layers: action.payload,
      };

    case "TOGGLE_LAYER_VISIBILITY":
      return {
        ...state,
        layers: state.layers.map((layer) =>
          layer.id === action.payload
            ? { ...layer, visible: !layer.visible }
            : layer
        ),
      };

    case "SET_LAYER_FILTER":
      return {
        ...state,
        layers: state.layers.map((layer) =>
          layer.id === action.payload.id
            ? { ...layer, filter: action.payload.filter }
            : layer
        ),
      };

    case "SET_SELECTED_LAYER":
      return {
        ...state,
        selectedLayerId: action.payload,
      };

    case "TOGGLE_GROUP":
      const newExpandedGroups = new Set(state.expandedGroups);
      if (newExpandedGroups.has(action.payload)) {
        newExpandedGroups.delete(action.payload);
      } else {
        newExpandedGroups.add(action.payload);
      }
      return {
        ...state,
        expandedGroups: newExpandedGroups,
      };

    case "SET_LOADING":
      return {
        ...state,
        isLoading: action.payload,
      };

    case "SET_ERROR":
      return {
        ...state,
        error: action.payload,
      };

    default:
      return state;
  }
}

// Context 생성
const LayerContext = createContext<LayerContextValue | null>(null);

/**
 * 🎯 LayerProvider
 *
 * 레이어 관리 기능을 제공하는 Provider입니다.
 * LayerFactory 인스턴스를 생성하고 레이어 상태를 관리합니다.
 *
 * @example
 * ```tsx
 * <Map>
 *   <LayerProvider
 *     initialLayers={[
 *       { id: 'base', name: '기본 레이어', visible: true, type: 'tile' }
 *     ]}
 *     onLayerChange={(layers) => console.log('Layers changed:', layers)}
 *   >
 *     <LayerPanel />
 *     <LayerControls />
 *   </LayerProvider>
 * </Map>
 * ```
 */
export function LayerProvider({
  children,
  initialLayers = [],
  onLayerChange,
  onError,
}: React.PropsWithChildren<LayerProviderOptions>) {
  const map = useMapStore((state) => state.map);
  const odf = useMapStore((state) => state.odf);

  const [state, dispatch] = useReducer(layerReducer, {
    ...initialState,
    layers: initialLayers,
  });

  const [layerFactory, setLayerFactory] = React.useState<LayerFactory | null>(
    null
  );

  // LayerFactory 초기화
  useEffect(() => {
    if (!map || !odf) return;

    try {
      const factory = new LayerFactory(map, odf);
      setLayerFactory(factory);
      console.log("LayerFactory initialized successfully");
    } catch (error) {
      const err = error instanceof Error ? error : new Error(String(error));
      dispatch({ type: "SET_ERROR", payload: err });
      onError?.(err);
      console.error("Failed to initialize LayerFactory:", err);
    }
  }, [map, odf, onError]);

  // 레이어 변경 감지
  useEffect(() => {
    onLayerChange?.(state.layers);
  }, [state.layers, onLayerChange]);

  // 액션 함수들
  const addLayer = useCallback((layer: Layer) => {
    dispatch({ type: "ADD_LAYER", payload: layer });
    return layer.id; // 레이어 ID 반환
  }, []);

  const removeLayer = useCallback((layerId: string) => {
    dispatch({ type: "REMOVE_LAYER", payload: layerId });
  }, []);

  const updateLayer = useCallback(
    (layerId: string, updates: Partial<Layer>) => {
      dispatch({ type: "UPDATE_LAYER", payload: { id: layerId, updates } });
    },
    []
  );

  const setLayers = useCallback((layers: Layer[]) => {
    dispatch({ type: "SET_LAYERS", payload: layers });
  }, []);

  const toggleLayerVisibility = useCallback((layerId: string) => {
    dispatch({ type: "TOGGLE_LAYER_VISIBILITY", payload: layerId });
  }, []);

  const setLayerFilter = useCallback((layerId: string, filter: string) => {
    dispatch({ type: "SET_LAYER_FILTER", payload: { id: layerId, filter } });
  }, []);

  const setSelectedLayer = useCallback((layerId: string) => {
    dispatch({ type: "SET_SELECTED_LAYER", payload: layerId });
  }, []);

  const toggleGroup = useCallback((groupId: string) => {
    dispatch({ type: "TOGGLE_GROUP", payload: groupId });
  }, []);

  // 유틸리티 함수들
  const getLayer = useCallback(
    (layerId: string) => {
      return state.layers.find((layer) => layer.id === layerId);
    },
    [state.layers]
  );

  const getVisibleLayers = useCallback(() => {
    return state.layers.filter((layer) => layer.visible);
  }, [state.layers]);

  const isLayerVisible = useCallback(
    (layerId: string) => {
      const layer = getLayer(layerId);
      return layer?.visible ?? false;
    },
    [getLayer]
  );

  const isGroupExpanded = useCallback(
    (groupId: string) => {
      return state.expandedGroups.has(groupId);
    },
    [state.expandedGroups]
  );

  const updateLayerStyle = useCallback(
    (layerId: string, style: any) => {
      const layer = getLayer(layerId);
      if (!layer) {
        console.error(`레이어를 찾을 수 없음 (ID: ${layerId})`);
        return false;
      }

      try {
        // 스타일 업데이트 로직 (기본 구현)
        updateLayer(layerId, { style });
        return true;
      } catch (error) {
        console.error(`스타일 업데이트 중 오류 (ID: ${layerId}):`, error);
        return false;
      }
    },
    [getLayer, updateLayer]
  );

  const contextValue: LayerContextValue = {
    state,
    layerFactory,
    addLayer,
    removeLayer,
    updateLayer,
    setLayers,
    toggleLayerVisibility,
    setLayerFilter,
    setSelectedLayer,
    toggleGroup,
    getLayer,
    getVisibleLayers,
    isLayerVisible,
    isGroupExpanded,
    updateLayerStyle,
  };

  return (
    <LayerContext.Provider value={contextValue}>
      {children}
    </LayerContext.Provider>
  );
}

/**
 * LayerProvider Context Hook
 */
export function useLayer(): LayerContextValue {
  const context = useContext(LayerContext);
  if (!context) {
    throw new Error("useLayer must be used within a LayerProvider");
  }
  return context;
}

/**
 * 안전한 LayerProvider Context Hook (에러를 던지지 않음)
 */
export function useLayerSafe(): LayerContextValue | null {
  return useContext(LayerContext);
}
