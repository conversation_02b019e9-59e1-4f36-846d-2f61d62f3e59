"use client";

import React, { useEffect } from "react";

import { CoreInstanceManager } from "../stores/core-instances";
import { useDrawStore } from "../stores/draw-store";
import { useLayerStore } from "../stores/layer-store";
import { useMapStore } from "../stores/map-store";
// 측정 옵션 타입은 core에서 직접 관리하므로 any로 수용
type MeasureControlOptions = any;

/**
 * MeasureProvider 설정 옵션
 */
export interface MeasureProviderOptions {
  /** Measure Control 초기화 옵션 */
  measureOptions?: MeasureControlOptions;
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * 🎯 MeasureProvider (Measure Control 전용)
 *
 * Measure Control만 초기화하는 독립적인 Provider입니다.
 * 측정 기능이 필요한 경우에만 선언하세요.
 *
 * @example
 * ```tsx
 * <MapProvider>
 *   <MeasureProvider measureOptions={{ tools: ["distance", "area"] }}>
 *     <MeasurementPanel />
 *   </MeasureProvider>
 * </MapProvider>
 * ```
 */
export function MeasureProvider({
  children,
  measureOptions = {
    tools: ["distance", "area", "round", "spot"],
    continuity: false,
    rightClickDelete: false,
  },
  onError,
}: React.PropsWithChildren<MeasureProviderOptions>) {
  const map = useMapStore((state) => state.map);
  const odf = useMapStore((state) => state.odf);
  const isLoading = useMapStore((state) => state.isLoading);
  const setMeasureCore = useDrawStore((state) => state.setMeasureCore);

  useEffect(() => {
    // Map이 준비되면 Measure Core 초기화
    if (map && odf && !isLoading) {
      try {
        const { measureCore, errors } =
          CoreInstanceManager.createMeasureOnlyCores(map, odf, measureOptions);

        if (measureCore) {
          setMeasureCore(measureCore);

          // ✅ LayerStore에 Measure 레이어 등록
          const measureLayer = measureCore.getMeasureLayer();
          if (measureLayer) {
            useLayerStore.getState().addMeasureLayer(measureLayer, {
              name: "Measure Layer",
              visible: true,
            });
          }
        }

        if (errors.length > 0) {
          const error = new Error(
            `Measure Core 초기화 실패: ${errors.join(", ")}`,
          );
          console.error("❌ Measure Core initialization failed:", errors);
          onError?.(error);
        }
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        console.error("❌ Failed to initialize Measure core:", err);
        onError?.(err);
      }
    } else if (!map || !odf) {
      // Map이 초기화되지 않은 경우 경고 (로딩 중이 아닐 때만)
      if (!isLoading && process.env.NODE_ENV === "development") {
        console.warn(
          "⚠️ MeasureProvider: Map 인스턴스가 준비되지 않았습니다.\n" +
            "확인사항: MapProvider가 MeasureProvider보다 상위에 있는지 확인하세요.\n\n" +
            "올바른 구조:\n" +
            "<MapProvider>\n" +
            "  <MeasureProvider>\n" +
            "    <App />\n" +
            "  </MeasureProvider>\n" +
            "</MapProvider>",
        );
      }
    }

    // 컴포넌트 언마운트 시 정리
    return () => {
      setMeasureCore(null);
    };
  }, [isLoading]);

  return <>{children}</>;
}
