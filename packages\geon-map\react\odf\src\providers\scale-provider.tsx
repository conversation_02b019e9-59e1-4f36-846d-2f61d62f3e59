"use client";

import React, { useEffect } from "react";

import { CoreInstanceManager } from "../stores/core-instances";
import { useMapStore } from "../stores/map-store";

/**
 * ScaleProvider 설정 옵션
 */
export interface ScaleProviderOptions {
  /** Scale Control 초기화 옵션 */
  scaleOptions?: {
    size?: number;
    scaleInput?: boolean;
  };
  /** 자동 초기화 여부 (기본: true) */
  autoInitialize?: boolean;
  /** 에러 발생 시 콜백 */
  onError?: (error: Error) => void;
}

/**
 * 🎯 ScaleProvider (Scale Control 전용)
 *
 * Scale Control만 초기화하는 독립적인 Provider입니다.
 * 축척 표시 기능이 필요한 경우에만 선언하세요.
 *
 * @example
 * ```tsx
 * <MapProvider>
 *   <ScaleProvider scaleOptions={{ size: 100, scaleInput: false }}>
 *     <ScaleWidget />
 *   </ScaleProvider>
 * </MapProvider>
 * ```
 */
export function ScaleProvider({
  children,
  scaleOptions = { size: 100, scaleInput: false },
  autoInitialize = true,
  onError,
}: React.PropsWithChildren<ScaleProviderOptions>) {
  const map = useMapStore((state) => state.map);
  const odf = useMapStore((state) => state.odf);
  const isLoading = useMapStore((state) => state.isLoading);
  const setScaleInstance = useMapStore((state) => state.setScaleInstance);

  useEffect(() => {
    if (!autoInitialize) return;

    // Map이 준비되면 Scale Core 초기화
    if (map && odf && !isLoading) {
      try {
        const { scaleInstance, errors } = CoreInstanceManager.createScaleCores(
          map,
          odf,
          scaleOptions,
        );

        if (scaleInstance) {
          setScaleInstance(scaleInstance);
        }

        if (errors.length > 0) {
          const error = new Error(
            `Scale Core 초기화 실패: ${errors.join(", ")}`,
          );
          console.error("❌ Scale Core initialization failed:", errors);
          onError?.(error);
        }
      } catch (error) {
        const err = error instanceof Error ? error : new Error(String(error));
        console.error("❌ Failed to initialize Scale core:", err);
        onError?.(err);
      }
    } else if (!map || !odf) {
      // Map이 초기화되지 않은 경우 경고 (로딩 중이 아닐 때만)
      if (!isLoading && process.env.NODE_ENV === "development") {
        console.warn(
          "⚠️ ScaleProvider: Map 인스턴스가 준비되지 않았습니다.\n" +
            "확인사항: MapProvider가 ScaleProvider보다 상위에 있는지 확인하세요.\n\n" +
            "올바른 구조:\n" +
            "<MapProvider>\n" +
            "  <ScaleProvider>\n" +
            "    <App />\n" +
            "  </ScaleProvider>\n" +
            "</MapProvider>",
        );
      }
    }

    // 컴포넌트 언마운트 시 정리
    return () => {
      setScaleInstance(null);
    };
  }, [isLoading]);

  return <>{children}</>;
}
