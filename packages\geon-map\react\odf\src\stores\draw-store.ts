import type { DrawControlOptions, MeasureControlOptions } from "@geon-map/core";
import { Clear, Draw, Measure } from "@geon-map/core";
import { create } from "zustand";
import { devtools } from "zustand/middleware";

import { CoreInstanceManager } from "./core-instances";

/**
 * 그리기 도구 관련 상태 타입
 */
interface DrawState {
  // 그리기 모드
  isDrawing: boolean;
  drawMode: string | null; // 'point', 'line', 'polygon', 'circle', etc.

  // 측정 모드
  isMeasuring: boolean;
  measureMode: string | null; // 'distance', 'area', etc.

  // 그리기 스타일
  drawStyle: any;
  measureStyle: any;

  // 그려진 피처들
  drawnFeatures: any[];
  measureResults: any[];

  // 설정
  continuity: boolean; // 연속 그리기 모드

  // Core 인스턴스 (우리가 만든 클래스) - 단일책임 분리
  drawCore: Draw | null;
  measureCore: Measure | null;
  clearCore: Clear | null;

  // ODF 컨트롤 인스턴스 (ODF 라이브러리)
  drawControl: any | null;
  measureControl: any | null;
  clearControl: any | null;

  // ❌ 제거됨: drawLayer, measureLayer는 LayerStore로 이동

  // 초기화 상태
  isReady: boolean;
  isInitializing: boolean;
  error: Error | null;
}

/**
 * 그리기 도구 관련 액션 타입
 */
interface DrawActions {
  // 그리기 모드 관리
  setDrawing: (isDrawing: boolean) => void;
  setDrawMode: (mode: string | null) => void;

  // 측정 모드 관리
  setMeasuring: (isMeasuring: boolean) => void;
  setMeasureMode: (mode: string | null) => void;

  // 스타일 관리
  setDrawStyle: (style: any) => void;
  setMeasureStyle: (style: any) => void;

  // 피처 관리
  addDrawnFeature: (feature: any) => void;
  removeDrawnFeature: (featureId: string) => void;
  clearDrawnFeatures: () => void;

  addMeasureResult: (result: any) => void;
  removeMeasureResult: (resultId: string) => void;
  clearMeasureResults: () => void;

  // 설정
  setContinuity: (continuity: boolean) => void;

  // Core 인스턴스 관리 (Store Actions 패턴) - 4개 Core 통합 관리
  setDrawCore: (core: Draw | null) => void;
  setMeasureCore: (core: Measure | null) => void;
  setClearCore: (core: Clear | null) => void;
  initializeAllCores: (
    map: any,
    odf: any,
    drawOptions: DrawControlOptions,
    measureOptions?: MeasureControlOptions,
  ) => Promise<void>;
  cleanupAllCores: () => void;

  // 그리기 액션 (Store Actions 패턴)
  startDrawing: (mode: string) => void;
  stopDrawing: () => void;
  deleteFeature: (feature: any) => boolean;
  clearAllFeatures: () => void;

  // 이벤트 처리 액션 (상태 연동)
  handleDrawEnd: (feature: any) => void;
  handleDrawStart: () => void;
  handleFeatureSelect: (feature: any) => void;
  handleMeasureEnd: (result: any) => void;

  // 개발자 도구
  getDebugInfo: () => any;

  // 전체 초기화
  reset: () => void;
}

type DrawStore = DrawState & DrawActions;

// 초기 상태
const initialState: DrawState = {
  isDrawing: false,
  drawMode: null,
  isMeasuring: false,
  measureMode: null,
  drawStyle: {
    fill: { color: [255, 0, 0, 0.3] },
    stroke: { color: [255, 0, 0, 1], width: 2 },
  },
  measureStyle: {
    fill: { color: [0, 100, 0, 0.3] },
    stroke: { color: [0, 100, 0, 1], width: 3 },
  },
  drawnFeatures: [],
  measureResults: [],
  continuity: false,
  drawCore: null,
  measureCore: null,
  clearCore: null,
  drawControl: null,
  measureControl: null,
  clearControl: null,
  isReady: false,
  isInitializing: false,
  error: null,
};

/**
 * 그리기 도구 전용 Zustand Store
 *
 * 그리기, 측정 관련 상태와 액션을 관리합니다.
 * Map Store와 분리하여 관심사를 명확히 구분합니다.
 */
export const useDrawStore = create<DrawStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // 그리기 모드 관리
      setDrawing: (isDrawing) => set({ isDrawing }, false, "setDrawing"),

      setDrawMode: (mode) => set({ drawMode: mode }, false, "setDrawMode"),

      // 측정 모드 관리
      setMeasuring: (isMeasuring) =>
        set({ isMeasuring }, false, "setMeasuring"),

      setMeasureMode: (mode) =>
        set({ measureMode: mode }, false, "setMeasureMode"),

      // 스타일 관리
      setDrawStyle: (style) => set({ drawStyle: style }, false, "setDrawStyle"),

      setMeasureStyle: (style) =>
        set({ measureStyle: style }, false, "setMeasureStyle"),

      // 피처 관리
      addDrawnFeature: (feature) =>
        set(
          (state) => ({
            drawnFeatures: [...state.drawnFeatures, feature],
          }),
          false,
          "addDrawnFeature",
        ),

      removeDrawnFeature: (featureId) =>
        set(
          (state) => ({
            drawnFeatures: state.drawnFeatures.filter(
              (f) => f.id !== featureId,
            ),
          }),
          false,
          "removeDrawnFeature",
        ),

      clearDrawnFeatures: () =>
        set({ drawnFeatures: [] }, false, "clearDrawnFeatures"),

      addMeasureResult: (result) =>
        set(
          (state) => ({
            measureResults: [...state.measureResults, result],
          }),
          false,
          "addMeasureResult",
        ),

      removeMeasureResult: (resultId) =>
        set(
          (state) => ({
            measureResults: state.measureResults.filter(
              (result) => result.id !== resultId,
            ),
          }),
          false,
          "removeMeasureResult",
        ),

      clearMeasureResults: () =>
        set({ measureResults: [] }, false, "clearMeasureResults"),

      // 설정
      setContinuity: (continuity) =>
        set({ continuity }, false, "setContinuity"),

      // Core 인스턴스 관리 (Store Actions 패턴) - 3개 Core 통합
      setDrawCore: (drawCore) => {
        const drawControl = drawCore?.getDrawControl() || null;
        const { measureCore, clearCore } = get();

        // 새로운 아키텍처: 각 Provider가 독립적으로 초기화하므로
        // 현재 설정된 Core들을 기반으로 isReady 계산
        const isReady = !!(drawCore || measureCore || clearCore);

        set({ drawCore, drawControl, isReady }, false, "setDrawCore");
      },

      setMeasureCore: (measureCore) => {
        const measureControl = measureCore?.getMeasureControl() || null;
        const { drawCore, clearCore } = get();

        // 새로운 아키텍처: 각 Provider가 독립적으로 초기화하므로
        // 현재 설정된 Core들을 기반으로 isReady 계산
        const isReady = !!(drawCore || measureCore || clearCore);

        set({ measureCore, measureControl, isReady }, false, "setMeasureCore");
      },

      setClearCore: (clearCore) => {
        const clearControl = clearCore?.getClearControl() || null;
        const { drawCore, measureCore } = get();

        // 새로운 아키텍처: 각 Provider가 독립적으로 초기화하므로
        // 현재 설정된 Core들을 기반으로 isReady 계산
        const isReady = !!(drawCore || measureCore || clearCore);

        set({ clearCore, clearControl, isReady }, false, "setClearCore");
      },

      initializeAllCores: async (map, odf, drawOptions, measureOptions) => {
        const { drawCore: existingDrawCore, isReady, isInitializing } = get();

        // 🛡️ 중복 초기화 방지
        if (existingDrawCore || isReady || isInitializing) {
          console.warn("Cores are already initialized or initializing", {
            existingDrawCore: !!existingDrawCore,
            isReady,
            isInitializing,
          });
          return;
        }

        try {
          set(
            { error: null, isInitializing: true },
            false,
            "initializeAllCores:start",
          );

          // 🎯 CoreInstanceManager를 사용한 통합 초기화
          const result = await CoreInstanceManager.createDrawCores(
            map,
            odf,
            drawOptions,
            measureOptions,
          );

          // Core 상태 검증
          const validation = CoreInstanceManager.validateDrawCores({
            drawCore: result.drawCore,
          });

          if (!validation.isValid) {
            throw validation.error;
          }

          // Store 업데이트 (부분 성공도 허용)
          set(
            {
              drawCore: result.drawCore,
              measureCore: result.measureCore,
              clearCore: result.clearCore,
              drawControl: result.drawControl,
              measureControl: result.measureControl,
              clearControl: result.clearControl,
              isReady: true,
              isInitializing: false,
              error:
                result.errors.length > 0
                  ? new Error(result.errors.join("; "))
                  : null,
            },
            false,
            "initializeAllCores:success",
          );

          console.log(
            `🎉 Core initialization completed (${4 - result.errors.length}/4 successful)`,
          );
        } catch (error) {
          const err = error instanceof Error ? error : new Error(String(error));
          set(
            {
              drawCore: null,
              measureCore: null,
              clearCore: null,
              drawControl: null,
              measureControl: null,
              clearControl: null,
              isReady: false,
              isInitializing: false,
              error: err,
            },
            false,
            "initializeAllCores:error",
          );
          console.error("Failed to initialize cores:", err);
          throw err;
        }
      },

      cleanupAllCores: () => {
        const { drawCore, measureCore, clearCore } = get();

        // TODO: 이벤트 리스너 정리 (기존 core에서 처리하던 이벤트 정리 코드를 react로 옮김)
        // EventListenerManager.cleanupAllEventListeners();

        // CoreInstanceManager를 사용한 Core 인스턴스 정리
        CoreInstanceManager.cleanupDrawCores({
          drawCore,
          measureCore,
          clearCore,
        });

        set(
          {
            drawCore: null,
            measureCore: null,
            clearCore: null,
            drawControl: null,
            measureControl: null,
            clearControl: null,
            isReady: false,
            isInitializing: false,
            error: null,
            isDrawing: false,
            drawMode: null,
            isMeasuring: false,
            measureMode: null,
          },
          false,
          "cleanupAllCores",
        );
      },

      // 🎯 그리기/측정 액션 (비즈니스 로직 처리)
      startDrawing: (mode) => {
        const { drawCore, measureCore } = get();

        // 측정 모드인지 확인
        const isMeasureMode = mode.startsWith("measure-");

        if (isMeasureMode) {
          // 측정 모드 - Measure Core 사용
          if (!measureCore) {
            console.warn(
              "⚠️ 측정 기능을 사용할 수 없습니다.\n" +
                "확인사항: DrawProvider가 설정되어 있고 지도가 초기화되었는지 확인하세요.\n" +
                "현재 상태: Measure Core가 초기화되지 않음",
            );
            return;
          }

          try {
            const measureMode = mode.replace("measure-", "") as any;
            measureCore.startMeasuring(measureMode);
            set(
              { isMeasuring: true, measureMode: measureMode },
              false,
              "startMeasuring",
            );
            console.log(`측정 시작: ${measureMode}`);
          } catch (error) {
            console.error("측정 시작 실패:", error);
          }
        } else {
          // 그리기 모드 - Draw Core 사용
          if (!drawCore) {
            console.warn(
              "⚠️ 그리기 기능을 사용할 수 없습니다.\n" +
                "확인사항: DrawProvider가 설정되어 있고 지도가 초기화되었는지 확인하세요.\n" +
                "현재 상태: Draw Core가 초기화되지 않음",
            );
            return;
          }

          try {
            drawCore.startDrawing(mode as any);
            set({ isDrawing: true, drawMode: mode }, false, "startDrawing");
            console.log(`그리기 시작: ${mode}`);
          } catch (error) {
            console.error("그리기 시작 실패:", error);
          }
        }
      },

      stopDrawing: () => {
        const { drawCore, measureCore, isDrawing, isMeasuring } = get();

        try {
          if (isDrawing && drawCore) {
            drawCore.stopDrawing();
            set({ isDrawing: false, drawMode: null }, false, "stopDrawing");
            console.log("그리기 중지");
          }

          if (isMeasuring && measureCore) {
            measureCore.stopMeasuring();
            set(
              { isMeasuring: false, measureMode: null },
              false,
              "stopMeasuring",
            );
            console.log("측정 중지");
          }
        } catch (error) {
          console.error("중지 실패:", error);
        }
      },

      deleteFeature: (feature) => {
        const { drawCore } = get();
        if (!drawCore) return false;

        try {
          const success = drawCore.deleteFeature(feature);
          if (success) {
            // Store에서도 제거
            set(
              (state) => ({
                drawnFeatures: state.drawnFeatures.filter(
                  (f) => f.feature !== feature,
                ),
              }),
              false,
              "deleteFeature",
            );
          }
          return success;
        } catch (error) {
          console.error("피처 삭제 실패:", error);
          return false;
        }
      },

      clearAllFeatures: () => {
        const { clearCore } = get();
        if (!clearCore) {
          console.warn("Clear core is not ready");
          return;
        }

        try {
          clearCore.clear(); // ✅ Clear Core 사용
          // Store에서도 모든 피처 제거
          set(
            {
              drawnFeatures: [],
              measureResults: [],
            },
            false,
            "clearAllFeatures",
          );
        } catch (error) {
          console.error("전체 삭제 실패:", error);
        }
      },

      // 🎯 이벤트 처리 액션 (상태 연동)
      handleDrawEnd: (feature) => {
        console.log("📝 Store: Draw end handled", feature);
        set(
          {
            isDrawing: false,
            drawMode: null,
          },
          false,
          "handleDrawEnd",
        );

        // 그린 피처를 Store에 추가
        if (feature) {
          set(
            (state) => ({
              drawnFeatures: [
                ...state.drawnFeatures,
                {
                  id: Date.now().toString(),
                  feature,
                  type: "draw",
                  createdAt: new Date(),
                },
              ],
            }),
            false,
            "addDrawnFeature",
          );
        }
      },

      handleDrawStart: () => {
        console.log("📝 Store: Draw start handled");
        set({ isDrawing: true }, false, "handleDrawStart");
      },

      handleFeatureSelect: (feature) => {
        console.log("📝 Store: Feature select handled", feature);
        // TODO: 선택된 피처 상태 업데이트
        // 현재는 useDrawInteraction에서 처리하므로 필요시 구현
      },

      handleMeasureEnd: (result) => {
        console.log("📏 Store: Measure end handled", result);
        set(
          {
            isMeasuring: false,
            measureMode: null,
          },
          false,
          "handleMeasureEnd",
        );

        // 측정 결과를 Store에 추가
        if (result) {
          set(
            (state) => ({
              measureResults: [
                ...state.measureResults,
                {
                  id: Date.now().toString(),
                  result,
                  type: "measure",
                  createdAt: new Date(),
                },
              ],
            }),
            false,
            "addMeasureResult",
          );
        }
      },

      // 전체 초기화
      reset: () => set(initialState, false, "reset"),

      // 🛠️ 개발자 도구 (디버깅용)
      getDebugInfo: () => {
        const state = get();
        return {
          cores: {
            draw: {
              instance: !!state.drawCore,
              control: !!state.drawControl,
              ready: !!state.drawCore && !!state.drawControl,
            },
            measure: {
              instance: !!state.measureCore,
              control: !!state.measureControl,
              ready: !!state.measureCore && !!state.measureControl,
            },
            clear: {
              instance: !!state.clearCore,
              control: !!state.clearControl,
              ready: !!state.clearCore && !!state.clearControl,
            },
          },
          state: {
            isReady: state.isReady,
            isInitializing: state.isInitializing,
            isDrawing: state.isDrawing,
            drawMode: state.drawMode,
            isMeasuring: state.isMeasuring,
            measureMode: state.measureMode,
            featureCount: state.drawnFeatures.length,
            measureCount: state.measureResults.length,
            hasError: !!state.error,
            errorMessage: state.error?.message,
          },
        };
      },
    }),
    {
      name: "draw-store",
    },
  ),
);

/**
 * 그리기 상태만 읽는 훅 (읽기 전용)
 */
export const useDrawState = () => {
  return useDrawStore((state) => ({
    isDrawing: state.isDrawing,
    drawMode: state.drawMode,
    isMeasuring: state.isMeasuring,
    measureMode: state.measureMode,
    drawnFeatures: state.drawnFeatures,
    measureResults: state.measureResults,
    continuity: state.continuity,
  }));
};

/**
 * 그리기 액션만 가져오는 훅
 */
export const useDrawActions = () => {
  return useDrawStore((state) => ({
    setDrawing: state.setDrawing,
    setDrawMode: state.setDrawMode,
    setMeasuring: state.setMeasuring,
    setMeasureMode: state.setMeasureMode,
    setDrawStyle: state.setDrawStyle,
    setMeasureStyle: state.setMeasureStyle,
    addDrawnFeature: state.addDrawnFeature,
    removeDrawnFeature: state.removeDrawnFeature,
    clearDrawnFeatures: state.clearDrawnFeatures,
    addMeasureResult: state.addMeasureResult,
    removeMeasureResult: state.removeMeasureResult,
    clearMeasureResults: state.clearMeasureResults,
    setContinuity: state.setContinuity,
    setDrawCore: state.setDrawCore,
    reset: state.reset,
  }));
};
