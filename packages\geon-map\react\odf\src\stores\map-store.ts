import { create } from "zustand";
import { devtools } from "zustand/middleware";

import type { MapInitializeOptions } from "../types/map-types";

// 타입 정의 (임시)
type ODF = any;
type ODF_MAP = any;

/**
 * 🎯 핵심 지도 상태 (Map Store)
 *
 * 오직 지도 자체와 관련된 상태만 관리
 * - 지도 인스턴스 (map, odf)
 * - 지도 뷰 상태 (center, zoom, scale)
 * - Core 인스턴스들 (mapController, eventInstance 등)
 * - 베이스맵 상태
 */
interface MapState {
  // 지도 인스턴스
  map: ODF_MAP | null;
  odf: ODF | null;
  isLoading: boolean;
  error: string | null;

  // 지도 설정
  mapOptions: MapInitializeOptions;

  // 지도 뷰 상태
  center: [number, number];
  zoom: number;
  mouseCoords: [number, number] | null;
  scale: string;

  // 베이스맵 상태
  currentBasemap: string | null;
  availableBasemaps: string[];

  // Core 인스턴스들
  mapInstance: any | null;
  eventInstance: any | null;
  scaleInstance: any | null;
  basemapInstance: any | null;
}

/**
 * 🎯 핵심 지도 액션 (Map Actions)
 *
 * 지도 자체 조작만 담당하는 순수한 액션들
 */
interface MapActions {
  // 지도 인스턴스 관리
  setMap: (map: ODF_MAP | null) => void;
  setOdf: (odf: ODF | null) => void;
  setLoading: (loading: boolean) => void;
  setError: (error: string | null) => void;

  // 지도 뷰 조작 (Store Actions 패턴)
  setCenter: (center: [number, number], fromProjection?: string) => void;
  setZoom: (zoom: number) => void;
  panTo: (center: [number, number], fromProjection?: string) => void;
  moveToCurrentLocation: (zoom?: number) => void;

  // 지도 상태 업데이트 (내부용)
  updateCenter: (center: [number, number]) => void;
  updateZoom: (zoom: number) => void;
  setMouseCoords: (coords: [number, number] | null) => void;
  setScale: (scale: string) => void;

  // 베이스맵 관리 (상태 업데이트)
  setCurrentBasemap: (basemap: string) => void;
  setAvailableBasemaps: (basemaps: string[]) => void;

  // 베이스맵 액션 (비즈니스 로직)
  switchBasemap: (basemapId: string) => void;
  getCurrentBasemap: () => string | null;
  getAvailableBasemaps: () => string[];

  // Core 인스턴스 관리
  setMapInstance: (instance: any) => void;
  setEventInstance: (instance: any) => void;
  setScaleInstance: (instance: any) => void;
  setBasemapInstance: (instance: any) => void;

  // 설정 관리
  updateMapOptions: (options: Partial<MapInitializeOptions>) => void;

  // 초기화
  reset: () => void;
}

type MapStore = MapState & MapActions;

// 초기 상태
const initialState: MapState = {
  map: null,
  odf: null,
  isLoading: true,
  error: null,
  mapOptions: {},
  center: [899587.8889468038, 1664175.9854401087], // EPSG:5179 기본값
  zoom: 11,
  mouseCoords: null,
  scale: "",
  currentBasemap: null,
  availableBasemaps: [],
  mapInstance: null,
  eventInstance: null,
  scaleInstance: null,
  basemapInstance: null,
};

/**
 * 🎯 지도 Store (기존 useMapStore 대체)
 *
 * 지도 자체와 관련된 상태만 관리하는 깔끔한 Store
 * 기존 useMapStore를 완전히 대체합니다.
 */
export const useMapStore = create<MapStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // 지도 인스턴스 관리
      setMap: (map) => set({ map }, false, "setMap"),
      setOdf: (odf) => set({ odf }, false, "setOdf"),
      setLoading: (isLoading) => set({ isLoading }, false, "setLoading"),
      setError: (error) => set({ error }, false, "setError"),

      // 지도 뷰 조작 (Store Actions 패턴)
      setCenter: (center, fromProjection) => {
        const { mapInstance } = get();

        if (mapInstance) {
          try {
            if (fromProjection) {
              const transformedCenter = mapInstance.transformCoordinate(
                center,
                fromProjection,
              );
              mapInstance.setCenter(transformedCenter);
            } else {
              mapInstance.setCenter(center);
            }
          } catch (error) {
            console.error("중심점 설정 실패:", error);
          }
        }

        set({ center }, false, "setCenter");
      },

      setZoom: (zoom) => {
        const { mapInstance } = get();

        if (mapInstance) {
          try {
            mapInstance.setZoom(zoom);
          } catch (error) {
            console.error("줌 레벨 설정 실패:", error);
          }
        }

        set({ zoom }, false, "setZoom");
      },

      panTo: (center, fromProjection) => {
        const actions = get();
        actions.setCenter(center, fromProjection);
      },

      moveToCurrentLocation: (zoom = 15) => {
        if (!navigator.geolocation) {
          console.warn("Geolocation을 사용할 수 없습니다.");
          return;
        }

        navigator.geolocation.getCurrentPosition(
          (position) => {
            const { latitude, longitude } = position.coords;
            const center: [number, number] = [longitude, latitude];

            const actions = get();
            actions.setCenter(center);
            actions.setZoom(zoom);
          },
          (error) => {
            console.error("위치 정보를 가져올 수 없습니다:", error);
          },
        );
      },

      // 지도 상태 업데이트 (내부용 - 이벤트 핸들러에서 사용)
      updateCenter: (center) => set({ center }, false, "updateCenter"),
      updateZoom: (zoom) => set({ zoom }, false, "updateZoom"),
      setMouseCoords: (mouseCoords) =>
        set({ mouseCoords }, false, "setMouseCoords"),
      setScale: (scale) => set({ scale }, false, "setScale"),

      // 베이스맵 관리 (상태 업데이트)
      setCurrentBasemap: (currentBasemap) =>
        set({ currentBasemap }, false, "setCurrentBasemap"),
      setAvailableBasemaps: (availableBasemaps) =>
        set({ availableBasemaps }, false, "setAvailableBasemaps"),

      // 베이스맵 액션 (비즈니스 로직)
      switchBasemap: (basemapId) => {
        const { basemapInstance } = get();

        if (basemapInstance) {
          try {
            basemapInstance.switchBaseLayer(basemapId);
            // 상태 업데이트
            set({ currentBasemap: basemapId }, false, "switchBasemap");
          } catch (error) {
            console.error("베이스맵 변경 실패:", error);
          }
        } else {
          console.warn("BasemapController가 초기화되지 않았습니다.");
        }
      },

      getCurrentBasemap: () => {
        const { basemapInstance } = get();

        if (basemapInstance) {
          try {
            return basemapInstance.getCurrentBasemap();
          } catch (error) {
            console.error("현재 베이스맵 조회 실패:", error);
            return null;
          }
        }

        return null;
      },

      getAvailableBasemaps: () => {
        const { basemapInstance } = get();

        if (basemapInstance) {
          try {
            return basemapInstance.getAvailableBasemaps();
          } catch (error) {
            console.error("베이스맵 목록 조회 실패:", error);
            return [];
          }
        }

        return [];
      },

      // Core 인스턴스 관리
      setMapInstance: (mapInstance) =>
        set({ mapInstance }, false, "setMapInstance"),
      setEventInstance: (eventInstance) =>
        set({ eventInstance }, false, "setEventInstance"),
      setScaleInstance: (scaleInstance) =>
        set({ scaleInstance }, false, "setScaleInstance"),
      setBasemapInstance: (basemapInstance) =>
        set({ basemapInstance }, false, "setBasemapInstance"),

      // 설정 관리
      updateMapOptions: (options) =>
        set(
          (state) => ({ mapOptions: { ...state.mapOptions, ...options } }),
          false,
          "updateMapOptions",
        ),

      // 초기화
      reset: () => set(initialState, false, "reset"),
    }),
    {
      name: "map-store",
    },
  ),
);

/**
 * 지도 상태만 읽는 훅 (읽기 전용)
 */
export const useMapState = () => {
  return useMapStore((state) => ({
    map: state.map,
    odf: state.odf,
    isLoading: state.isLoading,
    error: state.error,
    center: state.center,
    zoom: state.zoom,
    mouseCoords: state.mouseCoords,
    scale: state.scale,
    currentBasemap: state.currentBasemap,
    availableBasemaps: state.availableBasemaps,
    isReady: Boolean(
      state.map && state.odf && !state.isLoading && !state.error,
    ),
  }));
};

/**
 * 지도 기본 액션만 가져오는 훅 (내부용)
 *
 * 일반적으로는 hooks/use-map-actions.tsx의 useMapActions를 사용하세요.
 * 이 훅은 Store Actions만 제공하며, 베이스맵이나 유틸리티 함수는 포함하지 않습니다.
 */
export const useMapStoreActions = () => {
  return useMapStore((state) => ({
    setCenter: state.setCenter,
    setZoom: state.setZoom,
    panTo: state.panTo,
    moveToCurrentLocation: state.moveToCurrentLocation,
    // 베이스맵 액션들
    switchBasemap: state.switchBasemap,
    getCurrentBasemap: state.getCurrentBasemap,
    getAvailableBasemaps: state.getAvailableBasemaps,
  }));
};
