export type DrawId =
  | "text" // 텍스트 그리기 툴
  | "polygon" // 다각형 그리기 툴
  | "lineString" // 선 그리기 툴
  | "box" // 사각형 그리기 툴
  | "point" // 점 그리기 툴
  | "circle" // 원 그리기 툴
  | "curve" // 곡선 그리기 툴
  | "buffer"; // 버퍼 그리기 툴

export interface DrawControlOptions {
  /**
   * 연속 측정 기능 활성화 여부
   * - true: 측정 도구 지속 사용 (종료는 clean() 호출)
   * - false: 한 번만 측정
   */
  continuity?: boolean;

  /**
   * 측정 옵션 활성화 여부 (선/원 그리기 툴에서만 적용)
   */
  measure?: boolean;

  /**
   * 레이어를 새로 생성할지 여부
   * - false: 공유 레이어 사용 (id: odf-layer-draw-unique)
   * - true: 고유 레이어 생성 (id: odf-layer-draw-xxxx)
   */
  createNewLayer?: boolean;

  /**
   * 생성할 그리기 툴 배열
   * - 미지정 시 모든 도구 생성
   */
  tools?: DrawId[];

  /**
   * 우클릭 편집 기능 메뉴
   */
  editFeatureMenu?: ("modify" | "dragTranslate" | "delete" | "setText")[];

  /**
   * 사용자 정의 메시지
   */
  message?: Partial<
    Record<
      | "DRAWSTART_POINT"
      | "DRAWSTART_LINESTRING"
      | "DRAWSTART_POLYGON"
      | "DRAWSTART_CURVE"
      | "DRAWSTART_TEXT"
      | "DRAWSTART_BUFFER"
      | "DRAWSTART_CIRCLE"
      | "DRAWSTART_BOX"
      | "DRAWEND_DRAG"
      | "DRAWEND_DBCLICK",
      string
    >
  >;

  /**
   * 기본 도형 스타일
   */
  style?: {
    fill?: {
      color: [number, number, number, number]; // RGBA
    };
    stroke?: {
      color: [number, number, number, number];
      width: number;
    };
    image?: {
      circle?: {
        fill?: {
          color: [number, number, number, number];
        };
        stroke?: {
          color: [number, number, number, number];
          width: number;
        };
        radius?: number;
      };
    };
    text?: {
      textAlign?: "left" | "center" | "right";
      font?: string;
      fill?: {
        color: [number, number, number, number];
      };
      stroke?: {
        color: [number, number, number, number];
      };
    };
  };

  /**
   * 버퍼 도형 스타일
   */
  bufferStyle?: {
    stroke?: {
      color: [number, number, number, number];
      width: number;
    };
    fill?: {
      color: [number, number, number, number];
    };
  };
}
