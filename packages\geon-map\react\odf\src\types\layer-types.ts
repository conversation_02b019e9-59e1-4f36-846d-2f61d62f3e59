import type { ReactNode } from "react";

export interface RenderOptions {
  style?: any; // odfFlatStyle | odfStyleRule | odfWebGLVectorTileStyle
}

// 공통 레이어 속성
interface BaseLayerProps {
  id?: string;
  type: LayerType;
  renderOptions?: RenderOptions;
  className?: string;
  webGLRender?: boolean;
  attributions?: string[];
  children?: ReactNode;
  visible?: boolean;
  zIndex?: number;
  service?: LayerService;
}

// 서비스 타입 정의
export type LayerService =
  | "wms"
  | "wfs"
  | "group"
  | "cluster"
  | "hotspot"
  | "heatmap"
  | "aggregate";

// Empty 레이어 옵션 - 파라미터가 필요 없는 빈 레이어
export interface EmptyLayerProps extends BaseLayerProps {
  type: "empty";
  // 추가 파라미터가 필요하지 않음
}

// Geoserver 레이어 옵션
export interface GeoserverLayerProps extends BaseLayerProps {
  type: "geoserver";
  server:
    | string
    | {
        url: string;
        version?: string;
        proxyURL?: string;
        proxyParam?: string;
      };
  info: {
    lyrId: string;
    lyrNm: string;
  };
  layer: string;
  service: LayerService;
  limit?: number;
  crtfckey?: string;
  bbox?: boolean;
  method?: "get" | "post";
  tiled?: boolean;
  projection?: string;
  geometryType?: string;
  serviceTy?: string;
  name?: string;
  visible?: boolean;
  zIndex?: number;
}

// GeoTIFF 레이어 옵션
export interface GeoTiffLayerProps extends BaseLayerProps {
  type: "geotiff";
  sources: Array<{
    url?: string;
    overviews?: string[];
    proxyURL?: string;
    proxyParam?: string;
    blob?: Blob;
    bands?: number[];
    min?: number;
    max?: number;
    nodata?: number;
  }>;
  normalize?: boolean;
  wrapX?: boolean;
  opaque?: boolean;
  transition?: number;
}

// GeoJSON 레이어 옵션
export interface GeoJSONLayerProps extends BaseLayerProps {
  type: "geojson";
  data: any;
  service: LayerService;
  dataProjectionCode: string;
  featureProjectionCode: string;
}

// KML 레이어 옵션
export interface KMLLayerProps extends BaseLayerProps {
  type: "kml";
  data: string;
  dataProjectionCode: string;
  featureProjectionCode: string;
}

// CSV 레이어 옵션
export interface CSVLayerProps extends BaseLayerProps {
  type: "csv";
  data: string;
  dataProjectionCode: string;
  featureProjectionCode: string;
  geometryColumnName: string;
  delimiter?: string;
}

// API 레이어 옵션
export interface APILayerProps extends BaseLayerProps {
  type: "api";
  server:
    | string
    | {
        url: string;
        proxyURL?: string;
        proxyParam?: string;
      };
  service: "wms" | "wfs";
  bbox?: boolean;
  tiled?: boolean;
  tileGrid?: {
    origin: number[];
    resolutions: number[];
    matrixIds: string[] | number[];
  };
  originalOption?: {
    REQUEST?: boolean;
    SERVICE?: boolean;
    BBOX?: boolean | string;
    LAYER_PROJECTION?: string;
    TILEROW_TILECOL_INVERTED_STATE?: boolean;
    WIDTH?: boolean;
    HEIGHT?: boolean;
    VERSION?: boolean;
    TRANSPARENT?: boolean;
    STYLES?: boolean;
    CRS?: boolean;
  };
  parameterFilter?: (params: any) => any;
}

// 모든 레이어 타입을 하나로 통합
export type LayerProps =
  | GeoserverLayerProps
  | GeoTiffLayerProps
  | GeoJSONLayerProps
  | KMLLayerProps
  | CSVLayerProps
  | APILayerProps
  | SVGLayerProps
  | EmptyLayerProps;

export type LayerType =
  | "geoserver"
  | "geotiff"
  | "geojson"
  | "kml"
  | "csv"
  | "api"
  | "svg"
  | "empty"
  | "geoImage"
  | "draw" // 🆕 그리기 레이어
  | "measure" // 🆕 측정 레이어
  | "clear"; // 🆕 정리 레이어

// Add SVG layer props interface
export interface SVGLayerProps extends BaseLayerProps {
  type: "svg";
  svgContainer: any; // You might want to specify a more specific type depending on your SVG container
  extent: number[]; // [minx, miny, maxx, maxy]
}

export interface LayerStyle {
  type: string;
  properties: Record<string, unknown>;
}

export interface LayerInfo {
  lyrId: string;
  lyrNm: string;
  description?: string;
  metadata?: Record<string, unknown>;
}

export interface LayerConfig {
  type: LayerType;
  params: {
    service?: "wms" | "wfs";
    url?: string;
    layers?: string;
    [key: string]: unknown;
  };
}

export interface Layer {
  id: string;
  name: string;
  type: LayerType;
  visible: boolean;
  zIndex: number;
  odfLayer: any; // ODF Layer 인스턴스
  params: {
    service?: LayerService;
    [key: string]: any;
  };
  info?: LayerInfo;
  style?: LayerStyle;
  filter?: string;
  opacity?: number;
  group?: string;
  children?: Layer[];
}

export interface LayerContextState {
  layers: Layer[];
  selectedLayerId?: string;
  expandedGroups: Set<string>;
}

export type LayerAction =
  | { type: "ADD_LAYER"; payload: Layer }
  | { type: "REMOVE_LAYER"; payload: string }
  | { type: "UPDATE_LAYER"; payload: { id: string; updates: Partial<Layer> } }
  | { type: "SET_LAYERS"; payload: Layer[] }
  | { type: "TOGGLE_LAYER_VISIBILITY"; payload: string }
  | { type: "SET_LAYER_FILTER"; payload: { id: string; filter: string } }
  | { type: "SET_SELECTED_LAYER"; payload: string }
  | { type: "TOGGLE_GROUP"; payload: string };

export interface AddFeatureOptions {
  /** 피쳐 좌표계 SRID */
  srid?: string;
}
