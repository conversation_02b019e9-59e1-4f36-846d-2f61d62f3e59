import { ReactNode } from "react";

export type MarkerPositioning =
  | "top-left"
  | "top-center"
  | "top-right"
  | "center-left"
  | "center-center"
  | "center-right"
  | "bottom-left"
  | "bottom-center"
  | "bottom-right";

export interface MarkerStyle {
  width?: string;
  height?: string;
  src?: string;
  element?: HTMLElement;
}

export interface OdfMarker {
  getPosition(format: "array"): number[];
  setMap(map: any): void;
}

export interface MarkerEvent {
  type: string;
  target: OdfMarker;
}

export interface MarkerProps {
  /** 마커의 위치 [x, y] */
  position: [number, number];
  /** 드래그 가능 여부 */
  draggable?: boolean;
  /** 마커의 상대적 위치 */
  positioning?: MarkerPositioning;
  /** 기준점으로부터의 오프셋 [x, y] */
  offset?: [number, number];
  /** 이벤트 전파 중지 여부 */
  stopEvent?: boolean;
  /** 마커 스타일 */
  style?: MarkerStyle;
  /** 지도 화면에서 팝업이 잘려나올 경우 지도 이동 여부 */
  autoPan?: boolean;
  /** 지도 이동 애니메이션 시간 (ms) */
  autoPanAnimation?: number;
  /** 팝업과 지도 사이의 여백 (px) */
  autoPanMargin?: number;
  /** 자식 요소 (팝업 등) */
  children?: ReactNode;
  /** 마커 클릭 이벤트 핸들러 */
  onClick?: (event: MarkerEvent, marker: OdfMarker) => void;
  /** 마커 드래그 시작 이벤트 핸들러 */
  onDragStart?: (event: MarkerEvent, marker: OdfMarker) => void;
  /** 마커 드래그 중 이벤트 핸들러 */
  onDrag?: (event: MarkerEvent, marker: OdfMarker) => void;
  /** 마커 드래그 종료 이벤트 핸들러 */
  onDragEnd?: (event: MarkerEvent, marker: OdfMarker) => void;
  /** 마커 위치 변경 이벤트 핸들러 */
  onPositionChange?: (event: MarkerEvent, marker: OdfMarker) => void;
  /** 마커 맵 변경 이벤트 핸들러 */
  onMapChange?: (event: MarkerEvent, marker: OdfMarker) => void;
}
