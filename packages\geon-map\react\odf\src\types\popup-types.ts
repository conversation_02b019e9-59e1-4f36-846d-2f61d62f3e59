import { ReactNode } from "react";

export type Position = [number, number];

export interface PopupOptions {
  position?: Position;
  offset?: [number, number];
  positioning?:
    | "bottom-left"
    | "bottom-center"
    | "bottom-right"
    | "center-left"
    | "center-center"
    | "center-right"
    | "top-left"
    | "top-center"
    | "top-right";
  autoPan?: boolean;
  autoPanAnimation?: number;
  autoPanMargin?: number;
  content?: string | HTMLElement;
}

// 새로운 개선된 Popup 컴포넌트용 타입
export interface PopupProps extends PopupOptions {
  children?: ReactNode;
  /** 팝업 표시 여부 */
  visible?: boolean;
  /** 팝업이 열릴 때 호출되는 콜백 */
  onOpen?: () => void;
  /** 팝업이 닫힐 때 호출되는 콜백 */
  onClose?: () => void;
  /** 팝업 고유 키 (성능 최적화용) */
  popupKey?: string;
  /** 애니메이션 지속 시간 (ms) */
  animationDuration?: number;
  /** 팝업 z-index */
  zIndex?: number;
}
