
# 위젯 명명 규칙
### ※ 공통 명명규칙 필요한것 있으면 계속추가해주세요.)

## 📚위젯 컴포넌트 및 파일 명명 규칙
| 구분           | 규칙                                                 | 예시                                                  |
| ------------ |----------------------------------------------------|-----------------------------------------------------|
| 📁 파일명       | `[위젯명]-widget.tsx`                                 | `address-search-widget.tsx`                         |
| 🧩 완성형 컴포넌트  | `[위젯명] + Widget`                                   | `<AddressSearchWidget />`                           |
| 🧱 조립형 루트    | `[위젯명]`                                            | `<AddressSearch>`                                   |
| 🔧 조립형 하위 요소 | `[위젯명] + ShadCN-style 접미사`<br/> `[위젯명] + HTML 태그명` | `AddressSearchTrigger`, `AddressSearchSelect`, etc. |


### 접미사 참고
| **접미사**                                     | **의미 / 용도**           |
| ------------------------------------------- | --------------------- |
| `Trigger`                                   | 열기용 트리거 (버튼 등)        |
| `Content`                                   | 팝업/드롭다운/모달 내부 콘텐츠     |
| `Item`                                      | 선택 가능한 항목             |
| `Label`                                     | 그룹명 또는 텍스트 라벨         |
| `Description`                               | 부가 설명 텍스트             |
| `Title`                                     | 제목 역할                 |
| `Header` / `Footer`                         | 상단 / 하단 구조 구분         |
| `Close`                                     | 닫기용 트리거               |
| `Cancel` / `Action`                         | 하단 액션 버튼              |
| `Input`                                     | 검색어나 필터용 입력           |
| `Group`                                     | 항목 묶음                 |
| `Separator`                                 | 시각적 구분선               |
| `RadioGroup` / `RadioItem` / `CheckboxItem` | 단일/다중 선택 항목           |
| `Shortcut`                                  | 키보드 단축키 표시            |
| `Sub` / `SubTrigger` / `SubContent`         | 서브 메뉴 구성 요소           |
| `Empty`                                     | 결과 없음 안내              |
| `Portal`                                    | body 외부에 콘텐츠 렌더링      |
| `Image` / `Fallback`                        | 비주얼 요소 또는 실패 시 대체 콘텐츠 |
| `List` / `Page` / `Next` / `Previous`       | 리스트/페이징 관련 요소         |



## 📘 Radix UI 스타일 옵션 명명 규칙
### 1. 콜백 함수 (onX, onXChange)
| 유형     | 콜백 명              | 설명                            |
| ------ | ----------------- | ----------------------------- |
| 상태 변경  | `onOpenChange`    | 열림/닫힘 상태 변경                   |
|        | `onValueChange`   | 선택 값 변경                       |
|        | `onCheckedChange` | 체크 상태 변경 (Checkbox, Switch 등) |
| 입력 처리  | `onInputChange`   | 입력 필드 값 변경                    |
|        | `onSearch`        | 검색 버튼 클릭 또는 엔터 입력 등 검색 트리거    |
| 페이지 요청 | `onLoadMore`      | 스크롤 하단 도달, "더보기" 버튼, 페이지네이션 등 |
| 액션 처리  | `onSelect`        | 항목 선택 시                       |
|        | `onSubmit`        | 제출 시                          |
|        | `onCancel`, `onClear` | 취소, 초기화 등 명시적 액션              |
| 외부 이벤트 | `onEscapeKeyDown` | ESC 키 입력 시                    |
|        | `onInteractOutside` | 외부 클릭 또는 포커스 이동 등 인터랙션 감지     |


## 2. 옵션 prop
### 명명규칙 :
| 용도    | 예시                      |
| ----- | ----------------------- |
| 값 제어  | `value`, `defaultValue` |
| 열림 제어 | `open`, `defaultOpen`   |
| 기타 상태 | `disabled`, `multiple`  |
