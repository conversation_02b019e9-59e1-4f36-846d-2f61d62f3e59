"use client"; // 클라이언트 컴포넌트임을 명시 (React 18 이상)

import { useMapActions } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@geon-ui/react/primitives/hover-card";
import { MapIcon } from "lucide-react";
import * as React from "react";
// 1. 타입 정의
export type BasemapInfo = {
  bcrnMapClCodeNm: string;
  bcrnMapNm: string;
  base64: string;
  mapUrl: string;
  lyrStleCodeNm: string;
  mapUrlparamtr: string;
};

// 2. 전체 매핑 타입 정의 (선택적으로 쓰고 싶을 때는 Partial로도 가능)
export type BasemapMapType = BasemapInfo[];

// 3. DEFAULT_BASE_MAPS 선언
export const DEFAULT_BASE_MAPS: BasemapMapType = [
  {
    bcrnMapClCodeNm: "바로e맵",
    bcrnMapNm: "백지도",
    base64:
      "data:image/png;base64,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***************************************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",
    mapUrl: "https://city.geon.kr/api/map/api/map/baroemap",
    mapUrlparamtr:
      '{\n      "projection": "EPSG:5179",\n      "version": "1.0.0",\n      "format": "image/png",\n      "request": "GetTile",\n      "layer": "white_map",\n      "style": "korean",\n      "tileGrid": {\n        "origin": [-200000.0, 4000000.0],\n        "resolutions": [2088.96, 1044.48, 522.24, 261.12, 130.56, 65.28, 32.64, 16.32, 8.16, 4.08, 2.04, 1.02, 0.51, 0.255, 0.1275, 0.06375],\n        "matrixIds": ["L05", "L06", "L07", "L08", "L09", "L10", "L11", "L12", "L13", "L14", "L15", "L16", "L17", "L18", "L19", "L20"]\n      }\n}',
    lyrStleCodeNm: "WMTS",
  },
  {
    bcrnMapClCodeNm: "시계열영상",
    bcrnMapNm: "최신",
    base64:
      "data:image/png;base64,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",
    mapUrl: "https://city.geon.kr/api/map/api/map/ngisair",
    lyrStleCodeNm: "WMTS",
    mapUrlparamtr:
      '{\n      "projection": "EPSG:5179",\n      "version": "1.0.0",\n      "format": "image/jpg",\n      "request": "GetTile",\n      "layer": "AIRPHOTO",\n      "style": "_null",\n      "tilematrixset":"NGIS_AIR",\n      "tileGrid": {\n        "origin": [-200000.0, 4000000.0],\n        "resolutions": [2088.96, 1044.48, 522.24, 261.12, 130.56, 65.28, 32.64, 16.32, 8.16, 4.08, 2.04, 1.02, 0.51, 0.255, 0.1275, 0.06375],\n        "matrixIds": ["5", "6", "7", "8", "9", "10", "11", "12", "13", "14", "15", "16", "17", "18", "19", "20"]\n      }\n}',
  },
];

// BasemapContext: 현재 선택된 배경지도 상태를 하위 컴포넌트에 공유하기 위한 컨텍스트
const BasemapContext = React.createContext<{
  basemapInfo?: BasemapInfo;
  onValueChange?: (value: BasemapInfo) => void;
} | null>(null);

// BasemapContext를 사용하는 훅
function useBasemapContext() {
  const context = React.useContext(BasemapContext);
  if (!context) {
    throw new Error("Basemap 컴포넌트 내부에서만 사용할 수 있습니다.");
  }
  return context;
}

// Basemap 컴포넌트 - 배경지도 선택 기능의 루트
export interface BasemapProps extends React.ComponentPropsWithoutRef<"div"> {
  /**
   * 현재 선택된 배경지도 ID
   * @default 'eMapBasic'
   */
  basemapInfo?: BasemapInfo;
  /**
   * 배경지도가 변경될 때 호출되는 함수
   */

  onValueChange?: (value: BasemapInfo) => void;
}

export function Basemap({
  basemapInfo,
  onValueChange,
  className,
  children,
  ...props
}: BasemapProps) {
  // ✅ Store Actions 패턴 사용 (Core 인스턴스 직접 접근 X)
  const { setBasemap, setBasemapInfo } = useMapActions();

  const handleValueChange = React.useCallback(
    (_basemapInfo: BasemapInfo) => {
      console.log("basemapInfo", _basemapInfo);
      // ✅ Store Actions 호출 (Core 직접 접근 대신)
      setBasemapInfo(_basemapInfo);
      onValueChange?.(_basemapInfo); // 외부 콜백 호출
    },
    [setBasemapInfo, onValueChange],
  );

  React.useEffect(() => {
    if (basemapInfo) {
      // ✅ Store Actions 호출
      setBasemapInfo(basemapInfo);
    }
  }, [basemapInfo, setBasemapInfo]);

  return (
    <BasemapContext.Provider
      value={{ basemapInfo, onValueChange: handleValueChange }}
    >
      <div className={cn("inline-flex", className)} {...props}>
        <HoverCard openDelay={100}>
          {children} {/* Trigger + Content 등 포함됨 */}
        </HoverCard>
      </div>
    </BasemapContext.Provider>
  );
}

// BasemapTrigger: 사용자 입력을 받아 HoverCard를 열어주는 버튼 역할

export function BasemapTrigger({
  className,
  children,
  ...props
}: React.ComponentPropsWithoutRef<"button">) {
  return (
    <HoverCardTrigger asChild>
      <Button className={cn(className)} {...props}>
        {children}
      </Button>
    </HoverCardTrigger>
  );
}

// BasemapContent: HoverCard에 표시될 배경지도 선택 영역 (HoverCard의 본문)
export function BasemapContent({
  className,
  children,
  ...props
}: React.ComponentPropsWithoutRef<typeof HoverCardContent>) {
  return (
    <HoverCardContent
      className={cn(
        "flex flex-col gap-0.5 p-0.5 bg-background/90 backdrop-blur-md border shadow-lg rounded-xl",
        className,
      )}
      align="end"
      side="left"
      alignOffset={-40}
      sideOffset={8}
      {...props}
    >
      {children} {/* Item 목록 들어감 */}
    </HoverCardContent>
  );
}
// BasemapItem: 선택 가능한 개별 배경지도 항목 버튼
export interface BasemapItemProps
  extends React.ComponentPropsWithoutRef<"button"> {
  basemapInfo: BasemapInfo; // 해당 아이템이 나타내는 배경지도 ID
}
// BasemapItem: 배경지도 항목 하나 (선택하면 onValueChange 발생)
export function BasemapItem({
  basemapInfo,
  className,
  children,
  ...props
}: BasemapItemProps) {
  const { basemapInfo: selectedValue, onValueChange } = useBasemapContext();
  console.log(selectedValue);
  console.log(onValueChange);
  //const isSelected = value === selectedValue;
  console.log(basemapInfo);
  return (
    <Button
      variant="ghost"
      size="sm"
      className={cn(
        "w-full py-3 justify-start text-sm relative group transition-all duration-200",
        //isSelected ? "bg-muted" : "hover:bg-muted/50",
        className,
      )}
      onClick={() => onValueChange?.(basemapInfo)} // 클릭 시 선택 값 변경
      {...props}
    >
      {/* 선택된 경우 좌측에 색 바 표시 */}
      {/* {isSelected && (
        <div
          className="absolute left-0 top-0 h-full w-1 rounded-full transition-opacity duration-200"
          style={{ backgroundColor: color }}
        />
      )} */}
      {children}
    </Button>
  );
}

// Props 정의
export interface BasemapWidgetProps {
  selectBasemap?: BasemapInfo;
  onValueChange?: (value: BasemapInfo) => void;
  className?: string;
  baseMaps?: BasemapMapType;
}
//편리하게 사용할 수 있는 위젯
export function BasemapWidget({
  selectBasemap = DEFAULT_BASE_MAPS[0],
  onValueChange,
  className,
  baseMaps = DEFAULT_BASE_MAPS,
}: BasemapWidgetProps) {
  return (
    <Basemap
      basemapInfo={selectBasemap}
      onValueChange={onValueChange}
      className={cn("absolute right-4 top-4 flex flex-col gap-2", className)}
    >
      <BasemapTrigger className="bg-white/70 hover:bg-white dark:bg-zinc-800/90 dark:hover:bg-zinc-800">
        <MapIcon />
      </BasemapTrigger>
      <BasemapContent>
        {baseMaps.map((map, index) => (
          <BasemapItem key={index} basemapInfo={map}>
            <div
              className={cn(
                "flex items-center gap-3 transition-colors duration-200",
                // controlledValue === index
                //   ? "bg-white/80 hover:bg-white"
                //   : "bg-white/60 hover:bg-white",
              )}
            >
              <div className="rounded-lg p-1.5">
                <img
                  src={map["base64"]}
                  alt={map.bcrnMapNm}
                  className="w-[50px] h-[50px] object-contain"
                />
              </div>
              <div className="flex flex-col items-start gap-0.5">
                <span className="font-medium">{map.bcrnMapNm}</span>
                <span className="text-[11px] leading-tight">
                  {map.bcrnMapClCodeNm}
                </span>
              </div>
            </div>
          </BasemapItem>
        ))}
      </BasemapContent>
    </Basemap>
  );
}

// 별칭 export
export const Root = Basemap;
export const Trigger = BasemapTrigger;
export const Content = BasemapContent;
export const Item = BasemapItem;
export const Widget = BasemapWidget;
