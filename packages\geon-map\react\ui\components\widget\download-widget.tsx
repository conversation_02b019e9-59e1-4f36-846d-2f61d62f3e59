"use client";

import { useDownload } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@geon-ui/react/primitives/hover-card";
import { Download as DownloadIcon, FileDown, ImageDown } from "lucide-react";
import React, { useContext } from "react";

export type DownloadType = "png" | "pdf";

// ✅ Context 생성
interface DownloadContextValue {
  downloadPDF: () => void;
  downloadImage: () => void;
}

const DownloadContext = React.createContext<DownloadContextValue | null>(null);

// ✅ Context Hook
const useDownloadContext = () => {
  const context = useContext(DownloadContext);
  if (!context) {
    throw new Error("useDownloadContext must be used within Download");
  }
  return context;
};

export interface DownloadWidgetProps extends DownloadProps {}
export interface DownloadProps extends React.HTMLAttributes<HTMLDivElement> {}
export interface DownloadTriggerProps
  extends React.HTMLAttributes<HTMLButtonElement> {}
export interface DownloadContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}
export interface DownloadItemProps
  extends React.HTMLAttributes<HTMLButtonElement> {
  downloadType: DownloadType;
}

export const DownloadTrigger = ({
  className,
  children,
  ...props
}: DownloadTriggerProps) => {
  return (
    <HoverCardTrigger asChild>
      <Button
        className={cn(
          "cursor-pointer bg-white text-gray-600 hover:bg-white hover:text-black opacity-80",
          className,
        )}
        {...props}
      >
        {children}
      </Button>
    </HoverCardTrigger>
  );
};

export const DownloadContent = ({
  className,
  children,
  ...props
}: DownloadContentProps) => {
  return (
    <HoverCardContent
      className={cn(
        "flex flex-row p-1 gap-3 bg-background/90 backdrop-blur-md border shadow-lg rounded-md w-fit h-auto items-center justify-center",
        className,
      )}
      align="center"
      side="left"
      alignOffset={-40}
      sideOffset={8}
      {...props}
    >
      {children}
    </HoverCardContent>
  );
};

export const DownloadItem = ({
  className,
  children,
  downloadType,
  ...props
}: DownloadItemProps) => {
  const { downloadPDF, downloadImage } = useDownloadContext();

  const downloadMap = () => {
    switch (downloadType) {
      case "png":
        downloadImage();
        break;
      case "pdf":
        downloadPDF();
        break;
      default:
        console.error("downloadType is not defined");
        break;
    }
  };

  return (
    <button
      className={cn(
        "flex flex-col items-center justify-center gap-1 px-3 py-2 min-w-12 h-auto text-xs font-medium transition-all duration-200 text-gray-600 hover:text-black hover:bg-white rounded-md cursor-pointer",
        className,
      )}
      {...props}
      onClick={downloadMap}
    >
      {children}
    </button>
  );
};

export const Download = ({ className, children, ...props }: DownloadProps) => {
  const { downloadPDF, downloadImage } = useDownload();

  return (
    <div
      className={cn("absolute right-4 top-35 flex flex-col gap-2", className)}
      {...props}
    >
      <DownloadContext.Provider value={{ downloadPDF, downloadImage }}>
        <HoverCard>{children}</HoverCard>
      </DownloadContext.Provider>
    </div>
  );
};

export const DownloadWidget = (options: DownloadWidgetProps) => {
  return (
    <Download {...options}>
      <DownloadTrigger>
        <DownloadIcon className="w-5 h-5" />
      </DownloadTrigger>
      <DownloadContent>
        <DownloadItem downloadType="png">
          <ImageDown className="w-5 h-5" />
          <span>PNG</span>
        </DownloadItem>
        <DownloadItem downloadType="pdf">
          <FileDown className="w-5 h-5" />
          <span>PDF</span>
        </DownloadItem>
      </DownloadContent>
    </Download>
  );
};
