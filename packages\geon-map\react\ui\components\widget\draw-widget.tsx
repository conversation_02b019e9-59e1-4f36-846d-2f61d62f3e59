// "use client"; // 클라이언트 컴포넌트임을 명시 (React 18 이상)

// import { cn } from "@geon-ui/react/lib/utils";
// import { HoverCard } from "@geon-ui/react/primitives/hover-card";
// import * as React from "react";

// import { useMapContext } from "../../../odf/contexts";
// import { DrawId } from "../../../odf/types/draw-types";

// // DrawContext: 현재 선택된 그리기 도구 상태를 하위 컴포넌트에 공유하기 위한 컨텍스트
// const DrawContext = React.createContext<{
//   value?: DrawId;
//   onValueChange?: (value: DrawId) => void;
// } | null>(null);

// // DrawContext를 사용하는 훅

// // Draw 컴포넌트 - 그리기 도구 선택 기능의 루트
// export interface DrawProps extends React.ComponentPropsWithoutRef<"div"> {
//   value?: DrawId;
//   /**
//    * 현재 선택된 그리기 도구 ID
//    * @default 'circle'
//    */
//   /**
//    * 그리기 도구가 변경될 때 호출되는 함수
//    */
//   onValueChange?: (value: DrawId) => void;
// }

// export function Draw({
//   value = "circle",
//   onValueChange,
//   className,
//   children,
//   ...props
// }: DrawProps) {
//   const { view, control } = useMapContext(); // ODF 지도 컨텍스트 사용

//   const handleValueChange = React.useCallback(
//     (drawId: DrawId) => {
//       if (!view) return;
//       control.draw(drawId);
//       onValueChange?.(drawId);
//     },
//     [view, onValueChange],
//   );

//   return (
//     <DrawContext.Provider value={{ value, onValueChange: handleValueChange }}>
//       <div className={cn("inline-flex", className)} {...props}>
//         <HoverCard openDelay={100}>
//           {children} {/* Trigger + Content 등 포함됨 */}
//         </HoverCard>
//       </div>
//     </DrawContext.Provider>
//   );
// }
