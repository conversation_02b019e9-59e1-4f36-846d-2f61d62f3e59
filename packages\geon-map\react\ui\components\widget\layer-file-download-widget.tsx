"use-client";
import { cn } from "@geon-ui/react/lib/utils";
import { Button } from "@geon-ui/react/primitives/button";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@geon-ui/react/primitives/hover-card";
import {
  Download as DownloadIcon,
  FileArchive,
  FileCode2,
  FileJson2,
  FileSpreadsheet,
  Shapes,
} from "lucide-react";
import React from "react";
import { useMemo } from "react";

import {
  LayerFileDownloadContext,
  useLayerFileDownloadContext,
} from "../../contexts";
import {
  LayerFileDownloadCallback,
  LayerFileDownloadContentProps,
  LayerFileDownloadContextValue,
  LayerFileDownloadItemProps,
  LayerFileDownloadParams,
  LayerFileDownloadProps,
  LayerFileDownloadTriggerProps,
  LayerFileDownloadType,
  LayerFileDownloadWidgetProps,
} from "../../types/layer-file-download-types";

/** ### content-disposition 헤더 문자열에서 파일명을 추출하는 헬퍼함수
 * ---
 * - 현재 서버에서 노출 시킨 헤더는 content-length 뿐이라 content-disposition 접근 불가
 * @Todo headers.get("content-disposition") 가능하도록 변경필요(API 단).
 * @param headers Response Header
 * @returns 파일 이름 문자열 또는 null
 */
const getFilenameFromHeader = (headers: Headers) => {
  const contentDisposition = headers.get("content-disposition");

  if (!contentDisposition) {
    return null;
  }

  // "filename=" 또는 "filename*=" 패턴 추출을 위한 정규식
  const filenameMatch =
    /filename\*?=['"]?(?:UTF-\d{1,2}''|..)?([^'";\r\n]*)/.exec(
      contentDisposition,
    );
  if (filenameMatch && filenameMatch[1]) {
    try {
      // URL 인코딩된 파일명 디코딩
      return decodeURIComponent(filenameMatch[1]);
    } catch (error) {
      console.error("Layer Filename decoding failed", error);
      return filenameMatch[1];
    }
  }

  return null;
};

/**
 * ### 레이어 파일 다운로드 헬퍼함수
 * ---
 * @param layerFileDownloadType 레이어 파일 다운로드 타입 (e.g. "csv", "shape", "kml", "geojson", "kmz")
 * @param param Context 내 LayerFileDownloadRequest
 * @param onClickLayerFileDownload Context 내 LayerFileDownloadCallback 함수(use-layer-file-download.ts 의 handleLayerFileDownload)
 * @return 레이어 파일(e.g. '.csv', '.zip', '.geojson', '.kml', '.kmz')
 */
const downloadLayerFile = async (
  layerFileDownloadType: LayerFileDownloadType,
  params: LayerFileDownloadParams,
  onClickLayerFileDownload: LayerFileDownloadCallback | undefined,
) => {
  if (!onClickLayerFileDownload || !params || !layerFileDownloadType) {
    console.error("Download function called with invalid arguments.", {
      layerFileDownloadType,
      params,
      onClickLayerFileDownload,
    });
    return;
  }

  try {
    params.outputFormat = layerFileDownloadType;
    const result = await onClickLayerFileDownload?.(params);

    if (result && result.blob instanceof Blob) {
      const { blob, headers } = result;
      const baseFilename = getFilenameFromHeader(headers) || "layer-file";
      //const fileFormat = getFileFormatFromType(layerFileDownloadType);
      const filename = baseFilename;

      // 메모리에 있는 Blob 데이터를 가리키는 임시 URL을 생성
      const url = window.URL.createObjectURL(blob);

      // 다운로드를 실행을 위한 보이지 않는 <a> 태그 생성
      const link = document.createElement("a");
      link.href = url;

      // download 속성에 파일 이름 지정
      link.setAttribute("download", filename);

      // <a> 태그를 추가하고 강제로 클릭하여 다운로드
      document.body.appendChild(link);
      link.click();

      // 다운로드 시작 후, 임시 태그와 URL 삭제
      link.remove();
      window.URL.revokeObjectURL(url);
    } else {
      // Blob 아닌 다른 값 방어
      console.warn(
        "Download function did not return a valid Blob object.",
        result,
      );
    }
  } catch (error) {
    console.error("Layer file download failed: ", error);
  }
};

// 컴포넌트들
export const LayerFileDownloadTrigger = React.forwardRef<
  HTMLButtonElement,
  LayerFileDownloadTriggerProps
>(({ buttonName, className, children, ...props }, ref) => {
  return (
    <HoverCardTrigger asChild>
      <Button
        ref={ref}
        className={cn(
          "cursor-pointer bg-white text-black hover:bg-black hover:text-white opacity-80 border h-10",
          className,
        )}
        {...props}
      >
        {buttonName}
        {children}
      </Button>
    </HoverCardTrigger>
  );
});
LayerFileDownloadTrigger.displayName = "LayerFileDownloadTrigger";

export const LayerFileDownloadContent = React.forwardRef<
  React.ComponentRef<typeof HoverCardContent>,
  LayerFileDownloadContentProps
>(({ className, children, ...props }, ref) => {
  return (
    <HoverCardContent
      ref={ref}
      className={cn(
        "flex flex-row p-1 gap-3 bg-background/90 backdrop-blur-md border shadow-lg rounded-md w-fit h-auto items-center justify-center",
        className,
      )}
      align="center"
      side="bottom"
      alignOffset={-40}
      sideOffset={1}
      {...props}
    >
      {children}
    </HoverCardContent>
  );
});
LayerFileDownloadContent.displayName = "LayerFileDownloadContent";

export const LayerFileDownloadItem = React.forwardRef<
  HTMLButtonElement,
  LayerFileDownloadItemProps
>(({ className, children, layerFileDownloadType, ...props }, ref) => {
  const { layerFileDownloadInfo, onClickLayerFileDownload } =
    useLayerFileDownloadContext();

  const download = () => {
    if (layerFileDownloadInfo) {
      downloadLayerFile(
        layerFileDownloadType,
        layerFileDownloadInfo,
        onClickLayerFileDownload,
      );
    } else {
      console.error("Information for the layer file to download is missing.");
    }
  };

  return (
    <button
      ref={ref}
      className={cn(
        "flex flex-col items-center justify-center gap-1 px-3 py-2 min-w-12 h-auto text-xs font-medium transition-all duration-200 hover:bg-gray-100 rounded-md cursor-pointer",
        className,
      )}
      onClick={() => download()}
      {...props}
    >
      {children}
    </button>
  );
});
LayerFileDownloadItem.displayName = "LayerFileDownloadItem";

export const LayerFileDownload = React.forwardRef<
  HTMLDivElement,
  LayerFileDownloadProps
>(
  (
    {
      className,
      children,
      layerFileDownloadInfo,
      onClickLayerFileDownload,
      isLoading,
      ...props
    },
    ref,
  ) => {
    const contextValue: LayerFileDownloadContextValue = useMemo(
      () => ({
        isLoading,
        layerFileDownloadInfo,
        onClickLayerFileDownload,
      }),
      [isLoading, layerFileDownloadInfo, onClickLayerFileDownload],
    );

    return (
      <div ref={ref} className={cn("flex", className)} {...props}>
        <LayerFileDownloadContext.Provider value={contextValue}>
          <HoverCard>{children}</HoverCard>
        </LayerFileDownloadContext.Provider>
      </div>
    );
  },
);
LayerFileDownload.displayName = "LayerFileDownload";

// 완성형 위젯 컴포넌트
export const LayerFileDownloadWidget = ({
  layerFileDownloadInfo,
  onClickLayerFileDownload,
  isLoading,
  className,
  buttonName,
}: LayerFileDownloadWidgetProps) => {
  return (
    <LayerFileDownload
      layerFileDownloadInfo={layerFileDownloadInfo}
      onClickLayerFileDownload={onClickLayerFileDownload}
      isLoading={isLoading}
      className={className}
    >
      <LayerFileDownloadTrigger buttonName={buttonName}>
        <DownloadIcon className="w-5 h-5" />
      </LayerFileDownloadTrigger>
      <LayerFileDownloadContent>
        <LayerFileDownloadItem layerFileDownloadType="csv">
          <FileSpreadsheet className="w-5 h-5 text-green-500" />
          <span className="text-green-500">CSV</span>
        </LayerFileDownloadItem>
        <LayerFileDownloadItem layerFileDownloadType="shape">
          <Shapes className="w-5 h-5 text-sky-600" />
          <span className="text-sky-600">SHP</span>
        </LayerFileDownloadItem>
        <LayerFileDownloadItem layerFileDownloadType="geojson">
          <FileJson2 className="w-5 h-5 text-teal-500" />
          <span className="text-teal-500">GEOJSON</span>
        </LayerFileDownloadItem>
        <LayerFileDownloadItem layerFileDownloadType="kml">
          <FileCode2 className="w-5 h-5 text-red-600" />
          <span className="text-red-600">KML</span>
        </LayerFileDownloadItem>
        <LayerFileDownloadItem layerFileDownloadType="kmz">
          <FileArchive className="w-5 h-5 text-yellow-600" />
          <span className="text-yellow-600">KMZ</span>
        </LayerFileDownloadItem>
      </LayerFileDownloadContent>
    </LayerFileDownload>
  );
};
LayerFileDownloadWidget.displayName = "LayerFileDownloadWidget";
