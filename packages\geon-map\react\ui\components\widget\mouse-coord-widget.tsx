"use client";

import { cn } from "@geon-ui/react/lib/utils";
import { Label } from "@geon-ui/react/primitives/label";
import { Switch } from "@geon-ui/react/primitives/switch";
import { useState } from "react";

interface MouseCoordWidgetProps {
  className?: string;
}

export const MouseCoordWidget = ({ className }: MouseCoordWidgetProps) => {
  const [enabled] = useState(false);
  // const coordinate = useMouseCoordinate(enabled);

  return (
    <div
      className={cn(
        "absolute bottom-4 left-1/2 -translate-x-1/2 z-50 w-fit rounded-lg border p-4 shadow-sm bg-white text-sm",
        className,
      )}
    >
      <div className="flex flex-col gap-3">
        <div className="flex items-center gap-3">
          <Label htmlFor="coord-switch" className="text-sm font-medium">
            좌표 표시
          </Label>
          <Switch
            id="coord-switch"
            checked={enabled}
            // onCheckedChange={setEnabled}
          />
        </div>

        {enabled && (
          <div className="grid grid-cols-2 gap-2 text-gray-700">
            <div className="flex flex-col items-center">
              <span className="font-bold">X</span>
              <span className="min-w-[140px] text-center tabular-nums">
                {/* {coordinate?.x?.toFixed(8) ?? "-"} */}
              </span>
            </div>
            <div className="flex flex-col items-center">
              <span className="font-bold">Y</span>
              <span className="min-w-[140px] text-center tabular-nums">
                {/* {coordinate?.y?.toFixed(8) ?? "-"} */}
              </span>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
