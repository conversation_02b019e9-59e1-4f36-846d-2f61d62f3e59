"use client";

import { OverviewMapPositionType } from "@geon-map/core";
import { useOverview } from "@geon-map/react-odf";
import { cn } from "@geon-ui/react/lib/utils";
import { Label } from "@geon-ui/react/primitives/label";
import { Switch } from "@geon-ui/react/primitives/switch";

interface OverviewWidgetProps {
  className?: string;
  position?: OverviewMapPositionType;
}

export const OverviewWidget = ({
  className,
  position = "left-down",
}: OverviewWidgetProps) => {
  const { enabled, setEnabled } = useOverview(position);

  return (
    <div
      className={cn(
        "absolute bottom-4 left-1/10 -translate-x-1/2 z-50 w-fit rounded-lg border p-4 shadow-sm bg-white text-sm",
        className,
      )}
    >
      <div className="flex items-center gap-3">
        <Label htmlFor="overview-switch" className="text-sm font-medium">
          인덱스 맵
        </Label>
        <Switch
          id="overview-switch"
          checked={enabled}
          onCheckedChange={setEnabled}
        />
      </div>
    </div>
  );
};
