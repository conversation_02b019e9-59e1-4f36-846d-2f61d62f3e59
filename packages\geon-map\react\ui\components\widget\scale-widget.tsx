"use client";
import { useScale } from "@geon-map/react-odf";
import { Input } from "@geon-ui/react/primitives/input";

export const ScaleWidget = ({ className }: { className?: string }) => {
  const currentScale = useScale();

  return (
    <div
      className={`absolute bottom-4 right-50 z-50 bg-white border rounded-md shadow-md px-3 py-2 text-sm ${className}`}
    >
      <Input
        readOnly
        value={currentScale}
        className="w-28 text-center bg-gray-50"
      />
    </div>
  );
};
