import { DrawingMode, execute } from "@geon-map/core";
import {
  useDraw,
  useEvent,
  useLayer,
  useMapInstanceRequired,
} from "@geon-map/react-odf";
import { crtfckey, WMS_URL } from "@geon-query/model";
import { useRef, useState } from "react";
const drawingTools = [
  {
    id: "lineString",
    label: "직선",
    emoji: "📏",
    className: "bg-blue-50 hover:bg-blue-100 border-blue-200 text-blue-700",
  },
  {
    id: "polygon",
    label: "다각형",
    emoji: "⬟",
    className: "bg-green-50 hover:bg-green-100 border-green-200 text-green-700",
  },
  {
    id: "point",
    label: "점",
    emoji: "📍",
    className:
      "bg-purple-50 hover:bg-purple-100 border-purple-200 text-purple-700",
  },
  {
    id: "circle",
    label: "원",
    emoji: "⭕",
    className:
      "bg-orange-50 hover:bg-orange-100 border-orange-200 text-orange-700",
  },
];

export function SpatailSearchWidget() {
  // 새로운 기능들을 위해 useDraw 훅 사용 (Store Actions 패턴)
  const {
    stopDrawing,
    drawControl,
    // clearAllFeatures,
    // selectedFeatures,
    drawMode,
    isDrawing,
    startDrawing,
  } = useDraw();
  const { addLayer, findLayer } = useLayer();
  const { registerListener } = useEvent();
  const { map } = useMapInstanceRequired();
  const [targetLayerId, setTargetLayerId] = useState<any>(null);
  const cleanupRef = useRef<(() => void) | null>(null);

  // 기존 인터페이스와 호환성을 위한 변환
  const features = []; // 선택된 피처들을 features로 매핑
  const drawingMode = drawMode;

  // drawend 이벤트 처리 함수
  const handleDrawEnd = async (feature: any) => {
    try {
      console.log("feature", feature);
      console.log("center", feature.getCenterPoint());

      const projection = map.getProjection();
      // TODO: 레이어 찾기 로직 구현 필요 (findLayer 대체)
      const layer = findLayer(targetLayerId)!.odfLayer;
      const epsg = layer.getInitialOption().params.projection?.split(":")[1];
      const clone = feature.clone();
      const unproject = projection.unprojectGeom(clone, epsg);
      console.log("unproject", unproject);
      console.log("center", unproject.getCenterPoint());

      // 임시로 기본 처리
      console.log("그리기 완료된 feature:", feature);
      console.log("targetLayerId:", targetLayerId);

      // TODO: execute 함수 구현 필요
      const { result, odfFeatureList } = await execute(layer, {
        feature: unproject,
      });
      console.log("result", result);
      console.log("odfFeatureList", odfFeatureList);
    } catch (error) {
      console.error("drawend 처리 중 오류:", error);
    }
  };

  const add = async () => {
    const layerId = await addLayer({
      type: "geoserver",
      server: {
        url: WMS_URL,
      },
      layer: "muan_gis:2017_2018_n1a_h0010000",
      info: {
        lyrId: "2017_2018_n1a_h0010000",
        lyrNm: "2017_2018_n1a_h0010000",
      },
      crtfckey,
      service: "wms",
      method: "post",
      visible: true,
    });
    setTargetLayerId(layerId);
  };
  // drawend 이벤트 등록/해제
  const registerDrawEndListener = () => {
    // 기존 리스너 정리
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = null;
    }

    // drawControl 또는 map에 drawend 등록
    const target = (drawControl as any) || map;
    if (!target) return;

    cleanupRef.current = registerListener(
      target,
      "drawend",
      (evt: any) => {
        const feature = evt?.feature || evt?.target || evt;
        handleDrawEnd(feature);
      },
      { listenerId: "spatial_search_drawend" },
    );
  };

  const handleToolClick = (toolId: DrawingMode) => {
    if (drawingMode === toolId) {
      stopDrawing();
      // 그리기 중단 시 리스너 정리
      if (cleanupRef.current) {
        cleanupRef.current();
        cleanupRef.current = null;
      }
    } else {
      // ✅ Store Actions 패턴 사용 + drawend 이벤트 등록
      startDrawing(toolId as string);

      // drawend 이벤트 리스너 등록
      registerDrawEndListener();
    }
  };

  return (
    <div className="absolute right-20 top-20 z-10 w-64">
      <div className="rounded-lg border bg-white shadow-lg">
        <div className="border-b p-4">
          <div className="flex items-center justify-between">
            <h3 className="font-semibold text-gray-900">공간검색</h3>
            <div className="flex items-center gap-2">
              {isDrawing && (
                <div className="flex items-center gap-1">
                  <div className="h-2 w-2 animate-pulse rounded-full bg-green-500"></div>
                  <span className="text-xs text-green-600">
                    {drawingMode?.startsWith("measure-")
                      ? "측정 중"
                      : "그리기 중"}
                  </span>
                </div>
              )}
            </div>
          </div>
        </div>

        <div className="p-4">
          {/* 그리기 도구 버튼들 */}
          <div className="mb-4">
            <h4 className="mb-2 text-sm font-medium text-gray-700">
              검색 도구
            </h4>
            <div className="grid grid-cols-2 gap-2">
              <button
                onClick={add}
                className={`flex items-center gap-2 rounded-md border px-3 py-2 text-sm font-medium transition-colors 
                `}
              >
                레이어 추가
              </button>
              {drawingTools.map((tool) => (
                <button
                  key={tool.id}
                  onClick={() => handleToolClick(tool.id as DrawingMode)}
                  className={`flex items-center gap-2 rounded-md border px-3 py-2 text-sm font-medium transition-colors ${
                    drawingMode === tool.id
                      ? "ring-2 ring-blue-500 ring-offset-1"
                      : ""
                  } ${tool.className} `}
                >
                  <span className="text-base">{tool.emoji}</span>
                  {tool.label}
                  {drawingMode === tool.id && (
                    <span className="ml-auto text-xs">●</span>
                  )}
                </button>
              ))}
            </div>
          </div>
          {/* 액션 버튼들 */}
          <div className="space-y-2">
            <button
              // onClick={clearAllFeatures}
              disabled={features.length === 0}
              className="w-full rounded-md bg-red-50 px-3 py-2 text-sm font-medium text-red-700 hover:bg-red-100 disabled:cursor-not-allowed disabled:opacity-50"
            >
              🗑️ 초기화
            </button>
          </div>
        </div>
      </div>

      {/* 컨텍스트 메뉴 - 이제 헤드리스 방식으로 자동으로 상태를 가져옴 */}
    </div>
  );
}
