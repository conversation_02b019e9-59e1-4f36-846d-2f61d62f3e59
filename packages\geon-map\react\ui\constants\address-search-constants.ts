// 상수 정의
export const ADDRESS_SEARCH_OPTIONS = {
  integrated: "통합 검색",
  jibun: "지번 검색",
  road: "도로명주소 검색",
  roadApi: "도로명주소 API 검색",
  building: "건물명 검색",
  coordinates: "경위도 좌표 검색",
  postalCode: "기초구역번호 검색",
  pnu: "PNU 검색",
  poi: "POI 검색",
} as const;

// 검색 타입별 필드 매핑
export const SEARCH_TYPE_FIELDS = {
  integrated: [
    {
      key: "integrated",
      value: "",
      placeholder: "장소 또는 주소를 입력하세요",
    },
  ],
  jibun: [{ key: "jibun", value: "", placeholder: "지번주소를 입력하세요" }],
  road: [{ key: "road", value: "", placeholder: "도로명주소를 입력하세요" }],
  roadApi: [
    { key: "roadApi", value: "", placeholder: "도로명주소를 입력하세요" },
  ],
  building: [
    { key: "building", value: "", placeholder: "건물명을 입력하세요" },
  ],
  coordinates: [
    { key: "lat", value: "", placeholder: "위도를 입력하세요.(예: 37.5665)" },
    { key: "lng", value: "", placeholder: "경도를 입력하세요.(예: 126.9780)" },
  ],
  postalCode: [
    { key: "postalCode", value: "", placeholder: "기초구역번호를 입력하세요" },
  ],
  pnu: [{ key: "pnu", value: "", placeholder: "PNU를 입력하세요" }],
  poi: [{ key: "poi", value: "", placeholder: "POI명을 입력하세요" }],
};
