import { RegionType } from "../types/region-selector-types";

//지역간 계층 (예: 시도가 변경되면 시군구값 다시 셋팅 필요, 초기화 되야할 값은 읍면동,리)
export const REGION_HIERARCHY = {
  sido: {
    next: "sigungu" as const,
    reset: ["eupmyeondong", "li"] as RegionType[],
  },
  sigungu: {
    next: "eupmyeondong" as const,
    reset: ["li"] as RegionType[],
  },
  eupmyeondong: {
    next: "li" as const,
    reset: null,
  },
  li: {
    next: null,
    reset: null,
  },
} as const;

// 상위 지역 매핑
export const PARENT_REGION_MAP = {
  sido: undefined,
  sigungu: "sido" as const,
  eupmyeondong: "sigungu" as const,
  li: "eupmyeondong" as const,
} as const;
