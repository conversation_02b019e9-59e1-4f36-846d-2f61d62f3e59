import { createContext, useContext } from "react";

import {
  AddressBase,
  AddressSearchField,
  AddressSearchTypes,
  ItemClickCallback,
  PagingType,
  SearchCallback,
  SearchTypesOptions,
  VisibleFieldsOptions,
} from "../types";

// Context 상태 관리 인터페이스
export interface AddressSearchState {
  // 검색 관련 상태
  searchType: AddressSearchTypes;
  fields: AddressSearchField[];
  searchResults: AddressBase[];
  currentPage: number;
  currentKeyword: string;
  hasNextPage: boolean;

  // UI 상태
  isOpen: boolean;
  isLoading?: boolean;
  isFetching: boolean;
}

// Context 액션 인터페이스
export interface AddressSearchActions {
  // 검색 타입 및 필드 변경
  onTypeChange: (type: AddressSearchTypes) => void;
  onFieldsChange: (fields: AddressSearchField[]) => void;

  // 상태 업데이트 함수들
  setIsOpen: (open: boolean) => void;
  setSearchResults: (results: AddressBase[]) => void;
  setCurrentPage: (page: number) => void;
  setCurrentKeyword: (keyword: string) => void;
  setHasNextPage: (next: boolean) => void;
  setIsFetching: (fetching: boolean) => void;

  addAddressFeature: (item: AddressBase) => void;
  clearAddressFeatures: () => void;
}

// Context 설정 인터페이스
export interface AddressSearchConfig {
  searchTypes?: SearchTypesOptions;
  visibleFields?: VisibleFieldsOptions;
  customOptions?: Record<string, string>;
  pagingType?: PagingType;

  // 콜백 함수들
  onSearch?: SearchCallback;
  onItemClick?: ItemClickCallback;
  onLoadMore?: SearchCallback;
}

// 전체 Context Value 인터페이스
export interface AddressSearchContextValue
  extends AddressSearchState,
    AddressSearchActions,
    AddressSearchConfig {}

// Context 생성
export const AddressSearchContext =
  createContext<AddressSearchContextValue | null>(null);

// 커스텀 훅
export const useAddressSearchContext = (): AddressSearchContextValue => {
  const context = useContext(AddressSearchContext);
  if (!context) {
    throw new Error(
      "AddressSearch components must be used within AddressSearch Provider",
    );
  }
  return context;
};

// 타입 가드 함수
export const isValidSearchResult = (
  result: any,
): result is { resultList: AddressBase[]; hasNextPage: boolean } => {
  return (
    result &&
    Array.isArray(result.resultList) &&
    typeof result.hasNextPage === "boolean"
  );
};
