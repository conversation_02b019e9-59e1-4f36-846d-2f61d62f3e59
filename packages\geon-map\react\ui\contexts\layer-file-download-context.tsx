import { createContext, useContext } from "react";

import { LayerFileDownloadContextValue } from "../types";

// Context 생성
export const LayerFileDownloadContext =
  createContext<LayerFileDownloadContextValue | null>(null);

// 커스텀 훅
export const useLayerFileDownloadContext = () => {
  const context = useContext(LayerFileDownloadContext);
  if (!context) {
    throw new Error(
      "LayerFileDownload components must be used within LayerFileDownload Provider",
    );
  }
  return context;
};
