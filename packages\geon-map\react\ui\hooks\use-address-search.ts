"use client";
import { useFeature } from "@geon-map/react-odf";
import { createGeonAddrgeoClient } from "@geon-query/model";
import { useAppMutation } from "@geon-query/react-query";

import { AddressBase, AddressSearchField, AddressSearchTypes } from "../types";
import { addressSearchWidget as addressSearchConvertor } from "../utils/converter";
import { useHighlightLayer } from "./use-highlight-layer";

export type AddressApiType = "geon";
export type ClientType<T extends AddressApiType> = T extends "geon"
  ? ReturnType<typeof createGeonAddrgeoClient>
  : any;

// 옵션 타입
export interface UseAddressSearchOptions<T extends AddressApiType = "geon"> {
  apiType?: T;
  apiClient?: ClientType<T>; // 클라이언트 객체 직접 주입
  defaultCountPerPage?: number;
  srid?: string;
}

// 검색 파라미터 타입
interface AddressSearchParams {
  searchType: string;
  keyword?: string;
  lat?: number;
  lng?: number;
  currentPage?: number;
  srid?: string;
  countPerPage?: number;
}

/**
 * 🎯 주소 검색 올인원 훅
 * - GEON API 검색
 * - 지도 표시/하이라이트
 * - 위젯 연동
 */
export function useAddressSearch<T extends AddressApiType = "geon">(
  options: UseAddressSearchOptions<T> = {} as UseAddressSearchOptions<T>,
) {
  const {
    apiType = "geon" as T,
    apiClient, // 클라이언트 객체
    defaultCountPerPage = 10,
    srid = "5186",
  } = options;

  // 지도 기능
  const { highlight, clearHighlight, layerId } = useHighlightLayer();
  const { fromWKT, createPointFromCoordinates } = useFeature();

  // 검색 뮤테이션
  const searchMutation = useAppMutation({
    mutationFn: async (params: AddressSearchParams) => {
      // 실제 사용 시점에 체크
      if (!apiClient) {
        throw new Error("useAddressSearch: API 클라이언트가 필요합니다. ");
      }

      const {
        searchType,
        keyword,
        lat,
        lng,
        currentPage = 1,
        countPerPage = defaultCountPerPage,
      } = params;

      const baseParams = {
        currentPage,
        countPerPage,
        targetSrid: srid,
      };

      // 좌표 검색
      if (searchType === "coordinates") {
        if (lat === undefined || lng === undefined) {
          throw new Error("좌표 검색시 lat, lng는 필수입니다.");
        }
        return apiClient.address.coord({
          ...baseParams,
          lat,
          lng,
          byPass: false,
        });
      }

      // 키워드 검색
      if (!keyword) {
        throw new Error("키워드는 필수입니다.");
      }

      const keywordParams = { ...baseParams, keyword };

      switch (searchType) {
        case "integrated":
          return apiClient.address.int({
            ...keywordParams,
            showMultipleResults: true,
          });
        case "building":
          return apiClient.address.bld(keywordParams);
        case "poi":
          return apiClient.address.poi({
            ...keywordParams,
            searchAddress: keyword,
          });
        case "jibun":
          return apiClient.address.jibun(keywordParams);
        case "road":
          return apiClient.address.road(keywordParams);
        case "roadApi":
          return apiClient.address.roadLink(keywordParams);
        case "pnu":
          return apiClient.address.pnu(keywordParams);
        case "postalCode":
          return apiClient.address.basic(keywordParams);
        default:
          throw new Error(`지원하지 않는 검색 타입: ${searchType}`);
      }
    },
  });

  // 위젯용 검색 핸들러
  const handleAddressSearch = async (
    fields: AddressSearchField[],
    type: AddressSearchTypes,
    nextPage?: number,
  ) => {
    try {
      const params = extractSearchParams(fields, type, nextPage, srid);
      const result = await searchMutation.mutateAsync(params);

      return addressSearchConvertor({
        result,
        srid,
        searchType: type,
      });
    } catch (error) {
      console.error("GEON 주소 검색 오류:", error);
      return undefined;
    }
  };

  // 🗺️ 지도에 주소 표시
  const addAddressFeature = (item: AddressBase) => {
    let feature = null;

    if (item.x && item.y) {
      feature = createPointFromCoordinates([item.x, item.y]);
    } else if (item.geometryWKT) {
      feature = fromWKT(item.geometryWKT);
    }

    if (feature) {
      highlight(feature, {
        srid: item.srid,
        isFitToLayer: true,
      });
    } else {
      console.error("주소 좌표 또는 WKT 파싱 실패:", item);
    }
  };

  return {
    // 검색 기능
    handleAddressSearch, // 위젯용
    search: searchMutation.mutateAsync, // 순수 API

    // 지도 기능
    addAddressFeature,
    clearAddressFeatures: clearHighlight,
    layerId,

    // 상태
    isLoading: searchMutation.isPending,
    error: searchMutation.error,

    // 유틸리티
    reset: searchMutation.reset,
    apiClient: apiClient,
    apiType,
  };
}

// 헬퍼 함수
function extractSearchParams(
  fields: AddressSearchField[],
  type: AddressSearchTypes,
  nextPage = 1,
  srid = "5186",
): AddressSearchParams {
  if (type === "coordinates") {
    return {
      searchType: type,
      lat: parseFloat(fields.find((f) => f.key === "lat")?.value?.trim() ?? ""),
      lng: parseFloat(fields.find((f) => f.key === "lng")?.value?.trim() ?? ""),
      currentPage: nextPage,
      srid,
    };
  }

  return {
    searchType: type,
    keyword: fields.find((f) => f.key === type)?.value?.trim() ?? "",
    currentPage: nextPage,
    srid,
  };
}
