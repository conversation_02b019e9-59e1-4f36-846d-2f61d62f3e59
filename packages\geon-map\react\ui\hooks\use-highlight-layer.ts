"use client";
import { useLayer } from "@geon-map/react-odf";
import { useCallback, useEffect, useState } from "react";

type HighlightOptions = {
  isFitToLayer?: boolean;
  srid?: string;
};
type UseHighlightLayerProps = {
  style?: any;
};

export function useHighlightLayer(props: UseHighlightLayerProps = {}) {
  const { style } = props;
  const { addLayer } = useLayer();
  const [layerId, setLayerId] = useState<string | null>(null);

  useEffect(() => {
    // ✅ 하이라이트 레이어 생성 (기본 기능만 사용)
    try {
      const layer = {
        id: `highlight-${Date.now()}`,
        name: "Highlight Layer",
        type: "empty" as const,
        visible: true,
        zIndex: 9999,
        odfLayer: null,
        params: {},
      };
      addLayer(layer);
      setLayerId(layer.id);
    } catch (error) {
      console.error("Failed to add highlight layer:", error);
    }
  }, [addLayer]);

  // ✅ 스타일 적용은 나중에 구현 (현재는 기본 기능만)
  useEffect(() => {
    if (layerId && style) {
      console.log("Style will be applied to layer:", layerId, style);
      // TODO: 스타일 적용 로직 구현
    }
  }, [layerId, style]);

  const highlight = useCallback(
    (feature: any, options: HighlightOptions = {}) => {
      console.log("Highlighting feature:", feature, options);
      if (!feature || !layerId) return;

      // TODO: 실제 하이라이트 기능 구현
      console.log("Feature highlighted on layer:", layerId);
    },
    [layerId],
  );

  const clearHighlight = useCallback(() => {
    if (!layerId) return;

    // TODO: 실제 하이라이트 클리어 기능 구현
    console.log("Highlight cleared on layer:", layerId);
  }, [layerId]);

  return {
    highlight,
    clearHighlight,
    layerId,
  };
}
