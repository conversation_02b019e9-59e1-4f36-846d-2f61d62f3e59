"use-client";

import { createGeonAnalysisClient } from "@geon-query/model";
import { useAppMutation } from "@geon-query/react-query";

export type LayerFileDownloadApiType = "geon";
export type LayerFileDownloadClientType<T extends LayerFileDownloadApiType> =
  T extends "geon" ? ReturnType<typeof createGeonAnalysisClient> : any;

// 옵션 타입
export interface UseLayerFileDownloadOptions<
  T extends LayerFileDownloadApiType = "geon",
> {
  apiType?: T;
  apiClient?: LayerFileDownloadClientType<T>; // 클라이언트 객체 직접주입
}

/** ### 다운로드 파라미터 타입
 * ---
 * - => 그냥 geon-query/model/restapi/type 의 analysis-type.ts 를 import 해서 사용해도 되지만 명시적 구분을 위해서 아래와 같이 사용.
 */
type LayerFileDownloadParams = Parameters<
  LayerFileDownloadClientType<"geon">["fileDownload"]["layerFileDownload"]
>[0];

/** ### 레이어 파일 다운로드 훅 */
export const useLayerFileDownload = <
  T extends LayerFileDownloadApiType = "geon",
>(
  options: UseLayerFileDownloadOptions<T> = {} as UseLayerFileDownloadOptions<T>,
) => {
  const { apiType = "geon" as T, apiClient } = options;

  // 다운로드 뮤테이션
  const downloadLayerFileMutation = useAppMutation({
    mutationFn: async (params: LayerFileDownloadParams) => {
      // 실제 사용 시점에 체크
      if (!apiClient) {
        throw new Error("useLayerFileDownload: API 클라이언트가 필요합니다.");
      }

      return apiClient.fileDownload.layerFileDownload(params);
    },
  });

  // 위젯용 다운로드 핸들러
const handleLayerFileDownload: LayerFileDownloadCallback = async (
  params
) => {
  try {
    const result = await downloadLayerFileMutation.mutateAsync(params);
    return result; // LayerFileDownloadResponse
  } catch (error) {
    console.error("Layer 파일 다운로드 오류: ", error);
    // 빈 Response라도 반드시 반환
    return {
      fileName: "error.txt",
      blob: new Blob([""], { type: "text/plain" }),
    };
  }
};

  return {
    // 레이어 파일 다운로드 기능
    handleLayerFileDownload, // 위젯용
    downloadLayerFile: downloadLayerFileMutation.mutateAsync, // 순수 API

    // 상태
    isLoading: downloadLayerFileMutation.isPending,
    error: downloadLayerFileMutation.error,

    // 유틸리티
    reset: downloadLayerFileMutation.reset,
    apiClient: apiClient,
    apiType,
  };
};
