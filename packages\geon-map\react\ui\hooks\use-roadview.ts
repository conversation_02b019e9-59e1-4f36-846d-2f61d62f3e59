"use client";
// use-roadview.ts
import { <PERSON>, <PERSON><PERSON>, MarkerEventHandlers } from "@geon-map/core";
import { useDraw, useEvent, useMapInstanceRequired } from "@geon-map/react-odf";
import { useRef, useState } from "react";

import MapMarkerIcon from "../resource/images/map-marker.png";

interface UseRoadviewProps {
  onMarkerSelect: (marker: any) => void;
  onCenterSelect?: (center: [number, number]) => void;
}

export function useRoadview({
  onMarkerSelect,
  onCenterSelect,
}: UseRoadviewProps) {
  const [enabled, setEnabled] = useState(false);
  const [resizableWidth, setResizableWidth] = useState(160);
  const [prevWidth, setPrevWidth] = useState(420);
  const [roadviewLayer, setRoadviewLayer] = useState<any | null>(null);

  const { map, odf } = useMapInstanceRequired();

  const { startDrawing, drawControl } = useDraw();
  const { registerListener } = useEvent();
  const cleanupRef = useRef<(() => void) | null>(null);

  const roadviewLayerOption = {
    service: "xyz",
    projection: "EPSG:5181",
    extent: [-30000, -60000, 494288, 988576],
    tileGrid: {
      origin: [-30000, -60000],
      resolutions: [
        4096, 2048, 1024, 512, 256, 128, 64, 32, 16, 8, 4, 2, 1, 0.5, 0.25,
        0.125,
      ],
      matrixIds: [
        "0",
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7",
        "8",
        "9",
        "10",
        "11",
        "12",
        "13",
        "14",
        "15",
      ],
    },
    server: {
      url: "/api/daum/map_k3f_prod/bakery/image_map_png/PNGSD_RV01/v16_o9kb2/{{15-z}}/{{-y-1}}/{{x}}.png",
    },
  };

  const enableRoadView = () => {
    setEnabled(true);
    setResizableWidth(prevWidth);
  };

  const disableRoadView = () => {
    setEnabled(false);
    setPrevWidth(resizableWidth);

    // 등록된 drawend 리스너 정리
    if (cleanupRef.current) {
      cleanupRef.current();
      cleanupRef.current = null;
    }

    setResizableWidth(160);
    map.removeLayer(roadviewLayer);
  };
  // drawend에서 센터 선택 및 마커 생성
  const handleDrawEnd = (feature: any) => {
    try {
      let centerPoint: [number, number] = [0, 0];
      const geometry = feature?.getGeometry?.();
      if (geometry?.getType?.() === "Point") {
        const coord = geometry.getCoordinates?.();
        if (Array.isArray(coord) && coord.length >= 2) {
          centerPoint = [coord[0], coord[1]];
        }
      } else if (geometry?.getExtent) {
        const extent = geometry.getExtent();
        centerPoint = [
          (extent[0] + extent[2]) / 2,
          (extent[1] + extent[3]) / 2,
        ];
      }

      onCenterSelect?.(centerPoint);

      const markerObj = Marker.createAndAddMarker(
        map,
        {
          position: new odf.Coordinate(centerPoint),
          draggable: true,
          style: { src: MapMarkerIcon.src, height: "50px", width: "50px" },
        },
        handlers,
      ).odfMarker;

      onMarkerSelect(markerObj);
      enableRoadView();
    } catch (e) {
      console.error("drawend 처리 중 오류:", e);
    }
  };

  const roadviewLayerOn = () => {
    if (odf && map) {
      const layer = odf.LayerFactory.produce("api", roadviewLayerOption);
      layer.setOpacity(0.5);
      layer.setMap(map);
      setRoadviewLayer(layer);
    }
  };

  // (중복 정의 제거됨)

  const handlers: MarkerEventHandlers = {
    onDragEnd: (event) => {
      const newPosition: { _x: number; _y: number } = event.getPosition();
      // core 패키지를 통한 중심점 설정
      const mapController = new Map(map);
      mapController.setCenter([newPosition._x, newPosition._y]);
      onCenterSelect?.([newPosition._x, newPosition._y]);
    },
  };

  const handleToggle = (checked: boolean, marker: any) => {
    if (checked) {
      roadviewLayerOn();
      alert("지도에 지점을 선택해주세요");

      // ✅ Draw 클래스의 올바른 메서드 사용
      startDrawing("point");

      // TODO: 이벤트 리스너를 통해 drawend 이벤트 처리
      console.log("Point drawing started for roadview");

      // 임시로 centerPoint 설정 (실제로는 drawend 이벤트에서 처리)
      // drawend 리스너 등록
      if (!cleanupRef.current) {
        const target = (drawControl as any) || map;
        if (target) {
          cleanupRef.current = registerListener(
            target,
            "drawend",
            (evt: any) => {
              const feature = evt?.feature || evt?.target || evt;
              handleDrawEnd(feature);
            },
            { listenerId: "roadview_drawend" },
          );
        }
      }

      const centerPoint: [number, number] = [0, 0]; // 기본값
      onCenterSelect?.(centerPoint);

      const markerObj = Marker.createAndAddMarker(
        map,
        {
          position: new odf.Coordinate(centerPoint),
          draggable: true,
          style: {
            src: MapMarkerIcon.src,
            height: "50px",
            width: "50px",
          },
        },
        handlers,
      ).odfMarker;

      onMarkerSelect(markerObj);
      enableRoadView();

      // TODO: 이벤트 정리 로직 구현
      console.log("Roadview marker created");
    } else {
      Marker.removeMarker(marker);
      onMarkerSelect(null);
      disableRoadView();
    }
  };

  return {
    enabled,
    resizableWidth,
    setResizableWidth,
    prevWidth,
    setPrevWidth,
    handleToggle,
  };
}
