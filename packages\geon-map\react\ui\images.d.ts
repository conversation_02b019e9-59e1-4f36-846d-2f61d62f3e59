// declare module "*.png" {
//   const value: { src };
//   export default value;
// }
// declare module "*.jpg" {
//   const value: string;
//   export default value;
// }
// declare module "*.jpeg" {
//   const value: string;
//   export default value;
// }
// declare module "*.svg" {
//   const value: string;
//   export default value;
// }

// 참고: next-env.d.ts, next/image-types/global

type StaticImageData = {
  src: string;
  height: number;
  width: number;
  blurDataURL?: string;
  blurWidth?: number;
  blurHeight?: number;
};

declare module "*.png" {
  const content: StaticImageData;

  export default content;
}

declare module "*.svg" {
  /**
   * Use `any` to avoid conflicts with
   * `@svgr/webpack` plugin or
   * `babel-plugin-inline-react-svg` plugin.
   */
  const content: any;

  export default content;
}

declare module "*.jpg" {
  const content: StaticImageData;

  export default content;
}

declare module "*.jpeg" {
  const content: StaticImageData;

  export default content;
}

declare module "*.gif" {
  const content: StaticImageData;

  export default content;
}

declare module "*.webp" {
  const content: StaticImageData;

  export default content;
}

declare module "*.avif" {
  const content: StaticImageData;

  export default content;
}

declare module "*.ico" {
  const content: StaticImageData;

  export default content;
}

declare module "*.bmp" {
  const content: StaticImageData;

  export default content;
}
