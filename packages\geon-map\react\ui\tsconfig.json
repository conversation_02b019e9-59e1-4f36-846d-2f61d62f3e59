{
  "extends": "@config/typescript/react-library.json",
  "compilerOptions": {
    "outDir": "dist",
    // "rootDir": ".",
    "baseUrl": ".",
    "paths": {
      "@geon-map/core": ["../../../../../core/src"]
    },
    "customConditions": ["development"]
  },
  "include": [
    "./components",
    "./types",
    "./hooks",
    "./constants",
    "./contexts",
    "images.d.ts"
  ],
  "exclude": ["node_modules", "dist"]
}
