import {
  LayerFileDownloadRequest,
  LayerFileDownloadResponse,
} from "@geon-query/model/restapi/type/analysis-type";

// geon-query, geon-map 의 타입을 명시하고자 아래와 같이 사용.
export interface LayerFileDownloadParams extends LayerFileDownloadRequest {}
export interface LayerFileDownloadResult extends LayerFileDownloadResponse {}

// 레이어 파일 다운로드 포맷 유형 타입
export type LayerFileDownloadType = "csv" | "shape" | "kml" | "geojson" | "kmz";

// 레이어 파일 다운로드 콜백 함수 타입
export type LayerFileDownloadCallback = (
  layerFileDownloadInfo: LayerFileDownloadParams,
) => Promise<LayerFileDownloadResult> | undefined;

// 전체 Context Value 인터페이스 타입
export interface LayerFileDownloadContextValue {
  layerFileDownloadInfo?: LayerFileDownloadParams;
  onClickLayerFileDownload?: LayerFileDownloadCallback;
  isLoading?: boolean;
}

// 컴포넌트 Props 인테페이스들
type buttonName = {
  buttonName: string | "";
};
export interface LayerFileDownloadWidgetProps
  extends LayerFileDownloadContextValue,
    React.ComponentPropsWithoutRef<"div">,
    buttonName {}
export interface LayerFileDownloadProps
  extends React.HTMLAttributes<HTMLDivElement>,
    LayerFileDownloadContextValue {}
export interface LayerFileDownloadTriggerProps
  extends React.HTMLAttributes<HTMLButtonElement>,
    buttonName {}
export interface LayerFileDownloadContentProps
  extends React.HTMLAttributes<HTMLDivElement> {}
export interface LayerFileDownloadItemProps
  extends React.HTMLAttributes<HTMLButtonElement>,
    LayerFileDownloadContextValue {
  layerFileDownloadType: LayerFileDownloadType;
}
