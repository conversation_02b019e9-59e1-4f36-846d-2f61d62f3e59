import {
  AdministBaseRequest,
  AdministCtpvRequest,
  AdministEmdRequest,
  AdministLiRequest,
  AdministSggRequest,
  createGeonAddrgeoClient,
} from "@geon-query/model";
import * as React from "react";

// ===== 기본 타입 정의 =====
export type WKTPolygonType = `POLYGON(${string})` | `MULTIPOLYGON(${string})`;
export type ApiType = "geon";
export type ClientType<T extends ApiType> = T extends "geon"
  ? ReturnType<typeof createGeonAddrgeoClient>
  : any;

export type RegionType = "sido" | "sigungu" | "eupmyeondong" | "li";

// ===== 기본 데이터 인터페이스 =====
export interface RegionInfo {
  code: string;
  name: string;
}

export interface RegionData {
  sido?: string | null;
  sigungu?: string | null;
  eupmyeondong?: string | null;
  li?: string | null;
}

export interface RegionListData {
  sido?: RegionInfo[];
  sigungu?: RegionInfo[];
  eupmyeondong?: RegionInfo[];
  li?: RegionInfo[];
}

export interface RegionItem {
  code: string;
  name: string;
  wktPolygon: WKTPolygonType;
}

// ===== API 관련 타입 =====
export type AdministApiRequestType =
  | AdministBaseRequest
  | AdministCtpvRequest
  | AdministEmdRequest
  | AdministSggRequest
  | AdministLiRequest;

export interface RegionSelectParams {
  regionType: RegionType;
  regionCode: string;
  srid: string;
}

// ===== 옵션 타입 =====
export interface UseRegionSelectorOptions<T extends ApiType = "geon"> {
  apiType?: T;
  apiClient?: ClientType<T>;
  srid?: string;
}

// ===== 핸들러 타입 정의 (use-region-selector.ts에서 사용) =====
export type RegionInfoHandler = (
  regionType: RegionType,
  regionCode: string,
) => Promise<RegionItem | null>;

export type RegionListHandler = (
  regionType: RegionType,
  parentCode?: string | null,
) => Promise<RegionInfo[] | null>;

export type RegionPnuHandler = (
  lonLat: [number, number],
) => Promise<RegionData | null>;

// ===== 지역 변경 파라미터 (use-region-selector.ts에서 사용) =====
export interface RegionChangeParams {
  regionCode: string;
  regionName: string;
  wktPolygon: WKTPolygonType;
  regionType: RegionType;
}

// ===== 통합된 액션 인터페이스 =====
export interface RegionSelectorActions {
  // 비즈니스 상태 액션들
  /**
   * 특정 지역 타입의 코드를 선택
   * @param regionType - 지역 타입 (sido, sigungu, eupmyeondong, li)
   * @param code - 선택할 지역 코드
   */
  selectRegion: (regionType: RegionType, code: string) => void;

  /**
   * 하위 지역들을 초기화 (상위 지역 변경시 사용)
   * @param regionTypes - 초기화할 지역 타입들의 배열
   */
  clearChildRegions: (regionTypes: RegionType[]) => void;

  /**
   * 모든 지역 선택을 초기화
   */
  resetAllRegions: () => void;

  /**
   * 특정 지역 타입의 리스트를 업데이트
   * @param regionType - 업데이트할 지역 타입
   * @param list - 새로운 지역 리스트
   */
  updateRegionList: (regionType: RegionType, list: RegionInfo[]) => void;

  /**
   * 여러 지역 타입의 리스트를 한번에 업데이트
   * @param updates - 업데이트할 지역 리스트들의 부분 객체
   */
  updateMultipleRegionLists: (updates: Partial<RegionListData>) => void;

  /**
   * 특정 지역 타입들의 리스트를 초기화 (빈 배열로 설정)
   * @param regionTypes - 초기화할 지역 타입들의 배열
   */
  clearRegionList: (regionTypes: RegionType[]) => void;

  /**
   * 전체 지역 데이터를 초기화 (초기 로딩, 지도 동기화시 사용)
   * @param regionData - 설정할 지역 데이터 (null 체크 포함)
   * @param listData - 설정할 지역 리스트 데이터
   * @param useLi - 리 단위 사용 여부 (기본값: false)
   */
  initialize: (
    regionData: RegionData | null,
    listData: RegionListData,
    useLi?: boolean,
  ) => void;

  // 지도 관련 액션들
  /**
   * 지도에 지역 폴리곤을 표시
   * @param wktPolygon - WKT 폴리곤 문자열
   */
  showRegionOnMap: (wktPolygon: WKTPolygonType) => void;

  /**
   * 지역 데이터에서 폴리곤을 추출하여 지도에 표시
   * @param regionData - 지역 데이터 (WKT 폴리곤 포함)
   */
  showRegionFromData: (regionData: RegionItem) => void;

  /**
   * 지도의 하이라이트를 제거
   */
  clearMapHighlight: () => void;

  // 복합 액션들
  /**
   * 지역 선택과 지도 표시를 동시에 수행
   * @param regionType - 지역 타입
   * @param code - 선택할 지역 코드
   * @param regionData - 지역 데이터 (지도 표시용)
   */
  selectAndShowRegion: (
    regionType: RegionType,
    code: string,
    regionData: RegionItem,
  ) => void;

  /**
   * 모든 상태를 초기화하고 지도 하이라이트도 제거
   */
  resetAllAndClearMap: () => void;
}

// ===== 훅 반환 타입 =====
export interface UseRegionSelectorReturn {
  // 상태
  regionLists: RegionListData;
  selectedRegion: RegionData;
  isLoading: boolean;
  error: Error | null;

  // 통합된 액션 인터페이스
  actions: RegionSelectorActions;

  // 핸들러
  handleRegionSelect: RegionInfoHandler;
  handleRegionList: RegionListHandler;
  handlePnuSelect: RegionPnuHandler;
  handleRegionChange: (params: RegionChangeParams) => Promise<any>;

  // 유틸리티
  getParentRegionType: (regionType: RegionType) => RegionType | undefined;
}

// ===== 컨텍스트 타입 (region-selector-widget.tsx에서 사용) =====
export interface RegionSelectorContextValue {
  // 상태
  regionLists: RegionListData;
  selectedRegion: RegionData;
  isLoading: boolean;
  error: Error | null;

  // 통합된 액션 인터페이스
  actions: RegionSelectorActions;

  // API 함수들 (위젯에서 필요한 핸들러들)
  fetchRegionInfo: RegionInfoHandler;
  fetchRegionList: RegionListHandler;
  fetchPnu: RegionPnuHandler;
  handleRegionChange: (params: RegionChangeParams) => Promise<any>;

  // 설정값들
  useLi: boolean;
  enableMapSync: boolean;
  setEnableMapSync: React.Dispatch<React.SetStateAction<boolean>>;

  // 유틸 함수
  getParentRegionType: (regionType: RegionType) => RegionType | undefined;
}

// ===== 컴포넌트 Props 타입 =====
export interface RegionSelectTriggerProps
  extends React.HTMLAttributes<HTMLButtonElement> {}

export interface RegionSelectorProps
  extends React.HTMLAttributes<HTMLDivElement> {
  fetchRegionInfo: RegionInfoHandler;
  fetchRegionList: RegionListHandler;
  fetchPnu: RegionPnuHandler;
  useLi?: boolean;
}

export interface RegionSelectorWidgetProps
  extends React.HTMLAttributes<HTMLDivElement> {
  fetchRegionInfo: RegionInfoHandler;
  fetchRegionList: RegionListHandler;
  fetchPnu: RegionPnuHandler;
  useLi?: boolean;
}
