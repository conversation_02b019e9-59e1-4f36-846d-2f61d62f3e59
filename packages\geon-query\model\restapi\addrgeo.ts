import { API_TYPE, apiHelper } from "../utils/geonAPI";
import {
  AddressBldRequest,
  AddressCoordRequest,
  AddressIntRequest,
  AddressJibunRequest,
  AddressPnuRequest,
  AddressPoiRequest,
  AddressRoadLinkRequest,
  AddressRoadRequest,
  AddressSearchResponse,
  AdministCtpvListRequest,
  AdministCtpvRequest,
  AdministEmdListRequest,
  AdministEmdRequest,
  AdministLiListRequest,
  AdministLiRequest,
  AdministResponse,
  AdministSggListRequest,
  AdministSggRequest,
} from "./type/addrgeo-type";

const type: API_TYPE = "addrgeo";

// 설정 타입
export interface AddrgeoAPIConfig {
  baseUrl?: string;
  crtfckey?: string;
  timeout?: number;
}
//타입 선언용 returnType 정의
export type AddrgeoClient = ReturnType<typeof createGeonAddrgeoClient>;

// 동적 API 클라이언트 생성 함수
export function createGeonAddrgeoClient(config: AddrgeoAPIConfig = {}) {
  const { baseUrl, crtfckey } = config;
  const api = apiHelper({ type, baseUrl, crtfckey });
  return {
    // 주소 API
    address: {
      /** 건물 주소 검색 */
      bld: api.get<AddressBldRequest, AddressSearchResponse>("/address/bld"),

      /** 통합 주소 검색 */
      int: api.get<AddressIntRequest, AddressSearchResponse>("/address/int"),

      /** 지번 주소 검색 */
      jibun: api.get<AddressJibunRequest, AddressSearchResponse>(
        "/address/jibun",
      ),

      /** 도로명 주소 검색 */
      road: api.get<AddressRoadRequest, AddressSearchResponse>("/address/road"),

      /** 도로 링크 기반 주소 검색 */
      roadLink: api.get<AddressRoadLinkRequest, AddressSearchResponse>(
        "/address/road/link",
      ),

      /** 좌표 → 주소 변환 */
      coord: api.get<AddressCoordRequest, AddressSearchResponse>(
        "/address/coord",
      ),

      /** PNU 기반 주소 조회 */
      pnu: api.get<AddressPnuRequest, AddressSearchResponse>("/address/pnu"),

      /** POI 주소 검색 */
      poi: api.get<AddressPoiRequest, AddressSearchResponse>("/address/poi"),

      /** 기초구역번호 검색 */
      basic: api.get<AddressBldRequest, AddressSearchResponse>(
        "/address/basic",
      ),
    },

    // 행정구역 API
    administ: {
      /** 시도 검색 */
      ctpv: api.get<AdministCtpvRequest, AdministResponse>("/administ/ctpv"),

      /** 시도 목록 검색 */
      ctpvList: api.get<AdministCtpvListRequest, AdministResponse>(
        "/administ/ctpv/list",
      ),

      /** 시군구 검색 */
      sgg: api.get<AdministSggRequest, AdministResponse>("/administ/sgg"),

      /** 시군구 목록 검색 */
      sggList: api.get<AdministSggListRequest, AdministResponse>(
        "/administ/sgg/list",
      ),

      /** 읍면동 검색 */
      emd: api.get<AdministEmdRequest, AdministResponse>("/administ/emd"),

      /** 읍면동 목록 검색 */
      emdList: api.get<AdministEmdListRequest, AdministResponse>(
        "/administ/emd/list",
      ),

      /** 리 검색 */
      li: api.get<AdministLiRequest, AdministResponse>("/administ/li"),

      /** 리 목록 검색 */
      liList: api.get<AdministLiListRequest, AdministResponse>(
        "/administ/li/list",
      ),
    },
  };
}
export const defaultGeonAddrgeoClient = createGeonAddrgeoClient();
