/** @Todo : analysis-type 만들고 import, fetcher import */
import { API_TYPE, apiUrl } from "../utils/geonAPI";
import { Log } from "../utils/Log";
import {
  LayerFileDownloadRequest,
  LayerFileDownloadResponse,
} from "./type/analysis-type";

const type: API_TYPE = "analysis";

// 설정 타입
export interface AnalysisConfig {
  baseUrl?: string;
  crtfckey?: string;
  timeout?: number;
}

// 동적 API 클라이언트 생성 함수
export function createGeonAnalysisClient(config: AnalysisConfig = {}) {
  const { baseUrl, crtfckey } = config;

  return {
    fileDownload: {
      /** ### 레이어 파일 다운로드 (e.g. .csv, .zip, .geojson, .kml)
       * ---
       *  - blob 형태의 응답을 받기 위해 utils/fetcher 는 사용하지 않는다.
       *  @param params LayerFileDownloadRequest
       *  @returns API Response Header, Blob
       */
      layerFileDownload: async (
        params: LayerFileDownloadRequest,
      ): Promise<LayerFileDownloadResponse | undefined> => {
        const fullUrl = apiUrl({
          endpoint: "/layer/file/download",
          type: type,
          baseUrl,
          crtfckey,
        });

        Log.logRequest("POST", fullUrl, params);

        try {
          const response = await fetch(fullUrl, {
            method: "POST",
            headers: {
              // 서버에 보내는 요청은 JSON 형식
              "Content-Type": "application/json",
            },
            body: JSON.stringify(params),
          });

          if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
          }

          const blob = await response.blob();

          Log.logResponse("POST", fullUrl, {
            message: "Response is a Blob",
            size: blob.size,
            type: blob.type,
          });

          return { headers: response.headers, blob: blob };
        } catch (error) {
          Log.logError("POST", fullUrl, error);
          throw error;
        }
      },
    },
  };
}
