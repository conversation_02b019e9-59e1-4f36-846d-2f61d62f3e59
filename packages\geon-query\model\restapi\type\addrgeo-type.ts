// 0. 공통
type WKTPolygonType = `POLYGON(${string})` | `MULTIPOLYGON(${string})`;

// 1. 위치검색
/** 공통: 인증키와 키워드 */
export interface AddressBaseRequest {
  keyword: string;
  crtfckey?: string;
  currentPage?: number;
  countPerPage?: number;
}

/** 공통: 좌표계/중복결과 */
export interface AddressAdvancedRequest {
  targetSrid?: string;
  showMultipleResults?: boolean;
}

/** 건물 주소 검색 요청 */
export interface AddressBldRequest extends AddressBaseRequest {
  targetSrid?: string;
}

/** 통합 주소 검색 요청 */
export interface AddressIntRequest
  extends AddressBaseRequest,
    AddressAdvancedRequest {}

/** 지번 주소 검색 요청 */
export interface AddressJibunRequest
  extends AddressBaseRequest,
    AddressAdvancedRequest {}

/** 도로명 주소 검색 요청 */
export interface AddressRoadRequest
  extends AddressBaseRequest,
    AddressAdvancedRequest {}

/** 도로 링크 기반 주소 검색 요청 */
export interface AddressRoadLinkRequest extends AddressBaseRequest {
  targetSrid?: string;
}

/** 좌표 → 주소 변환 요청 (구조 다름) */
export interface AddressCoordRequest {
  lat: number;
  lng: number;
  crtfckey?: string;
  showMultipleResults?: boolean;
  targetSrid?: string;
  byPass?: boolean;
}

/** PNU 기반 주소 조회 요청 */
export interface AddressPnuRequest
  extends AddressBaseRequest,
    AddressAdvancedRequest {}

/** POI 주소 검색 요청 */
export interface AddressPoiRequest extends AddressBaseRequest {
  targetSrid?: string;
  searchAddress: string;
}

/** 기초구역번호 검색 요청 */
export interface AddressBasicRequest extends AddressBaseRequest {}

// 공통 응답 구조
export interface AddressCommonResponse {
  searchAddressGbn: string;
  gbn: string;
  clOk: string;
  trOk: string;
  clCd: string;
  trCd: string;
  clMessage: string;
  trMessage: string;
  totalCount: string;
  currentPage: number;
  countPerPage: number;
}

// 주소 검색 결과 항목
export interface JusoItem {
  parcelX?: string;
  parcelY?: string;
  buildX?: string;
  buildY?: string;
  roadAddr?: string;
  jibunAddr?: string;
  addrPnu?: string;
  roadAddrIdKy?: string;
  roadBdMgNo?: string;
  geom?: WKTPolygonType;
  buildGeom?: WKTPolygonType;
  buildName?: string;
  parcelLo?: string;
  parcelLa?: string;
  buildLo?: string;
  buildLa?: string;
  poiName?: string;
  poiX?: string;
  poiY?: string;
}

export interface AddressSearchResponse {
  code: number;
  message: string;
  result: {
    common: AddressCommonResponse;
    jusoList: JusoItem[];
  };
}

// 2. 행정구역 검색
// 2-1. 요청
export interface AdministBaseRequest {
  crtfckey?: string;
  retGeom: boolean;
  targetSrid: string;
}
// [요청] 시도 검색
export interface AdministCtpvRequest extends AdministBaseRequest {
  ctprvnCd: string;
}
// [요청] 시도 리스트 검색
export interface AdministCtpvListRequest extends AdministBaseRequest {}

// [요청] 시군구 검색
export interface AdministSggRequest extends AdministBaseRequest {
  sigCd: string;
}
// [요청] 시군구 리스트 검색
export interface AdministSggListRequest extends AdministBaseRequest {
  ctprvnCd: string;
}

// [요청] 읍면동 검색
export interface AdministEmdRequest extends AdministBaseRequest {
  emdCd: string;
}

// [요청] 읍면동 리스트 검색
export interface AdministEmdListRequest extends AdministBaseRequest {
  sigCd: string;
}

// [요청] 리 검색
export interface AdministLiRequest extends AdministBaseRequest {
  liCd: string;
}

// [요청] 리 리스트 검색
export interface AdministLiListRequest extends AdministBaseRequest {
  emdCd: string;
}

// 2-1. 응답
export interface AdministItem {
  cd: string;
  engNm: string;
  korNm: string;
  x: string;
  y: string;
  geom: WKTPolygonType;
}
export interface AdministResponse {
  code: number;
  message: string;
  result: AdministItem[];
}
