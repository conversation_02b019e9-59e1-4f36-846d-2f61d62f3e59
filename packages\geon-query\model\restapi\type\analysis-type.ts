/** ### Analysis API Base Request
 * ---
 * - Analysis API 에서 받는 Parameter, RequestBody Filed 를 모두 갖는다.
 */
interface AnalysisBaseRequest {
  areaField?: string;
  areaUnit?: string;
  attributeFields?: string;
  attributeJoinType?: string;
  boundType?: string;
  bufferType?: boolean;
  caseField?: string;
  cmprAdministzoneCode?: string;
  cmprCode?: string;
  cmprCqlFilter?: string;
  cmprFileName?: string;
  cmprOpert?: string;
  cmprText?: string;
  cmprTextSrid?: string;
  cmprTypeName?: string;
  cntntsId?: string;
  crtfckey?: string;
  dimensionField?: string;
  direct?: boolean;
  dissolveType?: string;
  distance?: number;
  distanceUnit?: string;
  ellipseSize?: string;
  fileName?: string;
  fileOut?: boolean;
  flterAdministzoneCode?: string;
  flterCode?: string;
  flterCqlFilter?: string;
  flterFileName?: string;
  flterText?: string;
  flterTextSrid?: string;
  flterTypeName?: string;
  foreignKey?: string;
  joinOperation?: string;
  lengthField?: string;
  lyrnm?: string;
  mesureMth?: string;
  minute?: number;
  nearLmtt?: number;
  onlyValidGrid?: boolean;
  opertMergeFields?: string;
  opertNtcnSn?: string;
  opertUsrId?: string;
  outputCharset?: string;
  outputFormat?: string;
  outputSrid?: string;
  outsideOnly?: boolean;
  pointCount?: number;
  power?: number;
  resultFileName?: string;
  returnBoundingAreas?: boolean;
  searchRadius?: string;
  separationDistance?: number;
  separationDistanceType?: string;
  sizeType?: string;
  spatialJoinType?: string;
  spatialRelationship?: string;
  srid?: string;
  statisticsFields?: string;
  stdrAttrbNm?: string;
  sumField?: string;
  summaryType?: string;
  trgetAdministzoneCode?: string;
  trgetCode?: string;
  trgetCqlFilter?: string;
  trgetFileName?: string;
  trgetText?: string;
  trgetTextSrid?: string;
  trgetTypeName?: string;
  type?: string;
  useMultiPart?: boolean;
  userId?: string;
  validGrid?: boolean;
  weightField?: string;
}

// 1. 파일 다운로드 Request, Response 영역
// 1-1. 레이어 파일 다운로드
/** ### 레이어 파일 다운로드 Request
 * ---
 * - AnalysisBaseRequest 에서 Pick 과 Required 를 이용해서 Request 의 필수 값, 비 필수 값 구분하여 조합하는 형태
 */
export type LayerFileDownloadRequest = Pick<
  AnalysisBaseRequest,
  | "crtfckey"
  | "trgetCqlFilter"
  | "cmprTextSrid"
  | "cmprFileName"
  | "flterCode"
  | "flterTypeName"
  | "flterText"
  | "flterTextSrid"
  | "flterFileName"
> &
  Required<Pick<AnalysisBaseRequest, "trgetTypeName" | "outputFormat">>;

export type LayerFileDownloadResponse = {
  headers: Headers;
  blob?: Blob;
};
