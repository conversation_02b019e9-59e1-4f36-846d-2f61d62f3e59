export interface LayerGroupInfoListRequest {
  crtfckey?: string;
  lyrGrpNm?: string;
  userId?: string;
}

export interface LayerGroupInfoInsertRequest {
  crtfckey: string;
  userId: string;
  lyrGrpNm: string;
}

export interface LayerGroupInfoUpdateRequest {
  groupNm: string; // 레이어 그룹명
  groupDc?: string; // 레이어 그룹 설명
  lyrGroupId: string; // 레이어 그룹 ID (수정 대상 식별)
  othbcAt?: string; // 공개 여부 (예: 'Y' | 'N')
  userId: string; // 사용자 ID (수정자)
  crtfckey?: string; // 인증 키
}

export interface LayerGroupInfoDeleteRequest {
  crtfckey: string;
  lyrGrpId: string;
  userId: string;
}

export interface LayerGroupInfoSelectRequest {
  crtfckey: string;
  lyrGrpId: string;
}

export interface LayerGroupInfoExcelRequest {
  crtfckey: string;
  userId?: string;
  groupNm?: string;
}

export interface LayerGroupInfoListV2Request {
  crtfckey?: string;
  userId?: string;
  pageIndex?: number; // 페이지 인덱스
  pageSize?: number; // 페이지 크기
  othbcAt?: string; // 공개 여부 (예: 'Y' | 'N')
}

export interface LayerShareInsertRequest {
  crtfckey?: string;
  layerId: string;
  userId: string;
  shareGrant: string;
}

export interface LayerShareDeleteRequest {
  crtfckey?: string;
  layerShareId: string;
}

export interface LayerShareUpdateRequest {
  crtfckey?: string;
  layerShareId: string;
  shareGrant: string;
}

export interface LayerShareSelectRequest {
  crtfckey?: string;
  layerId: string;
}

export interface LayerShareSelectByUserIdRequest {
  crtfckey?: string;
  userId: string;
}

type LyrStleCode = "M" | "F" | "T";
// 타입은 swagger 정의 기준, 존재하지 않으면 명시적으로 작성해야 함

export interface CommonGroupCodeRequest {
  clCode?: string; // 분류 코드 (예: MPD)
  crtfckey?: string; // 인증키
  groupCode?: string; // 그룹 코드 (예: MPD003)
}

export interface CommonDetailCodeRequest {
  clCode?: string; // 분류 코드
  crtfckey?: string; // 인증키
  groupCode?: string; // 그룹 코드
}
export interface BasemapUpdateRequest {
  bcrnMapClCode: string;
  bcrnMapId: string;
  bcrnMapNm: string;
  bcrnMapUseAt: "Y" | "N";
  crtfckey?: string;
  lyrStleCode: LyrStleCode;
  mapUrl: string;
  mapUrlParamtr?: string;
  registerId: string;
}
export interface BasemapSelectRequest {
  bcrnMapId?: string;
  crtfckey?: string;
}

export interface BasemapListRequest {
  bcrnMapClCode?: string;
  bcrnMapNm?: string;
  bcrnMapUseAt?: "Y" | "N";
  crtfckey?: string;
  imageAt?: "Y" | "N";
  lyrStleCode?: LyrStleCode;
  pageIndex: number;
  pageSize: number;
}

export interface BasemapListResponse {
  code: number; // 응답 코드 (예: 200)
  message: string; // 응답 메시지 (예: "성공")
  result: {
    pageInfo: {
      pageSize: number;
      pageIndex: number;
      totalCount: number;
    };
    list: BasemapItem[];
  };
}
export interface BasemapItem {
  bcrnMapId: string; // 배경지도 ID
  bcrnMapNm: string; // 배경지도 이름
  bcrnMapUseAt: string; // 사용 여부 ("Y" or "N")
  bcrnMapClCode: string; // 지도 분류 코드
  bcrnMapClCodeNm: string; // 지도 분류 코드 이름
  lyrStleCode: string; // 스타일 코드 (예: "M")
  lyrStleCodeNm: string; // 스타일 코드 이름 (예: "WMS")
  mapUrl: string; // 지도 URL
  mapUrlparamtr: string; // 지도 파라미터 (JSON 문자열 형태)
  base64: string; // 이미지 Base64 인코딩 데이터
  registerId: string; // 등록자 ID
  registDt: string; // 등록일시 (YYYY-MM-DD HH:mm)
  updusrId: string; // 수정자 ID
  updtDt: string; // 수정일시 (YYYY-MM-DD HH:mm)
}
export interface BasemapInsertRequest {
  bcrnMapClCode: string;
  bcrnMapNm: string;
  bcrnMapUseAt: "Y" | "N";
  crtfckey?: string;
  lyrStleCode: LyrStleCode;
  mapUrl: string;
  mapUrlParamtr?: string;
  registerId: string;
}

export interface BasemapDeleteRequest {
  bcrnMapId: string;
  crtfckey?: string;
}
