import { Log } from "./Log";
type ContentType = "application/json" | "application/x-www-form-urlencoded";
export const fetcher = {
  get: async <T = unknown>(
    url: string,
    params?: Record<string, any>,
  ): Promise<T> => {
    const queryString = params
      ? Object.entries(params)
          .map(
            ([key, value]) =>
              `${encodeURIComponent(key)}=${encodeURIComponent(value)}`,
          )
          .join("&")
      : "";

    const fullUrl = queryString
      ? url.includes("?")
        ? `${url}&${queryString}`
        : `${url}?${queryString}`
      : url;
    Log.logRequest("GET", fullUrl);

    try {
      const res = await fetch(fullUrl);
      const data = await res.json();
      Log.logResponse("GET", fullUrl, data);
      return data;
    } catch (error) {
      Log.logError("GET", fullUrl, error);
      throw error;
    }
  },

  post: async <T = unknown, B = Record<string, any>>(
    url: string,
    body: B,
    contentType: ContentType = "application/json",
  ): Promise<T> => {
    Log.logRequest("POST", url, body);

    try {
      const headers: HeadersInit = {
        "Content-Type": contentType,
      };

      const encodedBody =
        contentType === "application/json"
          ? JSON.stringify(body)
          : new URLSearchParams(body as Record<string, string>).toString();

      const res = await fetch(url, {
        method: "POST",
        headers,
        body: encodedBody,
      });

      const data = await res.json();
      Log.logResponse("POST", url, data);
      return data;
    } catch (error) {
      Log.logError("POST", url, error);
      throw error;
    }
  },
};
