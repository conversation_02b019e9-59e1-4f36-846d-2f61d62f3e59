/* 
    API 인터페이스
    1.지도관련 API https://city.geon.kr/api/map/swagger-ui/index.html
    2.지오코딩 API https://city.geon.kr/api/addrgeo/swagger-ui/index.html
    3.도형분석 API https://city.geon.kr/api/analysis/swagger-ui/index.html
    4.좌표변환 API https://city.geon.kr/api/coord/swagger-ui/index.html
    5.도형정보 등록 API https://city.geon.kr/api/publish/swagger-ui/index.html
    6.저작도구 API https://city.geon.kr/api/smt/swagger-ui/index.html
    7.부동산 정보 API https://city.geon.kr/api/estate/swagger-ui/index.html
    무안군 crtfckey : UxizIdSqCePz93ViFt8ghZFFJuOzvUp0

    URL만드는법은 맨 아래 주석 참고
 */

export const url = {
  map_comment: "지도 요청지도 요청 기능 제공 합니다.",
  map: {
    baroemap_comment: "바로e맵 서비스",
    baroemap: "/api/map/baroemap",
    ngisair_comment: "정사영상 서비스",
    ngisair: "/api/map/ngisair",
    wfs_comment: "wfs 서비스 [POST]",
    wfs: "/api/map/wfs",
    wms_comment: "wms 서비스 [POST]",
    wms: "/api/map/wms",
    wmts_comment: "wmts 서비스",
    wmts: "/api/map/wmts",
  },
  vworld_comment: "지도 요청지도 요청 기능 제공 합니다.",
  vworld: {
    wfs_comment: "브이월드 서비스 (WFS)",
    wfs: "/api/vworld/wfs",
    wms_comment: "브이월드 서비스 (WMS)",
    wms: "/api/vworld/wms",
  },
  "{tileRow}_comment": "지도 요청지도 요청 기능 제공 합니다.",
  "{tileRow}": {
    "{tileCol}_comment": "브이월드 서비스 (WMTS)",
    "{tileCol}":
      "/api/vworld/wmts/{layer}/{tileMatrix}/{tileRow}/{tileCol}.{tileType}",
  },
  wmts_comment: "지도 요청지도 요청 기능 제공 합니다.",
  wmts: {
    capabilities_comment: "브이월드 서비스 (Capabilities)",
    capabilities: "/api/vworld/wmts/capabilities",
  },
  cn_comment: "컨텐츠관리컨텐츠 정보 관리 기능 제공 합니다.",
  cn: {
    delete_comment: "컨텐츠 삭제",
    delete: "/layer/cn/delete",
    list_comment: "컨텐츠 목록조회",
    list: "/layer/cn/list",
    select_comment: "컨텐츠 상세조회",
    select: "/layer/cn/select",
    update_comment: "컨텐츠 수정",
    update: "/layer/cn/update",
  },
  find_comment: "컨텐츠관리컨텐츠 정보 관리 기능 제공 합니다.",
  find: {
    fromEmdCode_comment: "컨텐츠검색(법정동선택)",
    fromEmdCode: "/layer/cn/find/fromEmdCode",
    fromGeometry_comment: "컨텐츠검색(영역선택)",
    fromGeometry: "/layer/cn/find/fromGeometry",
  },
  _comment: "테이블 레이어 정보 관리테이블 레이어 정보 관리 기능을 제공합니다.",
  "": {
    storage_comment: "저장소 삭제(관리자 시스템 전용)",
    storage: "/storage",
    table_comment: "테이블 레이어 목록 조회",
    table: "/table",
  },
  address_comment: "위치검색위치검색",
  address: {
    bld_comment: "건물명 검색",
    bld: "/address/bld",
    coord_comment: "경위도 좌표 위치검색",
    coord: "/address/coord",
    int_comment: "통합 위치검색",
    int: "/address/int",
    jibun_comment: "지번 위치검색",
    jibun: "/address/jibun",
    pnu_comment: "PNU 위치검색",
    pnu: "/address/pnu",
    poi_comment: "POI 검색",
    poi: "/address/poi",
    road_comment: "도로명주소 위치검색",
    road: "/address/road",
  },
  jibun_comment: "위치검색위치검색",
  jibun: {
    list_comment: "지번 위치검색(다건)",
    list: "/address/jibun/list",
  },
  road_comment: "위치검색위치검색",
  road: {
    link_comment: "도로명주소API 검색",
    link: "/address/road/link",
  },
  refine_comment: "주소정제주소정제",
  refine: {
    file_comment: "주소정제(파일)",
    file: "/refine/file",
    single_comment: "주소정제(단일)",
    single: "/refine/single",
    stream_comment: "주소정제(Stream)",
    stream: "/refine/stream",
  },
  geofile_comment:
    "지오코딩 결과 파일 관리지오코딩 결과 파일 관리 기능 제공 합니다.",
  geofile: {
    delete_comment: "지오코딩 결과 파일 삭제",
    delete: "/geofile/delete",
    insert_comment: "지오코딩 결과 파일 등록",
    insert: "/geofile/insert",
    list_comment: "지오코딩 결과 파일 7일전 목록 조회",
    list: "/geofile/list",
    select_comment: "지오코딩 결과 파일 경로 조회",
    select: "/geofile/select",
    update_comment: "지오코딩 결과 파일 수정",
    update: "/geofile/update",
  },
  select_comment:
    "지오코딩 결과 파일 관리지오코딩 결과 파일 관리 기능 제공 합니다.",
  select: {
    userId_comment: "사용자가 공유받은 레이어의 공유 정보 조회",
    userId: "/layer/share/select/userId",
    "image-contents_comment": "웹앱 템플릿 이미지 컨텐츠 조회(스토리/탭 테마)",
    "image-contents": "/webapp/tmplat/select/image-contents",
    detail_comment: "지오코딩 결과 상세 조회",
    detail: "/geofile/select/detail",
  },
  geoco_comment: "지오코딩지오코딩",
  geoco: {
    file_comment: "지오코딩(파일)",
    file: "/geoco/file",
    stream_comment: "지오코딩",
    stream: "/geoco/stream",
  },
  administ_comment: "행정구역 검색행정구역 검색",
  administ: {
    ctpv_comment: "시도 검색",
    ctpv: "/administ/ctpv",
    emd_comment: "읍면동 검색",
    emd: "/administ/emd",
    li_comment: "리 검색",
    li: "/administ/li",
    sgg_comment: "시군구 검색",
    sgg: "/administ/sgg",
  },
  ctpv_comment: "행정구역 검색행정구역 검색",
  ctpv: {
    list_comment: "시도 리스트 검색",
    list: "/administ/ctpv/list",
  },
  emd_comment: "행정구역 검색행정구역 검색",
  emd: {
    list_comment: "읍면동 리스트 검색",
    list: "/administ/emd/list",
  },
  li_comment: "행정구역 검색행정구역 검색",
  li: {
    list_comment: "리 리스트 검색",
    list: "/administ/li/list",
  },
  sgg_comment: "행정구역 검색행정구역 검색",
  sgg: {
    list_comment: "시군구 리스트 검색",
    list: "/administ/sgg/list",
  },
  "geojson-file-to-shp_comment": "공간정보 변환공간정보 확장자 변환",
  "geojson-file-to-shp": {
    download_comment: "GeoJson 파일을 shp 파일로 변환",
    download: "/convert/geojson-file-to-shp/download",
  },
  "geojson-text-to-shp_comment": "공간정보 변환공간정보 확장자 변환",
  "geojson-text-to-shp": {
    download_comment: "GeoJson 텍스트를 shp 파일로 변환",
    download: "/convert/geojson-text-to-shp/download",
  },
  pttrn_comment: "도형분석-공간패턴 분석공간패턴 분석",
  pttrn: {
    density_comment: "밀도 분석",
    density: "/anals/pttrn/density",
    hotspot_comment: "핫 스팟 분석(V2)",
    hotspot: "/anals/pttrn/hotspot",
    interpolatePoints_comment: "포인트 내삽 찾기 분석",
    interpolatePoints: "/anals/pttrn/interpolatePoints",
  },
  proximity_comment: "도형분석-근접도 분석근접도 분석",
  proximity: {
    buffer_comment: "버퍼 분석",
    buffer: "/anals/proximity/buffer",
    connectDestination_comment: "출발지와 목적지 연결 분석",
    connectDestination: "/anals/proximity/connectDestination",
    drivingArea_comment: "운전시간 영역 생성 분석",
    drivingArea: "/anals/proximity/drivingArea",
    findNearestPoint_comment: "최근접 위치찾기 분석",
    findNearestPoint: "/anals/proximity/findNearestPoint",
    findPath_comment: "경로계획 분석",
    findPath: "/anals/proximity/findPath",
  },
  manage_comment: "도형분석-데이터 관리 분석데이터 관리 분석",
  manage: {
    ar_comment: "객체별 면적 계산",
    ar: "/anals/manage/ar",
    clustering_comment: "클러스터링",
    clustering: "/anals/manage/clustering",
    dsslve_comment: "경계디졸브",
    dsslve: "/anals/manage/dsslve",
    dvsion_comment: "공간 분할 생성",
    dvsion: "/anals/manage/dvsion",
    lt_comment: "객체별 길이 계산",
    lt: "/anals/manage/lt",
    merge_comment: "병합(Merge)",
    merge: "/anals/manage/merge",
  },
  extrc_comment: "도형분석-데이터 관리 분석데이터 관리 분석",
  extrc: {
    file_comment: "데이터 추출(파일)",
    file: "/anals/manage/extrc/file",
    stream_comment: "데이터 추출(스트림)",
    stream: "/anals/manage/extrc/stream",
  },
  ovrlay_comment: "도형분석-데이터 관리 분석데이터 관리 분석",
  ovrlay: {
    erase_comment: "지우기(Erase)",
    erase: "/anals/manage/ovrlay/erase",
    intsct_comment: "교차(Intersect)",
    intsct: "/anals/manage/ovrlay/intsct",
    union_comment: "결합(Union)",
    union: "/anals/manage/ovrlay/union",
  },
  intsct_comment: "도형분석-데이터 관리 분석데이터 관리 분석",
  intsct: {
    coverage_comment: "레스터 레이어 교차(Intersect)",
    coverage: "/anals/manage/ovrlay/intsct/coverage",
  },
  sumry_comment: "도형분석-데이터 요약 분석데이터 요약 분석",
  sumry: {
    ag_comment: "포인트집계 분석",
    ag: "/anals/sumry/ag",
    center_comment: "공간분포 패턴",
    center: "/anals/sumry/center",
    join_comment: "공간 조인",
    join: "/anals/sumry/join",
    nrby_comment: "주변 집계",
    nrby: "/anals/sumry/nrby",
    range_comment: "영역 내 집계",
    range: "/anals/sumry/range",
  },
  lc_comment: "도형분석-위치찾기 분석위치찾기 분석",
  lc: {
    searchCenter_comment: "중심 찾기",
    searchCenter: "/anals/lc/searchCenter",
    searchLegacy_comment: "공간 조건 검색",
    searchLegacy: "/anals/lc/searchLegacy",
    searchNew_comment: "공간 조건 추출",
    searchNew: "/anals/lc/searchNew",
  },
  ntice_comment: "작업알림작업알림",
  ntice: {
    detail_comment: "작업 상세조회",
    detail: "/ntice/detail",
    seedState_comment: "시드 상태 조회",
    seedState: "/ntice/seedState",
  },
  file_comment:
    "파일 다운로드레이어 또는 속성 정보를 파일로 다운로드하는 기능을 제공 합니다.",
  file: {
    download_comment: "레이어 파일 다운로드",
    download: "/layer/file/download",
    donwload_comment: "이미지 레이어 다운로드",
    donwload: "/image/layer/file/donwload",
  },
  geotiff_comment:
    "파일 다운로드레이어 또는 속성 정보를 파일로 다운로드하는 기능을 제공 합니다.",
  geotiff: {
    download_comment: "GeoTIFF 레이어 다운로드",
    download: "/image/layer/geotiff/download",
  },
  dxf_comment: "공간 도형 정보 업로드공간 도형 정보 업로드 입니다",
  dxf: {
    file_comment: "Dxf 파일 서비스 발행",
    file: "/upload/spatial/dxf/file",
    stream_comment: "Dxf 파일 업로드",
    stream: "/upload/spatial/dxf/stream",
  },
  coord_comment: "좌표변환좌표변환",
  coord: {
    single_comment: "단일 좌표 및 도분초 변환",
    single: "/coord/single",
    stream_comment: "파일 좌표변환",
    stream: "/coord/stream",
    text_comment: "피처 문자열 좌표변환",
    text: "/coord/text",
  },
  db_comment: "공간 도형 정보 업로드공간 도형 정보 업로드 입니다",
  db: {
    republish_comment: "DB Table 서비스 재발행",
    republish: "/upload/spatial/db/republish",
    table_comment: "DB Table 서비스 발행",
    table: "/upload/spatial/db/table",
  },
  geojson_comment: "공간 도형 정보 업로드공간 도형 정보 업로드 입니다",
  geojson: {
    file_comment: "GeoJSON 파일 서비스 발행",
    file: "/upload/spatial/geojson/file",
    stream_comment: "GeoJSON 파일 업로드",
    stream: "/upload/spatial/geojson/stream",
    text_comment: "GeoJSON 텍스트 서비스 발행",
    text: "/upload/spatial/geojson/text",
  },
  netcdf_comment: "공간 도형 정보 업로드공간 도형 정보 업로드 입니다",
  netcdf: {
    file_comment: "NetCDF 파일 서비스 발행",
    file: "/upload/spatial/netcdf/file",
    stream_comment: "NetCdf 파일 업로드",
    stream: "/upload/spatial/netcdf/stream",
  },
  shp_comment: "공간 도형 정보 업로드공간 도형 정보 업로드 입니다",
  shp: {
    file_comment: "SHP 파일 서비스 발행",
    file: "/upload/spatial/shp/file",
    stream_comment: "SHP 파일 업로드",
    stream: "/upload/spatial/shp/stream",
  },
  tif_comment: "공간 도형 정보 업로드공간 도형 정보 업로드 입니다",
  tif: {
    file_comment: "Tif 파일 서비스 발행",
    file: "/upload/spatial/tif/file",
    stream_comment: "Tif 파일 업로드",
    stream: "/upload/spatial/tif/stream",
  },
  image_comment: "사용자 이미지 관리사용자 이미지 파일을 등록/수정/삭제/조회",
  image: {
    delete_comment: "사용자 이미지 삭제",
    delete: "/image/delete",
    insert_comment: "사용자 이미지 등록",
    insert: "/image/insert",
    list_comment: "사용자 이미지 목록조회",
    list: "/image/list",
    select_comment: "사용자 이미지 상세조회",
    select: "/image/select",
    update_comment: "사용자 이미지 수정",
    update: "/image/update",
  },
  sld_comment: "SLD 관리지오서버에 저장된 SLD 정보를 관리합니다.",
  sld: {
    stream_comment: "SLD 추가(스트림)",
    stream: "/sld/stream",
  },
  code_comment: "공통 코드 조회공통 코드 조회 기능 제공 합니다.",
  code: {
    detail_comment: "공통 상세 코드 조회",
    detail: "/cmmn/code/detail",
    group_comment: "공통 그룹 코드 조회",
    group: "/cmmn/code/group",
  },
  instt_comment: "기관 정보기관 정보 관련 기능을 제공합니다.",
  instt: {
    id_comment: "기관 기본 정보 조회",
    id: "/instt/id",
  },
  share_comment: "웹앱 템플릿 관리웹앱 템플릿 관련 기능을 제공합니다.",
  share: {
    delete_comment: "웹앱 템플릿 부분 공유 삭제",
    delete: "/webapp/tmplat/share/delete",
    insert_comment: "웹앱 템플릿 공유 등록 및 수정",
    insert: "/webapp/tmplat/share/insert",
    select_comment: "웹앱 템플릿 공유 조회",
    select: "/webapp/tmplat/share/select",
    update_comment: "웹맵 공유 수정",
    update: "/webmap/share/update",
    list_comment: "웹앱 템플릿 공유 목록 조회",
    list: "/webapp/tmplat/share/list",
  },
  info_comment: "웹맵 관리웹맵 관련 기능을 제공합니다.",
  info: {
    delete_comment: "웹맵 삭제",
    delete: "/webmap/info/delete",
    excel_comment: "레이어 그룹 목록 엑셀 다운로드(관리자 시스템 전용)",
    excel: "/lyrgrp/info/excel",
    insert_comment: "웹맵 등록",
    insert: "/webmap/info/insert",
    list_comment: "웹맵 목록 조회",
    list: "/webmap/info/list",
    select_comment: "웹맵 상세 조회",
    select: "/webmap/info/select",
    update_comment: "웹맵 수정",
    update: "/webmap/info/update",
    auth_comment: "웹맵 권한 체크",
    auth: "/webmap/info/auth",
  },
  v2_comment: "레이어 그룹 정보레이어 그룹 관련 기능을 제공합니다.",
  v2: {
    list_comment: "레이어 그룹 목록 조회(관리자)",
    list: "/lyrgrp/info/v2/list",
  },
  layer_comment: "TOC 레이어 설정TOC 레이어 설정 기능 제공 합니다.",
  layer: {
    attributes_comment: "레이어 속성 데이터 목록 조회",
    attributes: "/layer/attributes",
    delete_comment: "TOC 레이어설정 삭제",
    delete: "/toc/layer/delete",
    insert_comment: "TOC 레이어설정 등록",
    insert: "/toc/layer/insert",
    select_comment: "TOC 레이어설정  조회",
    select: "/toc/layer/select",
    update_comment: "TOC 레이어설정 수정",
    update: "/toc/layer/update",
  },
  attributes_comment:
    "레이어 속성 정보 관리레이어 속성 정보 관리 기능을 제공 합니다.",
  attributes: {
    aggregate_comment: "레이어 속성 데이터 집계",
    aggregate: "/layer/attributes/aggregate",
    bbox_comment: "레이어 BBOX 조회",
    bbox: "/layer/attributes/bbox",
    count_comment: "레이어 속성 데이터 개수 조회",
    count: "/layer/attributes/count",
    select_comment: "레이어 속성 데이터 목록 조회 (POST)",
    select: "/layer/attributes/select",
  },
  aggregate_comment:
    "레이어 속성 정보 관리레이어 속성 정보 관리 기능을 제공 합니다.",
  aggregate: {
    select_comment: "레이어 속성 데이터 집계 (POST)",
    select: "/layer/attributes/aggregate/select",
  },
  bbox_comment:
    "레이어 속성 정보 관리레이어 속성 정보 관리 기능을 제공 합니다.",
  bbox: {
    select_comment: "레이어 BBOX 조회 (POST)",
    select: "/layer/attributes/bbox/select",
  },
  count_comment:
    "레이어 컬럼 정보 관리레이어 컬럼 정보 관리 기능을 제공합니다.",
  count: {
    select_comment: "레이어 컬럼 유일값 갯수 조회 (POST)",
    select: "/layer/column/unique/count/select",
  },
  flter_comment: "레이어 속성필터 설정레이어 속성필터 설정 기능 제공 합니다.",
  flter: {
    delete_comment: "레이어 속성필터 삭제",
    delete: "/layer/flter/delete",
    insert_comment: "레이어 속성필터 등록 및 수정",
    insert: "/layer/flter/insert",
    select_comment: "레이어 속성필터 조회",
    select: "/layer/flter/select",
  },
  style_comment: "레이어 스타일 설정레이어 스타일 설정 기능 제공 합니다.",
  style: {
    delete_comment: "레이어 스타일 삭제",
    delete: "/layer/style/delete",
    insert_comment: "레이어 스타일 등록",
    insert: "/layer/style/insert",
    select_comment: "레이어 스타일 조회",
    select: "/layer/style/select",
    update_comment: "레이어 스타일 수정",
    update: "/layer/style/update",
  },
  symbol_comment: "레이어 심볼 설정레이어 심볼 설정 기능 제공 합니다.",
  symbol: {
    delete_comment: "이미지 심볼 삭제",
    delete: "/layer/symbol/delete",
    insert_comment: "이미지 심볼 등록",
    insert: "/layer/symbol/insert",
    select_comment: "이미지 심볼 조회",
    select: "/layer/symbol/select",
    update_comment: "이미지 심볼 수정",
    update: "/layer/symbol/update",
  },
  thumbnail_comment:
    "웹앱 템플릿의 썸네일 관리웹앱 템플릿의 썸네일을 등록/수정/삭제",
  thumbnail: {
    delete_comment: "웹앱 썸네일 삭제",
    delete: "/webapp/tmplat/thumbnail/delete",
    insert_comment: "웹맵 썸네일 등록",
    insert: "/webmap/thumbnail/insert",
    select_comment: "웹맵 썸네일 조회",
    select: "/webmap/thumbnail/select",
    update_comment: "웹앱 썸네일 등록 및 수정",
    update: "/webapp/tmplat/thumbnail/update",
  },
  update_comment: "웹맵 관리웹맵 관련 기능을 제공합니다.",
  update: {
    body_comment: "웹맵 썸네일 수정(RequestBody)",
    body: "/webmap/thumbnail/update/body",
    type_comment: "레이어 컬럼 타입 수정",
    type: "/layer/column/update/type",
    code_comment: "사용공유 구분코드 수정",
    code: "/webmap/info/update/code",
  },
  column_comment:
    "레이어 컬럼 정보 관리레이어 컬럼 정보 관리 기능을 제공합니다.",
  column: {
    create_comment: "레이어 컬럼 생성",
    create: "/layer/column/create",
    insert_comment: "레이어 컬럼 정보 등록 및 수정",
    insert: "/layer/column/insert",
    range_comment: "레이어 컬럼 범위 조회",
    range: "/layer/column/range",
    select_comment: "레이어 컬럼 정보 조회",
    select: "/layer/column/select",
    unique_comment: "레이어 컬럼 유일값 조회",
    unique: "/layer/column/unique",
  },
  unique_comment:
    "레이어 컬럼 정보 관리레이어 컬럼 정보 관리 기능을 제공합니다.",
  unique: {
    count_comment: "레이어 컬럼 유일값 갯수 조회",
    count: "/layer/column/unique/count",
    select_comment: "레이어 컬럼 유일값 조회 (POST)",
    select: "/layer/column/unique/select",
  },
  popup_comment: "레이어 팝업 설정레이어 팝업 설정 기능 제공 합니다.",
  popup: {
    delete_comment: "레이어 팝업 설정 삭제",
    delete: "/layer/popup/delete",
    insert_comment: "레이어 팝업 설정 등록 및 수정",
    insert: "/layer/popup/insert",
    select_comment: "레이어 팝업 설정 조회",
    select: "/layer/popup/select",
  },
  basemap_comment: "베이스맵 관리베이스맵 정보 관리 기능 제공 합니다.",
  basemap: {
    delete_comment: "베이스맵 삭제",
    delete: "/basemap/delete",
    insert_comment: "베이스맵 추가",
    insert: "/basemap/insert",
    list_comment: "베이스맵 목록",
    list: "/basemap/list",
    select_comment: "베이스맵 조회",
    select: "/basemap/select",
    update_comment: "베이스맵 수정",
    update: "/basemap/update",
  },
  users_comment: "사용자 관리사용자 관련 기능을 제공합니다.",
  users: {
    id_comment: "사용자 조회",
    id: "/users/id",
  },
  bkmk_comment: "사용자 북마크 관리사용자 북마크 관리 기능 제공 합니다.",
  bkmk: {
    delete_comment: "사용자 북마크 삭제",
    delete: "/bkmk/delete",
    insert_comment: "사용자 북마크 등록",
    insert: "/bkmk/insert",
    list_comment: "사용자 북마크 목록 조회",
    list: "/bkmk/list",
    select_comment: "사용자 북마크 조회",
    select: "/bkmk/select",
    update_comment: "사용자 북마크 수정",
    update: "/bkmk/update",
  },
  opertntcn_comment:
    "사용자 지도 작업 알림 관리사용자 지도 작업 알림 관리 기능 제공 합니다.",
  opertntcn: {
    delete_comment: "사용자 지도 작업 알림 삭제",
    delete: "/usermap/opertntcn/delete",
    insert_comment: "사용자 지도 작업 알림 등록",
    insert: "/usermap/opertntcn/insert",
    list_comment: "사용자 지도 작업 알림 목록 조회",
    list: "/usermap/opertntcn/list",
    queue_comment: "사용자 지도 작업 알림 대기열 목록 조회",
    queue: "/usermap/opertntcn/queue",
    select_comment: "사용자 지도 작업 알림 조회",
    select: "/usermap/opertntcn/select",
    stats_comment: "사용자 지도 작업 알림 상태 변경",
    stats: "/usermap/opertntcn/stats",
    update_comment: "사용자 지도 작업 알림 수정",
    update: "/usermap/opertntcn/update",
  },
  stats_comment:
    "사용자 지도 작업 알림 관리사용자 지도 작업 알림 관리 기능 제공 합니다.",
  stats: {
    geocoding_comment: "사용자 지도 작업 알림 지오코딩 상태 변경",
    geocoding: "/usermap/opertntcn/stats/geocoding",
  },
  ownership_comment: "ApiBuildingRegistryController건축물 대장 정보 조회",
  ownership: {
    area_comment: "전유공유면적 조회",
    area: "/api/registry/ownership/area",
  },
  upload_comment:
    "업로드 이미지 레이어 정보 관리레이어 정보 관리 기능 제공 합니다.",
  upload: {
    image_comment: "이미지 레이어 정보 조회",
    image: "/upload/image",
  },
  url_comment:
    "웹 레이어 서비스웹 레이어에 대한 레이어 서비스 등록/수정/삭제/조회",
  url: {
    delete_comment: "웹레이어 삭제",
    delete: "/layer/url/delete",
    insert_comment: "웹레이어 등록",
    insert: "/layer/url/insert",
    list_comment: "웹레이어 목록조회",
    list: "/layer/url/list",
    select_comment: "웹레이어 상세조회",
    select: "/layer/url/select",
    update_comment: "웹레이어 수정",
    update: "/layer/url/update",
  },
  auth_comment: "웹앱 템플릿 관리웹앱 템플릿 관련 기능을 제공합니다.",
  auth: {
    fullshare_comment: "웹앱 템플릿 전체 공유 권한 체크",
    fullshare: "/webapp/tmplat/auth/fullshare",
  },
  insert_comment: "웹앱 템플릿 관리웹앱 템플릿 관련 기능을 제공합니다.",
  insert: {
    process_comment:
      "웹맵, 웹맵 레이어그룹, 웹맵 레이어설정, 웹맵 공유 일괄 저장",
    process: "/webmap/info/insert/process",
    body_comment: "웹맵 썸네일 등록(RequestBody)",
    body: "/webmap/thumbnail/insert/body",
    "image-contents_comment": "웹앱 템플릿 이미지 컨텐츠 등록(스토리/탭 테마)",
    "image-contents": "/webapp/tmplat/insert/image-contents",
  },
  allShare_comment: "웹앱 템플릿 관리웹앱 템플릿 관련 기능을 제공합니다.",
  allShare: {
    delete_comment: "웹앱 전체 공유 삭제",
    delete: "/webapp/tmplat/allShare/delete",
    insert_comment: "웹앱 전체 공유(개선 필요)",
    insert: "/webapp/tmplat/allShare/insert",
  },
  tmplat_comment: "웹앱 템플릿 관리웹앱 템플릿 관련 기능을 제공합니다.",
  tmplat: {
    auth_comment: "웹앱 템플릿 권한 체크",
    auth: "/webapp/tmplat/auth",
    delete_comment: "웹앱 템플릿 삭제",
    delete: "/webapp/tmplat/delete",
    insert_comment: "웹앱 템플릿 등록 및 수정",
    insert: "/webapp/tmplat/insert",
    list_comment: "웹앱 템플릿 목록 조회",
    list: "/webapp/tmplat/list",
    select_comment: "웹앱 템플릿 상세 조회",
    select: "/webapp/tmplat/select",
  },
  storage_comment: "저장소 정보 관리저장소 정보 관리 기능을 제공합니다.",
  storage: {
    activation_comment: "저장소 활성화 여부 수정(관리자 시스템 전용)",
    activation: "/storage/activation",
    detail_comment: "저장소 상세 조회(관리자 시스템 전용)",
    detail: "/storage/detail",
  },
  table_comment:
    "테이블 레이어 정보 관리테이블 레이어 정보 관리 기능을 제공합니다.",
  table: {
    publish_comment: "테이블 레이어 발행(관리자 시스템 전용)",
    publish: "/table/publish",
    republish_comment: "테이블 레이어 재발행(관리자 시스템 전용)",
    republish: "/table/republish",
  },
  group_comment: "TOC 그룹 설정TOC 그룹 설정 기능 제공 합니다.",
  group: {
    delete_comment: "TOC 그룹삭제",
    delete: "/toc/group/delete",
    insert_comment: "TOC 그룹등록",
    insert: "/toc/group/insert",
    select_comment: "TOC 그룹조회",
    select: "/toc/group/select",
    update_comment: "TOC 그룹수정",
    update: "/toc/group/update",
  },
  lry_comment: "TOC 그룹 설정TOC 그룹 설정 기능 제공 합니다.",
  lry: {
    id_comment: "레이어 그룹 ID 생성",
    id: "/toc/group/lry/id",
  },
  heading_comment: "ApiBuildController건물 조회(일필지)",
  heading: {
    floor_comment: "(Open-API) 건물층수 조회",
    floor: "/api/builds/build/heading/floor",
    ho_comment: "(Open-API) 건물호수 조회",
    ho: "/api/builds/build/heading/ho",
  },
  build_comment: "ApiBuildController건물 조회(일필지)",
  build: {
    register_comment: "(Open-API) 대지권 등록 목록 조회",
    register: "/api/builds/build/register",
    serial_comment: "(Open-API) 건물일련번호 조회",
    serial: "/api/builds/build/serial",
  },
  registry_comment: "ApiBuildingRegistryController건축물 대장 정보 조회",
  registry: {
    floor_comment: "층별개요 조회",
    floor: "/api/registry/floor",
    headings_comment: "표제부 조회",
    headings: "/api/registry/headings",
    ownership_comment: "전유부 조회",
    ownership: "/api/registry/ownership",
  },
  general_comment: "ApiBuildingRegistryController건축물 대장 정보 조회",
  general: {
    headings_comment: "총괄표제부 조회",
    headings: "/api/registry/general/headings",
  },
  land_comment: "ApiLandUsePlanController토지이용계획 조회(일필지)",
  land: {
    basic_comment: "(Open-API) 토지임야 목록 조회",
    basic: "/api/land/basic",
    characteristics_comment: "(Open-API) 토지특성 속성 조회",
    characteristics: "/api/land/characteristics",
    history_comment: "(Open-API) 토지이동이력 속성 조회",
    history: "/api/land/history",
    ownership_comment: "(Open-API) 토지소유정보 속성 조회",
    ownership: "/api/land/ownership",
    useplan_comment: "(Open-API) 토지이용계획 속성 조회",
    useplan: "/api/land/useplan",
  },
  parcel_comment: "ApiOnePcAllInqireController일필지 종합 정보",
  parcel: {
    all_comment: "(Open-API) 일필지 종합 정보 조회",
    all: "/api/parcel/all",
  },
  house_comment: "ApiPriceController가격 정보 조회(일필지)",
  house: {
    apt_comment: "(V-World) 공동주택가격 속성 조회",
    apt: "/api/price/house/apt",
    ind_comment: "(V-World) 개별주택가격 속성 조회",
    ind: "/api/price/house/ind",
  },
  price_comment: "ApiPriceController가격 정보 조회(일필지)",
  price: {
    pclnd_comment: "(V-World) 개별공시지가 속성 조회",
    pclnd: "/api/price/pclnd",
  },
  search_comment: "LandBundleController토지묶음",
  search: {
    address_comment: "",
    address: "/api/landbundle/search/address",
  },
} as const;
export type UrlType = typeof url;
export const url_comment = {
  "/api/map/baroemap": "바로e맵 서비스",
  "/api/map/ngisair": "정사영상 서비스",
  "/api/map/wfs": "wfs 서비스 [POST]",
  "/api/map/wms": "wms 서비스 [POST]",
  "/api/map/wmts": "wmts 서비스",
  "/api/vworld/wfs": "브이월드 서비스 (WFS)",
  "/api/vworld/wms": "브이월드 서비스 (WMS)",
  "/api/vworld/wmts/{layer}/{tileMatrix}/{tileRow}/{tileCol}.{tileType}":
    "브이월드 서비스 (WMTS)",
  "/api/vworld/wmts/capabilities": "브이월드 서비스 (Capabilities)",
  "/layer/cn/delete": "컨텐츠 삭제",
  "/layer/cn/list": "컨텐츠 목록조회",
  "/layer/cn/select": "컨텐츠 상세조회",
  "/layer/cn/update": "컨텐츠 수정",
  "/layer/cn/find/fromEmdCode": "컨텐츠검색(법정동선택)",
  "/layer/cn/find/fromGeometry": "컨텐츠검색(영역선택)",
  "/address/bld": "건물명 검색",
  "/address/coord": "경위도 좌표 위치검색",
  "/address/int": "통합 위치검색",
  "/address/jibun": "지번 위치검색",
  "/address/pnu": "PNU 위치검색",
  "/address/poi": "POI 검색",
  "/address/road": "도로명주소 위치검색",
  "/address/jibun/list": "지번 위치검색(다건)",
  "/address/road/link": "도로명주소API 검색",
  "/refine/file": "주소정제(파일)",
  "/refine/single": "주소정제(단일)",
  "/refine/stream": "주소정제(Stream)",
  "/geofile/delete": "지오코딩 결과 파일 삭제",
  "/geofile/insert": "지오코딩 결과 파일 등록",
  "/geofile/list": "지오코딩 결과 파일 7일전 목록 조회",
  "/geofile/select": "지오코딩 결과 파일 경로 조회",
  "/geofile/update": "지오코딩 결과 파일 수정",
  "/layer/share/select/userId": "사용자가 공유받은 레이어의 공유 정보 조회",
  "/webapp/tmplat/select/image-contents":
    "웹앱 템플릿 이미지 컨텐츠 조회(스토리/탭 테마)",
  "/geofile/select/detail": "지오코딩 결과 상세 조회",
  "/geoco/file": "지오코딩(파일)",
  "/geoco/stream": "지오코딩",
  "/administ/ctpv": "시도 검색",
  "/administ/emd": "읍면동 검색",
  "/administ/li": "리 검색",
  "/administ/sgg": "시군구 검색",
  "/administ/ctpv/list": "시도 리스트 검색",
  "/administ/emd/list": "읍면동 리스트 검색",
  "/administ/li/list": "리 리스트 검색",
  "/administ/sgg/list": "시군구 리스트 검색",
  "/convert/geojson-file-to-shp/download": "GeoJson 파일을 shp 파일로 변환",
  "/convert/geojson-text-to-shp/download": "GeoJson 텍스트를 shp 파일로 변환",
  "/anals/pttrn/density": "밀도 분석",
  "/anals/pttrn/hotspot": "핫 스팟 분석(V2)",
  "/anals/pttrn/interpolatePoints": "포인트 내삽 찾기 분석",
  "/anals/proximity/buffer": "버퍼 분석",
  "/anals/proximity/connectDestination": "출발지와 목적지 연결 분석",
  "/anals/proximity/drivingArea": "운전시간 영역 생성 분석",
  "/anals/proximity/findNearestPoint": "최근접 위치찾기 분석",
  "/anals/proximity/findPath": "경로계획 분석",
  "/anals/manage/ar": "객체별 면적 계산",
  "/anals/manage/clustering": "클러스터링",
  "/anals/manage/dsslve": "경계디졸브",
  "/anals/manage/dvsion": "공간 분할 생성",
  "/anals/manage/lt": "객체별 길이 계산",
  "/anals/manage/merge": "병합(Merge)",
  "/anals/manage/extrc/file": "데이터 추출(파일)",
  "/anals/manage/extrc/stream": "데이터 추출(스트림)",
  "/anals/manage/ovrlay/erase": "지우기(Erase)",
  "/anals/manage/ovrlay/intsct": "교차(Intersect)",
  "/anals/manage/ovrlay/union": "결합(Union)",
  "/anals/manage/ovrlay/intsct/coverage": "레스터 레이어 교차(Intersect)",
  "/anals/sumry/ag": "포인트집계 분석",
  "/anals/sumry/center": "공간분포 패턴",
  "/anals/sumry/join": "공간 조인",
  "/anals/sumry/nrby": "주변 집계",
  "/anals/sumry/range": "영역 내 집계",
  "/anals/lc/searchCenter": "중심 찾기",
  "/anals/lc/searchLegacy": "공간 조건 검색",
  "/anals/lc/searchNew": "공간 조건 추출",
  "/ntice/detail": "작업 상세조회",
  "/ntice/seedState": "시드 상태 조회",
  "/layer/file/download": "레이어 파일 다운로드",
  "/image/layer/file/donwload": "이미지 레이어 다운로드",
  "/image/layer/geotiff/download": "GeoTIFF 레이어 다운로드",
  "/upload/spatial/dxf/file": "Dxf 파일 서비스 발행",
  "/upload/spatial/dxf/stream": "Dxf 파일 업로드",
  "/coord/single": "단일 좌표 및 도분초 변환",
  "/coord/stream": "파일 좌표변환",
  "/coord/text": "피처 문자열 좌표변환",
  "/upload/spatial/db/republish": "DB Table 서비스 재발행",
  "/upload/spatial/db/table": "DB Table 서비스 발행",
  "/upload/spatial/geojson/file": "GeoJSON 파일 서비스 발행",
  "/upload/spatial/geojson/stream": "GeoJSON 파일 업로드",
  "/upload/spatial/geojson/text": "GeoJSON 텍스트 서비스 발행",
  "/upload/spatial/netcdf/file": "NetCDF 파일 서비스 발행",
  "/upload/spatial/netcdf/stream": "NetCdf 파일 업로드",
  "/upload/spatial/shp/file": "SHP 파일 서비스 발행",
  "/upload/spatial/shp/stream": "SHP 파일 업로드",
  "/upload/spatial/tif/file": "Tif 파일 서비스 발행",
  "/upload/spatial/tif/stream": "Tif 파일 업로드",
  "/image/delete": "사용자 이미지 삭제",
  "/image/insert": "사용자 이미지 등록",
  "/image/list": "사용자 이미지 목록조회",
  "/image/select": "사용자 이미지 상세조회",
  "/image/update": "사용자 이미지 수정",
  "/sld/stream": "SLD 추가(스트림)",
  "/cmmn/code/detail": "공통 상세 코드 조회",
  "/cmmn/code/group": "공통 그룹 코드 조회",
  "/instt/id": "기관 기본 정보 조회",
  "/webapp/tmplat/share/delete": "웹앱 템플릿 부분 공유 삭제",
  "/webapp/tmplat/share/insert": "웹앱 템플릿 공유 등록 및 수정",
  "/webapp/tmplat/share/select": "웹앱 템플릿 공유 조회",
  "/webmap/share/update": "웹맵 공유 수정",
  "/webapp/tmplat/share/list": "웹앱 템플릿 공유 목록 조회",
  "/webmap/info/delete": "웹맵 삭제",
  "/lyrgrp/info/excel": "레이어 그룹 목록 엑셀 다운로드(관리자 시스템 전용)",
  "/webmap/info/insert": "웹맵 등록",
  "/webmap/info/list": "웹맵 목록 조회",
  "/webmap/info/select": "웹맵 상세 조회",
  "/webmap/info/update": "웹맵 수정",
  "/webmap/info/auth": "웹맵 권한 체크",
  "/lyrgrp/info/v2/list": "레이어 그룹 목록 조회(관리자)",
  "/layer/attributes": "레이어 속성 데이터 목록 조회",
  "/toc/layer/delete": "TOC 레이어설정 삭제",
  "/toc/layer/insert": "TOC 레이어설정 등록",
  "/toc/layer/select": "TOC 레이어설정  조회",
  "/toc/layer/update": "TOC 레이어설정 수정",
  "/layer/attributes/aggregate": "레이어 속성 데이터 집계",
  "/layer/attributes/bbox": "레이어 BBOX 조회",
  "/layer/attributes/count": "레이어 속성 데이터 개수 조회",
  "/layer/attributes/select": "레이어 속성 데이터 목록 조회 (POST)",
  "/layer/attributes/aggregate/select": "레이어 속성 데이터 집계 (POST)",
  "/layer/attributes/bbox/select": "레이어 BBOX 조회 (POST)",
  "/layer/column/unique/count/select": "레이어 컬럼 유일값 갯수 조회 (POST)",
  "/layer/flter/delete": "레이어 속성필터 삭제",
  "/layer/flter/insert": "레이어 속성필터 등록 및 수정",
  "/layer/flter/select": "레이어 속성필터 조회",
  "/layer/style/delete": "레이어 스타일 삭제",
  "/layer/style/insert": "레이어 스타일 등록",
  "/layer/style/select": "레이어 스타일 조회",
  "/layer/style/update": "레이어 스타일 수정",
  "/layer/symbol/delete": "이미지 심볼 삭제",
  "/layer/symbol/insert": "이미지 심볼 등록",
  "/layer/symbol/select": "이미지 심볼 조회",
  "/layer/symbol/update": "이미지 심볼 수정",
  "/webapp/tmplat/thumbnail/delete": "웹앱 썸네일 삭제",
  "/webmap/thumbnail/insert": "웹맵 썸네일 등록",
  "/webmap/thumbnail/select": "웹맵 썸네일 조회",
  "/webapp/tmplat/thumbnail/update": "웹앱 썸네일 등록 및 수정",
  "/webmap/thumbnail/update/body": "웹맵 썸네일 수정(RequestBody)",
  "/layer/column/update/type": "레이어 컬럼 타입 수정",
  "/webmap/info/update/code": "사용공유 구분코드 수정",
  "/layer/column/create": "레이어 컬럼 생성",
  "/layer/column/insert": "레이어 컬럼 정보 등록 및 수정",
  "/layer/column/range": "레이어 컬럼 범위 조회",
  "/layer/column/select": "레이어 컬럼 정보 조회",
  "/layer/column/unique": "레이어 컬럼 유일값 조회",
  "/layer/column/unique/count": "레이어 컬럼 유일값 갯수 조회",
  "/layer/column/unique/select": "레이어 컬럼 유일값 조회 (POST)",
  "/layer/popup/delete": "레이어 팝업 설정 삭제",
  "/layer/popup/insert": "레이어 팝업 설정 등록 및 수정",
  "/layer/popup/select": "레이어 팝업 설정 조회",
  "/basemap/delete": "베이스맵 삭제",
  "/basemap/insert": "베이스맵 추가",
  "/basemap/list": "베이스맵 목록",
  "/basemap/select": "베이스맵 조회",
  "/basemap/update": "베이스맵 수정",
  "/users/id": "사용자 조회",
  "/bkmk/delete": "사용자 북마크 삭제",
  "/bkmk/insert": "사용자 북마크 등록",
  "/bkmk/list": "사용자 북마크 목록 조회",
  "/bkmk/select": "사용자 북마크 조회",
  "/bkmk/update": "사용자 북마크 수정",
  "/usermap/opertntcn/delete": "사용자 지도 작업 알림 삭제",
  "/usermap/opertntcn/insert": "사용자 지도 작업 알림 등록",
  "/usermap/opertntcn/list": "사용자 지도 작업 알림 목록 조회",
  "/usermap/opertntcn/queue": "사용자 지도 작업 알림 대기열 목록 조회",
  "/usermap/opertntcn/select": "사용자 지도 작업 알림 조회",
  "/usermap/opertntcn/stats": "사용자 지도 작업 알림 상태 변경",
  "/usermap/opertntcn/update": "사용자 지도 작업 알림 수정",
  "/usermap/opertntcn/stats/geocoding":
    "사용자 지도 작업 알림 지오코딩 상태 변경",
  "/api/registry/ownership/area": "전유공유면적 조회",
  "/upload/image": "이미지 레이어 정보 조회",
  "/layer/url/delete": "웹레이어 삭제",
  "/layer/url/insert": "웹레이어 등록",
  "/layer/url/list": "웹레이어 목록조회",
  "/layer/url/select": "웹레이어 상세조회",
  "/layer/url/update": "웹레이어 수정",
  "/webapp/tmplat/auth/fullshare": "웹앱 템플릿 전체 공유 권한 체크",
  "/webmap/info/insert/process":
    "웹맵, 웹맵 레이어그룹, 웹맵 레이어설정, 웹맵 공유 일괄 저장",
  "/webmap/thumbnail/insert/body": "웹맵 썸네일 등록(RequestBody)",
  "/webapp/tmplat/insert/image-contents":
    "웹앱 템플릿 이미지 컨텐츠 등록(스토리/탭 테마)",
  "/webapp/tmplat/allShare/delete": "웹앱 전체 공유 삭제",
  "/webapp/tmplat/allShare/insert": "웹앱 전체 공유(개선 필요)",
  "/webapp/tmplat/auth": "웹앱 템플릿 권한 체크",
  "/webapp/tmplat/delete": "웹앱 템플릿 삭제",
  "/webapp/tmplat/insert": "웹앱 템플릿 등록 및 수정",
  "/webapp/tmplat/list": "웹앱 템플릿 목록 조회",
  "/webapp/tmplat/select": "웹앱 템플릿 상세 조회",
  "/storage/activation": "저장소 활성화 여부 수정(관리자 시스템 전용)",
  "/storage/detail": "저장소 상세 조회(관리자 시스템 전용)",
  "/table/publish": "테이블 레이어 발행(관리자 시스템 전용)",
  "/table/republish": "테이블 레이어 재발행(관리자 시스템 전용)",
  "/toc/group/delete": "TOC 그룹삭제",
  "/toc/group/insert": "TOC 그룹등록",
  "/toc/group/select": "TOC 그룹조회",
  "/toc/group/update": "TOC 그룹수정",
  "/toc/group/lry/id": "레이어 그룹 ID 생성",
  "/api/builds/build/heading/floor": "(Open-API) 건물층수 조회",
  "/api/builds/build/heading/ho": "(Open-API) 건물호수 조회",
  "/api/builds/build/register": "(Open-API) 대지권 등록 목록 조회",
  "/api/builds/build/serial": "(Open-API) 건물일련번호 조회",
  "/api/registry/floor": "층별개요 조회",
  "/api/registry/headings": "표제부 조회",
  "/api/registry/ownership": "전유부 조회",
  "/api/registry/general/headings": "총괄표제부 조회",
  "/api/land/basic": "(Open-API) 토지임야 목록 조회",
  "/api/land/characteristics": "(Open-API) 토지특성 속성 조회",
  "/api/land/history": "(Open-API) 토지이동이력 속성 조회",
  "/api/land/ownership": "(Open-API) 토지소유정보 속성 조회",
  "/api/land/useplan": "(Open-API) 토지이용계획 속성 조회",
  "/api/parcel/all": "(Open-API) 일필지 종합 정보 조회",
  "/api/price/house/apt": "(V-World) 공동주택가격 속성 조회",
  "/api/price/house/ind": "(V-World) 개별주택가격 속성 조회",
  "/api/price/pclnd": "(V-World) 개별공시지가 속성 조회",
  "/api/landbundle/search/address": "",
};

/* Snippets 시용해서 json 만들기


const STORAGE_KEY = 'swaggerApiDump';

// 기존 localStorage에서 불러오기
const existing = JSON.parse(localStorage.getItem(STORAGE_KEY) || '{}');

const newData = Array.from(document.querySelectorAll('.opblock-tag'))
  .map(groupEl => {
    if (groupEl.parentElement.querySelectorAll('.opblock').length === 0) {
      groupEl.click();
    }

    return {
      comment: groupEl.textContent.trim(),
      children: Array.from(groupEl.parentElement.querySelectorAll('.opblock')).map(el => ({
        comment: el.querySelector('.opblock-summary-description')?.innerHTML || '',
        method: el.querySelector('.opblock-summary-method')?.innerHTML || '',
        path: el.querySelector('.opblock-summary-path')?.childNodes[0]?.textContent.trim() || '',
      })),
    };
  })
  .sort((a, b) => a.comment.localeCompare(b.comment))
  .reduce((acc, group) => {
    const key1 = '_comment';
    const key2 = '';

    group.children
      .sort((a, b) => a.path.localeCompare(b.path))
      .forEach(child => {
        const paths = child.path.split('/');
        const [first, second] = paths.slice(paths.length - 2);
        if (!second) return;

        const _first = first;
        const _second = second.split('.')[0];

        acc[_first + key1] = group.comment;
        if (acc[_first + key2]) {
          acc[_first + key2][_second + key1] = child.comment.replaceAll('&gt;', '>');
          acc[_first + key2][_second + key2] = child.path;
        } else {
          acc[_first + key2] = {
            [_second + key1]: child.comment.replaceAll('&gt;', '>'),
            [_second + key2]: child.path,
          };
        }
      });

    return acc;
  }, {});

// 기존 데이터에 병합
const merged = { ...existing, ...newData };

// 저장
localStorage.setItem(STORAGE_KEY, JSON.stringify(merged));
console.log('✅ 누적 저장 완료:', merged);

*/

//URL을 이용해 URL JSON 만들기.(comment용)
/*
    export const urlJsonFn = () => {
    //temp1 에는 url 입력
    const temp1 = JSON.parse(localStorage.getItem("swaggerApiDump") || "[]");
    const urlJson = {};
    const url1 = Object.keys(temp1).filter((e) => {
        if (e.indexOf("_comment") == -1) {
        return e;
        }
    });
    url1.forEach((e) => {
        const obj = Object.keys(temp1[e]).filter((e) => {
        if (e.indexOf("_comment") == -1) {
            return e;
        }
        });
        obj.forEach((j) => {
        urlJson[temp1[e][j]] = temp1[e][j + "_comment"];
        });
    });
    };
 */
