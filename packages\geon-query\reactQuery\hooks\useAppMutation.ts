'use client';

import {
  useMutation,
  UseMutationOptions,
  UseMutationResult,
} from '@tanstack/react-query';

export function useAppMutation<
  TData = unknown,
  TVariables = void,
  TError = unknown,
  TContext = unknown
>(
  options: UseMutationOptions<TData, TError, TVariables, TContext>
): UseMutationResult<TData, TError, TVariables, TContext> {
  return useMutation({
    retry: 1,
    ...options,
  });
}
