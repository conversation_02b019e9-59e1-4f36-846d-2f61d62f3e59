import { useQuery, UseQueryOptions, UseQueryResult } from '@tanstack/react-query';

type AppQueryOptions<TData = unknown, TError = unknown> = Omit<
  UseQueryOptions<TData, TError>,
  'queryKey' | 'queryFn'
> & {
  queryKey: UseQueryOptions<TData, TError>['queryKey'];
  queryFn: UseQueryOptions<TData, TError>['queryFn'];
};

export function useAppQuery<TData = unknown, TError = unknown>(
  options: AppQueryOptions<TData, TError>
): UseQueryResult<TData, TError> {
  return useQuery<TData, TError>({
    staleTime: 5 * 60 * 1000, // 공통 기본 설정 (5분)
    retry: 1,
    ...options,
  });
}
