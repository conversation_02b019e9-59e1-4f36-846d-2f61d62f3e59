import { cn } from "@geon-ui/react/lib/utils";

import { Input } from "./input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "./select";

export interface SelectInputProps extends React.ComponentProps<typeof Select> {
  triggerClassName?: React.ComponentProps<typeof SelectTrigger>["className"];
  items: string[];
  side?: React.ComponentProps<typeof SelectContent>["side"];
  inputClassName?: React.ComponentProps<typeof Input>["className"];
  inputValue?: React.ComponentProps<typeof Input>["value"];
  inputPlaceholder?: React.ComponentProps<typeof Input>["placeholder"];
  inputChange?: React.ComponentProps<typeof Input>["onChange"];
}

export default function SelectInput({
  triggerClassName,
  items,
  side,
  value,
  onValueChange,
  inputClassName,
  inputPlaceholder,
  inputValue,
  inputChange,
  ...props
}: SelectInputProps) {
  return (
    <div className="flex items-center">
      <Select value={value} onValueChange={onValueChange} {...props}>
        <SelectTrigger
          className={cn(
            "h-8 w-[150px] rounded-r-none border-r-0",
            triggerClassName,
          )}
        >
          <SelectValue placeholder={value || "선택"} />
        </SelectTrigger>
        <SelectContent side={side}>
          {items.map((item, index) => (
            <SelectItem key={`select-item-${index}`} value={item}>
              {item}
            </SelectItem>
          ))}
        </SelectContent>
      </Select>
      <Input
        disabled={!value}
        placeholder={inputPlaceholder}
        value={inputValue}
        onChange={inputChange}
        className={cn("max-w-sm rounded-l-none", inputClassName)}
      />
    </div>
  );
}
