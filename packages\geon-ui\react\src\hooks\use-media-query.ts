"use client";

import { useEffect, useState } from "react";

/**
 * 컴포넌트 내에서 media query 를 사용하는 hook
 * @param query 화면 크기를 확인할 media query
 *
 * `min-width: 1024px` --> 1024 픽셀 이상
 *
 * `max-width: 1440px` --> 1440 픽셀 이하
 * @example
 * ```tsx
 * import { useMediaQuery } from "@geon-ui/react/hooks/use-media-query.jsx";
 *
 * export default function Component() {
 *   const isLargeScreen = useMediaQuery("min-width: 1024px");
 *
 *   return isLargeScreen ? <LargeScreenComponent /> : <MobileScreenComponent />;
 * }
 *
 * ```
 */
export function useMediaQuery(query: string) {
  const [isMatch, setIsMatch] = useState<boolean | undefined>();

  useEffect(() => {
    const mql = window.matchMedia(`(${query})`);
    const onChange = () => setIsMatch(mql.matches);
    mql.addEventListener("change", onChange);
    setIsMatch(mql.matches);

    return () => mql.removeEventListener("change", onChange);
  }, [query]);

  return !!isMatch;
}
