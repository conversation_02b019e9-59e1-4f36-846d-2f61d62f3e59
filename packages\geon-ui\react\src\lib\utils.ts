import { type ClassValue, clsx } from "clsx";
import { twMerge } from "tailwind-merge";

/**
 * Tailwindcss 의 className 을 합쳐주는 utility 함수.
 * 중복되는 속성은 뒤에 오는 className 이 덮어씁니다.
 *
 * @example
 * ```tsx
 * function Component({ className }: ComponentProps) {
 *   return (
 *     <div className={cn("p-5", className)} />
 *   );
 * }
 *
 * <Component className="p-10" />
 * // This equals to:
 * <div className="p-10" />
 * ```
 */
export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs));
}
