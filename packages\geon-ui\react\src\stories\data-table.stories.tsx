import type { <PERSON><PERSON>, <PERSON><PERSON>b<PERSON> } from "@storybook/react-webpack5";
import type { ColumnDef } from "@tanstack/react-table";
import { MoreHorizontal } from "lucide-react";

import { Button } from "../components/primitives/button";
import { Checkbox } from "../components/primitives/checkbox";
import type { DataTableProps } from "../components/primitives/data-table";
import {
  DataTable,
  DataTableColumnHeader,
} from "../components/primitives/data-table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "../components/primitives/dropdown-menu";

// This type is used to define the shape of our data.
// You can use a Zod schema here if you want.
type Payment = {
  id: string;
  amount: number;
  status: "pending" | "processing" | "success" | "failed";
  email: string;
};

const data: Payment[] = [
  {
    id: "728ed52f",
    amount: 100,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "489e1d42",
    amount: 125,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "3f7a9b2c",
    amount: 250,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "8d4e5f6a",
    amount: 75,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "b1c2d3e4",
    amount: 300,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "f5g6h7i8",
    amount: 150,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "j9k0l1m2",
    amount: 200,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "n3o4p5q6",
    amount: 450,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "r7s8t9u0",
    amount: 90,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "v1w2x3y4",
    amount: 175,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "z5a6b7c8",
    amount: 320,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "d9e0f1g2",
    amount: 110,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "h3i4j5k6",
    amount: 275,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "l7m8n9o0",
    amount: 85,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "p1q2r3s4",
    amount: 190,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "t5u6v7w8",
    amount: 400,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "x9y0z1a2",
    amount: 95,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "b3c4d5e6",
    amount: 225,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "f7g8h9i0",
    amount: 130,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "j1k2l3m4",
    amount: 350,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "n5o6p7q8",
    amount: 165,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "r9s0t1u2",
    amount: 280,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "v3w4x5y6",
    amount: 120,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "z7a8b9c0",
    amount: 210,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "d1e2f3g4",
    amount: 180,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "h5i6j7k8",
    amount: 290,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "l9m0n1o2",
    amount: 155,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "p3q4r5s6",
    amount: 240,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "t7u8v9w0",
    amount: 105,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "x1y2z3a4",
    amount: 330,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "b5c6d7e8",
    amount: 195,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "f9g0h1i2",
    amount: 260,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "j3k4l5m6",
    amount: 140,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "n7o8p9q0",
    amount: 310,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "r1s2t3u4",
    amount: 170,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "v5w6x7y8",
    amount: 385,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "z9a0b1c2",
    amount: 115,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "d3e4f5g6",
    amount: 255,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "h7i8j9k0",
    amount: 125,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "l1m2n3o4",
    amount: 295,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "p5q6r7s8",
    amount: 185,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "t9u0v1w2",
    amount: 220,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "x3y4z5a6",
    amount: 145,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "b7c8d9e0",
    amount: 365,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "f1g2h3i4",
    amount: 160,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "j5k6l7m8",
    amount: 285,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "n9o0p1q2",
    amount: 135,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "r3s4t5u6",
    amount: 270,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "v7w8x9y0",
    amount: 110,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "z1a2b3c4",
    amount: 340,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "d5e6f7g8",
    amount: 205,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "h9i0j1k2",
    amount: 245,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "l3m4n5o6",
    amount: 125,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "p7q8r9s0",
    amount: 315,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "t1u2v3w4",
    amount: 175,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "x5y6z7a8",
    amount: 395,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "b9c0d1e2",
    amount: 90,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "f3g4h5i6",
    amount: 265,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "j7k8l9m0",
    amount: 150,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "n1o2p3q4",
    amount: 325,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "r5s6t7u8",
    amount: 190,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "v9w0x1y2",
    amount: 230,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "z3a4b5c6",
    amount: 115,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "d7e8f9g0",
    amount: 355,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "h1i2j3k4",
    amount: 165,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "l5m6n7o8",
    amount: 275,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "p9q0r1s2",
    amount: 140,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "t3u4v5w6",
    amount: 300,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "x7y8z9a0",
    amount: 120,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "b1c2d3e4",
    amount: 380,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "f5g6h7i8",
    amount: 195,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "j9k0l1m2",
    amount: 250,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "n3o4p5q6",
    amount: 135,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "r7s8t9u0",
    amount: 290,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "v1w2x3y4",
    amount: 155,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "z5a6b7c8",
    amount: 335,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "d9e0f1g2",
    amount: 185,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "h3i4j5k6",
    amount: 215,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "l7m8n9o0",
    amount: 145,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "p1q2r3s4",
    amount: 370,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "t5u6v7w8",
    amount: 200,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "x9y0z1a2",
    amount: 260,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "b3c4d5e6",
    amount: 130,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "f7g8h9i0",
    amount: 305,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "j1k2l3m4",
    amount: 170,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "n5o6p7q8",
    amount: 285,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "r9s0t1u2",
    amount: 115,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "v3w4x5y6",
    amount: 345,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "z7a8b9c0",
    amount: 180,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "d1e2f3g4",
    amount: 235,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "h5i6j7k8",
    amount: 125,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "l9m0n1o2",
    amount: 315,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "p3q4r5s6",
    amount: 160,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "t7u8v9w0",
    amount: 275,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "x1y2z3a4",
    amount: 140,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "b5c6d7e8",
    amount: 350,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "f9g0h1i2",
    amount: 195,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "j3k4l5m6",
    amount: 220,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "n7o8p9q0",
    amount: 105,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "r1s2t3u4",
    amount: 385,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "v5w6x7y8",
    amount: 175,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "z9a0b1c2",
    amount: 295,
    status: "processing",
    email: "<EMAIL>",
  },
  {
    id: "d3e4f5g6",
    amount: 150,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "h7i8j9k0",
    amount: 325,
    status: "failed",
    email: "<EMAIL>",
  },
  {
    id: "l1m2n3o4",
    amount: 185,
    status: "pending",
    email: "<EMAIL>",
  },
  {
    id: "p5q6r7s8",
    amount: 240,
    status: "success",
    email: "<EMAIL>",
  },
  {
    id: "t9u0v1w2",
    amount: 165,
    status: "processing",
    email: "<EMAIL>",
  },
];

const columns: ColumnDef<Payment>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={
          table.getIsAllPageRowsSelected() ||
          (table.getIsSomePageRowsSelected() && "indeterminate")
        }
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
    enableColumnFilter: false,
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
  },
  {
    accessorKey: "email",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Email" />
    ),
  },
  {
    accessorKey: "amount",
    header: () => <div className="text-right">Amount</div>,
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("amount"));
      const formatted = new Intl.NumberFormat("en-US", {
        style: "currency",
        currency: "USD",
      }).format(amount);

      return <div className="text-right font-medium">{formatted}</div>;
    },
    enableColumnFilter: false,
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const payment = row.original;

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(payment.id)}
            >
              Copy payment ID
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem>View customer</DropdownMenuItem>
            <DropdownMenuItem>View payment details</DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      );
    },
    enablePinning: true,
  },
];

/**
 * parameters.docs.description.component 값을 여기에 입력할 수 있습니다.
 */
const meta = {
  title: "Shadcn/DataTable",
  component: DataTable,
  /**
   * Default Component Props 를 설정합니다.
   * @see {@link https://storybook.js.org/docs/writing-stories/args}
   */
  args: {
    columns,
    data,
    filter: true,
    pagination: true,
  },
  argTypes: {},
  /**
   * 컴포넌트를 감싸는 Wrapper 를 설정합니다.
   * @see {@link https://storybook.js.org/docs/writing-stories/decorators}
   */
  decorators: [],
  /** @see {@link https://storybook.js.org/docs/writing-stories/parameters} */
  parameters: {
    docs: {
      subtitle: "Powerful table and datagrids built using TanStack Table.",
      codePanel: true, // 컴포넌트의 코드 패널 표시 여부
      source: {
        language: "tsx", // 컴포넌트 구성 언어 (syntax highlight)
        code: undefined, // 컴포넌트 구성 코드를 직접 입력하여 제공
      },
      description: {
        component:
          "정렬, 행 선택, 필터, 표시 열 선택, pagination, actions 등의 기능이 포함된 데이터 표시를 위한 테이블 컴포넌트입니다.",
      },
    },
    /**
     * 컴포넌트의 표시 위치를 결정합니다. `"padded" |  "centered" | "fullscreen"`
     * @default "padded"
     */
    layout: "fullscreen",
    backgrounds: {
      options: {},
    },
  },
  /**
   * 스토리에 달릴 tag 를 관리합니다.
   *
   * `"autodocs"`는 document 자동 생성 태그입니다.
   * @see {@link https://storybook.js.org/docs/writing-stories/tags}
   */
  tags: ["autodocs"],
  /**
   * Data 를 fetch 할 API 를 설정합니다.
   * @see {@link https://storybook.js.org/docs/writing-stories/loaders}
   */
  loaders: [],
  /**
   * @see {@link https://storybook.js.org/docs/writing-stories/play-function}
   */
  play: undefined,
  render: (args) => (
    <div className="w-full p-10">
      <DataTable {...args} />
    </div>
  ),
} satisfies Meta<DataTableProps<Payment, unknown>>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  // You can Override "meta" here
};
